version: '3.8'

# PaaS 平台本地开发环境 Docker Compose 配置
# 专为单台服务器开发优化，无需镜像仓库

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres-local
    environment:
      POSTGRES_DB: paas_platform
      POSTGRES_USER: paas
      POSTGRES_PASSWORD: paas123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - paas-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas -d paas_platform"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: paas-redis-local
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - paas-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Gitea 代码仓库（可选，用于测试 CI/CD）
  gitea:
    image: gitea/gitea:1.20
    container_name: paas-gitea-local
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=gitea
      - GITEA__database__USER=paas
      - GITEA__database__PASSWD=paas123
    restart: always
    ports:
      - "3000:3000"
      - "2222:22"
    volumes:
      - gitea_data:/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - full  # 使用 --profile full 启动完整环境

  # API Gateway (网关服务)
  api-gateway:
    build:
      context: .
      dockerfile: deployments/docker/api-gateway.Dockerfile
    container_name: paas-api-gateway-local
    ports:
      - "8080:8080"
    environment:
      - SERVER_PORT=8080
      - LOG_LEVEL=debug
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
      - DOCKER_REGISTRY=local  # 本地构建模式
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # App Manager (应用管理服务)
  app-manager:
    build:
      context: .
      dockerfile: deployments/docker/app-manager.Dockerfile
    container_name: paas-app-manager-local
    ports:
      - "8081:8081"
    environment:
      - SERVER_PORT=8081
      - LOG_LEVEL=debug
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_REGISTRY=local  # 本地构建模式
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock  # Docker socket
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # CI/CD Service (持续集成服务) - 本地开发模式
  cicd-service:
    build:
      context: .
      dockerfile: deployments/docker/cicd-service.Dockerfile
    container_name: paas-cicd-service-local
    ports:
      - "8082:8082"
    environment:
      - SERVER_PORT=8082
      - LOG_LEVEL=debug
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
      - GITEA_URL=http://gitea:3000
      - DOCKER_REGISTRY=local  # 本地构建模式，不推送到仓库
      - BUILD_MODE=local       # 本地构建模式
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # User Service (用户服务)
  user-service:
    build:
      context: .
      dockerfile: deployments/docker/user-service.Dockerfile
    container_name: paas-user-service-local
    ports:
      - "8083:8083"
    environment:
      - SERVER_PORT=8083
      - LOG_LEVEL=debug
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
      - JWT_SECRET=your-jwt-secret-key
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Config Service (配置服务)
  config-service:
    build:
      context: .
      dockerfile: deployments/docker/config-service.Dockerfile
    container_name: paas-config-service-local
    ports:
      - "8084:8084"
    environment:
      - SERVER_PORT=8084
      - LOG_LEVEL=debug
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  gitea_data:
    driver: local

# 网络
networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
