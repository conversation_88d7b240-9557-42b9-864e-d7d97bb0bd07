# =============================================================================
# PaaS 平台项目 .gitignore 文件
# 包含 Go、Vue.js、<PERSON><PERSON>、数据库等相关忽略规则
# =============================================================================

# =============================================================================
# Go 语言相关
# =============================================================================

# 编译后的二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# Go 服务二进制文件（根目录）
/api-gateway
/app-manager
/cicd-service
/config-service
/monitor-service
/user-service

# Go 服务二进制文件（cmd 目录下）
/cmd/*/cmd
/cmd/api-gateway/api-gateway
/cmd/app-manager/app-manager
/cmd/cicd-service/cicd-service
/cmd/config-service/config-service
/cmd/monitor-service/monitor-service
/cmd/user-service/user-service

# 根目录下的二进制文件（更精确的匹配）
/api-gateway
/app-manager
/cicd-service
/config-service
/monitor-service
/user-service
/script-service

# bin 目录下的所有二进制文件
/bin/*
!bin/.gitkeep

# 注意：移除了过于宽泛的规则，避免误匹配源代码目录
# 使用 / 前缀确保只匹配特定路径的文件，不影响子目录中的源代码

# 测试二进制文件，使用 go test -c 构建
*.test

# 输出的覆盖率文件
*.out
coverage.txt
coverage.html

# Go 依赖管理
vendor/
go.work
go.work.sum

# Go 模块代理缓存
GOPROXY
GOSUMDB

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# 前端 Vue.js 相关 (web 目录)
# =============================================================================

# 依赖目录
web/node_modules/
web/.pnp
web/.pnp.js

# 生产构建
web/dist/
web/build/

# 本地环境变量文件
web/.env.local
web/.env.development.local
web/.env.test.local
web/.env.production.local

# 日志文件
web/npm-debug.log*
web/yarn-debug.log*
web/yarn-error.log*
web/pnpm-debug.log*
web/lerna-debug.log*

# 运行时数据
web/pids
web/*.pid
web/*.seed
web/*.pid.lock

# 覆盖率目录
web/coverage/
web/*.lcov

# nyc 测试覆盖率
web/.nyc_output

# Grunt 中间存储
web/.grunt

# Bower 依赖目录
web/bower_components

# node-waf 配置
web/.lock-wscript

# 编译的二进制插件
web/build/Release

# TypeScript 缓存
web/*.tsbuildinfo

# 可选的 npm 缓存目录
web/.npm

# 可选的 eslint 缓存
web/.eslintcache

# Microbundle 缓存
web/.rpt2_cache/
web/.rts2_cache_cjs/
web/.rts2_cache_es/
web/.rts2_cache_umd/

# 可选的 REPL 历史
web/.node_repl_history

# Yarn Integrity 文件
web/.yarn-integrity

# parcel-bundler 缓存
web/.cache
web/.parcel-cache

# Next.js 构建输出
web/.next

# Nuxt.js 构建/生成输出
web/.nuxt
web/dist

# Gatsby 文件
web/.cache/
web/public

# Storybook 构建输出
web/.out
web/.storybook-out

# Temporary 文件夹
web/.tmp
web/.temp

# =============================================================================
# 数据库相关
# =============================================================================

# SQLite 数据库文件
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite
data/*.sqlite3

# PostgreSQL 数据文件
*.pgsql
*.sql.backup

# 数据库迁移临时文件
migrations/*.tmp

# =============================================================================
# Docker 相关
# =============================================================================

# Docker 构建上下文中的临时文件
.dockerignore.bak
docker-compose.override.yml

# =============================================================================
# 配置和敏感信息
# =============================================================================

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# 配置文件备份
configs/*.bak
configs/*.backup
configs/*.orig

# SSL 证书和密钥
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# JWT 密钥文件
jwt-*.key
*.jwt

# =============================================================================
# 日志文件
# =============================================================================

# 应用日志
logs/
*.log
log/
app.log
error.log
access.log
debug.log

# 系统日志
/var/log/

# =============================================================================
# 存储和缓存
# =============================================================================

# 本地存储目录
data/storage/
storage/
uploads/
temp/
tmp/

# 缓存目录（排除源代码目录）
/cache/
.cache/
data/cache/
tmp/cache/
temp/cache/

# Redis 持久化文件
dump.rdb
appendonly.aof

# =============================================================================
# 构建和部署相关
# =============================================================================

# 构建产物
build/
dist/
out/

# 部署脚本生成的文件
deploy/*.generated
k8s/*.generated.yaml
helm/*/charts/
helm/*/Chart.lock

# Terraform 文件
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =============================================================================
# 开发工具和临时文件
# =============================================================================

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器临时文件
*~
.#*
\#*#
.*.swp
.*.swo

# 测试相关
test-results/
coverage/
.coverage
htmlcov/

# 性能分析文件
*.prof
*.pprof

# 调试文件
debug
debug.test

# 备份文件
*.bak
*.backup
*.orig

# 压缩文件
*.tar.gz
*.zip
*.rar
*.7z

# =============================================================================
# 项目特定忽略
# =============================================================================

# PaaS 平台特定的临时文件
/data/apps/
/data/builds/
/data/deployments/
/data/repositories/

# 监控数据
/data/metrics/
/data/logs/

# 证书存储
/data/certs/

# 备份目录
/backups/

# 开发时的测试数据
/test-data/
/mock-data/
