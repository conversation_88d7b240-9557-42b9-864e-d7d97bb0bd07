#!/bin/bash
# 代码库优化回滚脚本
# 功能：将代码库恢复到优化前的状态
# 作者：PaaS平台开发团队
# 版本：1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ROLLBACK_LOG="$PROJECT_ROOT/rollback-$(date +%Y%m%d-%H%M%S).log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

# 显示帮助信息
show_help() {
    cat << EOF
代码库优化回滚脚本

用法: $0 <备份目录路径> [选项]

参数:
  备份目录路径        由 optimize-codebase.sh 创建的备份目录

选项:
  -h, --help          显示此帮助信息
  -f, --force         强制回滚，不进行确认
  -v, --verbose       详细输出模式
  --dry-run           预览模式，不实际执行操作

示例:
  $0 backup-20250821-143022                    # 从指定备份回滚
  $0 backup-20250821-143022 --force            # 强制回滚
  $0 backup-20250821-143022 --dry-run          # 预览回滚操作
EOF
}

# 解析命令行参数
BACKUP_DIR=""
FORCE=false
VERBOSE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$BACKUP_DIR" ]]; then
                BACKUP_DIR="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 验证备份目录
validate_backup_directory() {
    if [[ -z "$BACKUP_DIR" ]]; then
        log_error "请提供备份目录路径"
        show_help
        exit 1
    fi
    
    # 如果是相对路径，转换为绝对路径
    if [[ ! "$BACKUP_DIR" =~ ^/ ]]; then
        BACKUP_DIR="$PROJECT_ROOT/$BACKUP_DIR"
    fi
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    log_info "使用备份目录: $BACKUP_DIR"
    
    # 检查备份目录内容
    local backup_files=$(ls -la "$BACKUP_DIR" 2>/dev/null | wc -l)
    if [[ $backup_files -lt 3 ]]; then  # 至少应该有 . .. 和一些文件
        log_warning "备份目录似乎是空的或内容很少"
        if [[ "$FORCE" != "true" ]]; then
            read -p "是否继续？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "回滚已取消"
                exit 0
            fi
        fi
    fi
}

# 显示备份信息
show_backup_info() {
    log_info "备份目录信息:"
    log_info "路径: $BACKUP_DIR"
    log_info "创建时间: $(stat -c %y "$BACKUP_DIR" 2>/dev/null || stat -f %Sm "$BACKUP_DIR" 2>/dev/null || echo "未知")"
    log_info "大小: $(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1 || echo "未知")"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "备份内容:"
        ls -la "$BACKUP_DIR" | while read line; do
            log_info "  $line"
        done
    fi
}

# 确认回滚操作
confirm_rollback() {
    if [[ "$FORCE" == "true" ]] || [[ "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    echo -e "\n${YELLOW}⚠️ 警告：回滚操作将覆盖当前的文件和目录${NC}"
    echo -e "${YELLOW}这将撤销所有优化操作，包括：${NC}"
    echo -e "  - 恢复已删除的README文件"
    echo -e "  - 恢复已归档的脚本"
    echo -e "  - 删除新创建的目录结构"
    echo -e "  - 恢复修复报告到原位置"
    echo
    
    read -p "确定要继续回滚吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "回滚已取消"
        exit 0
    fi
}

# 创建当前状态备份
create_current_backup() {
    local current_backup="$PROJECT_ROOT/pre-rollback-backup-$(date +%Y%m%d-%H%M%S)"
    
    log_info "创建当前状态备份: $current_backup"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将创建当前状态备份: $current_backup"
        return 0
    fi
    
    mkdir -p "$current_backup"
    
    # 备份当前重要文件
    local backup_items=(
        "*.md"
        "scripts/"
        "docs/"
        "docker-compose*.yml"
    )
    
    for item in "${backup_items[@]}"; do
        if ls $item 1> /dev/null 2>&1; then
            cp -r $item "$current_backup/" 2>/dev/null || true
            [[ "$VERBOSE" == "true" ]] && log_info "已备份当前状态: $item"
        fi
    done
    
    log_success "当前状态备份完成: $current_backup"
    echo "CURRENT_BACKUP=$current_backup" >> "$ROLLBACK_LOG"
}

# 清理优化后的目录结构
cleanup_optimization_artifacts() {
    log_info "清理优化后的目录结构..."
    
    local cleanup_dirs=(
        "docs/guides"
        "docs/历史修复记录"
        "scripts/archived"
    )
    
    for dir in "${cleanup_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                log_info "[预览] 将删除目录: $dir"
            else
                rm -rf "$dir"
                log_success "已删除目录: $dir"
            fi
        fi
    done
}

# 恢复文件
restore_files() {
    log_info "从备份恢复文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将从 $BACKUP_DIR 恢复所有文件"
        log_info "[预览] 恢复的文件包括:"
        find "$BACKUP_DIR" -type f | while read file; do
            local relative_path="${file#$BACKUP_DIR/}"
            log_info "[预览]   $relative_path"
        done
        return 0
    fi
    
    # 恢复所有备份文件
    cd "$BACKUP_DIR"
    
    # 使用 find 和 cp 来保持目录结构
    find . -type f | while read file; do
        local target_file="$PROJECT_ROOT/${file#./}"
        local target_dir="$(dirname "$target_file")"
        
        # 创建目标目录（如果不存在）
        mkdir -p "$target_dir"
        
        # 复制文件
        cp "$file" "$target_file"
        
        [[ "$VERBOSE" == "true" ]] && log_info "已恢复: ${file#./}"
    done
    
    # 恢复目录结构
    find . -type d | while read dir; do
        local target_dir="$PROJECT_ROOT/${dir#./}"
        if [[ "$dir" != "." ]]; then
            mkdir -p "$target_dir"
            [[ "$VERBOSE" == "true" ]] && log_info "已恢复目录: ${dir#./}"
        fi
    done
    
    cd "$PROJECT_ROOT"
    log_success "文件恢复完成"
}

# 验证回滚结果
verify_rollback() {
    log_info "验证回滚结果..."
    
    local verification_items=(
        "README.md:主README文件"
        "README-CICD.md:CI/CD README文件"
        "README-DEV-AUTH.md:开发认证README文件"
        "README-IDP.md:IDP README文件"
    )
    
    local success_count=0
    local total_count=${#verification_items[@]}
    
    for item in "${verification_items[@]}"; do
        local file="${item%:*}"
        local description="${item#*:}"
        
        if [[ -f "$file" ]]; then
            log_success "验证通过: $description 已恢复"
            ((success_count++))
        else
            log_warning "验证失败: $description 未找到"
        fi
    done
    
    log_info "验证结果: $success_count/$total_count 项通过"
    
    if [[ $success_count -eq $total_count ]]; then
        log_success "回滚验证完全通过"
        return 0
    else
        log_warning "回滚验证部分失败，请手动检查"
        return 1
    fi
}

# 生成回滚报告
generate_rollback_report() {
    log_info "生成回滚报告..."
    
    local report_file="rollback-report-$(date +%Y%m%d-%H%M%S).md"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将生成回滚报告: $report_file"
        return 0
    fi
    
    cat > "$report_file" << EOF
# 代码库优化回滚报告

**执行时间**: $(date)  
**备份来源**: $BACKUP_DIR  
**执行模式**: $([[ "$DRY_RUN" == "true" ]] && echo "预览模式" || echo "实际执行")

## 回滚操作

### 1. 恢复的文件
- ✅ README-CICD.md
- ✅ README-DEV-AUTH.md  
- ✅ README-IDP.md
- ✅ 原始脚本文件
- ✅ 修复报告文档

### 2. 清理的目录
- ✅ docs/guides/
- ✅ docs/历史修复记录/
- ✅ scripts/archived/

### 3. 验证结果
$(verify_rollback > /dev/null 2>&1 && echo "✅ 回滚验证通过" || echo "⚠️ 回滚验证部分失败")

## 后续建议

1. **功能测试**: 验证所有脚本和配置文件功能正常
2. **文档检查**: 确认所有文档链接有效
3. **重新优化**: 如需要，可以重新运行优化脚本

## 备份信息

- **原始备份**: $BACKUP_DIR
- **回滚前备份**: $(grep "CURRENT_BACKUP=" "$ROLLBACK_LOG" 2>/dev/null | cut -d= -f2 || echo "未创建")

---
**回滚脚本版本**: 1.0.0  
**执行状态**: ✅ 回滚完成
EOF
    
    log_success "回滚报告已生成: $report_file"
}

# 主函数
main() {
    echo -e "${CYAN}🔄 PaaS平台代码库优化回滚工具${NC}"
    echo -e "${CYAN}====================================${NC}"
    
    # 初始化日志
    echo "回滚开始时间: $(date)" > "$ROLLBACK_LOG"
    echo "项目根目录: $PROJECT_ROOT" >> "$ROLLBACK_LOG"
    
    # 验证备份目录
    validate_backup_directory
    
    # 显示备份信息
    show_backup_info
    
    # 确认回滚操作
    confirm_rollback
    
    # 创建当前状态备份
    create_current_backup
    
    # 执行回滚操作
    cleanup_optimization_artifacts
    restore_files
    
    # 验证回滚结果
    if ! verify_rollback; then
        log_warning "回滚验证发现问题，请手动检查"
    fi
    
    # 生成回滚报告
    generate_rollback_report
    
    echo -e "\n${GREEN}🎉 回滚操作完成！${NC}"
    echo -e "${BLUE}📝 回滚日志: $ROLLBACK_LOG${NC}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${CYAN}💡 这是预览模式，没有实际执行操作${NC}"
        echo -e "${CYAN}💡 要执行实际回滚，请运行: $0 $BACKUP_DIR${NC}"
    else
        echo -e "${YELLOW}📋 建议运行功能测试验证回滚结果${NC}"
    fi
}

# 执行主函数
main "$@"
