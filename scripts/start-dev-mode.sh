#!/bin/bash

# PaaS平台开发环境启动脚本
# 此脚本用于在开发环境中启动所有服务，并自动配置认证跳过
# ⚠️ 警告：此脚本仅适用于开发环境，绝不能在生产环境使用！

set -e  # 遇到错误时退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 服务配置
declare -A SERVICES=(
    ["user-service"]="8083"
    ["api-gateway"]="8080"
    ["app-manager"]="8081"
    ["cicd-service"]="8082"
    ["config-service"]="8084"
    ["script-service"]="8085"
    ["faas-service"]="8087"
)

# 显示开发模式警告
show_dev_warning() {
    echo -e "${RED}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${RED}║                    ⚠️  开发模式警告 ⚠️                      ║${NC}"
    echo -e "${RED}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${RED}║  🔓 用户认证已禁用 - 所有API请求将跳过身份验证              ║${NC}"
    echo -e "${RED}║  🧑‍💻 使用默认开发用户身份 (admin权限)                      ║${NC}"
    echo -e "${RED}║  🚨 此配置仅适用于开发环境，请勿在生产环境使用！            ║${NC}"
    echo -e "${RED}║  📝 如需启用认证，请设置环境变量 PAAS_AUTH_ENABLED=true     ║${NC}"
    echo -e "${RED}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 显示帮助信息
show_help() {
    cat << EOF
PaaS 平台开发环境启动脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -s, --services SERVICES 指定要启动的服务 (逗号分隔)
    -p, --parallel          并行启动服务
    -c, --clean             清理数据后启动
    -v, --verbose           详细输出
    --skip-build            跳过构建步骤
    --skip-deps             跳过依赖检查
    --config-only           仅生成配置文件

可用服务:
$(for service in "${!SERVICES[@]}"; do echo "    - $service (端口: ${SERVICES[$service]})"; done)

示例:
    $0                              # 启动所有服务
    $0 -s user-service,api-gateway  # 仅启动指定服务
    $0 -p                           # 并行启动所有服务
    $0 -c                           # 清理数据后启动
    $0 --config-only                # 仅生成配置文件

EOF
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."

    local missing_tools=()

    # 检查Go
    if ! command -v go &> /dev/null; then
        missing_tools+=("go")
    else
        local go_version=$(go version | awk '{print $3}' | sed 's/go//')
        log_debug "Go 版本: $go_version"
        if [[ "$(printf '%s\n' "1.21" "$go_version" | sort -V | head -n1)" != "1.21" ]]; then
            log_error "Go 版本过低，需要 1.21+，当前版本: $go_version"
            exit 1
        fi
    fi

    # 检查必要的命令行工具
    local tools=("curl" "lsof" "netstat")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重试"
        exit 1
    fi

    # 检查Docker (可选)
    if ! command -v docker &> /dev/null; then
        log_warn "Docker未安装，脚本服务和FaaS服务可能无法正常工作"
    else
        # 检查Docker是否运行
        if ! docker info &> /dev/null 2>&1; then
            log_warn "Docker未运行，脚本服务和FaaS服务可能无法正常工作"
        else
            log_debug "Docker 已安装并运行"
        fi
    fi

    # 检查Redis (可选)
    if ! command -v redis-cli &> /dev/null; then
        log_warn "Redis未安装，缓存功能可能受限"
    else
        log_debug "Redis 客户端已安装"
    fi

    # 检查PostgreSQL客户端 (可选)
    if ! command -v psql &> /dev/null; then
        log_warn "PostgreSQL客户端未安装，将使用SQLite"
    else
        log_debug "PostgreSQL 客户端已安装"
    fi

    log_info "工具检查完成"
}

# 设置开发环境变量
setup_dev_env() {
    log_info "设置开发环境变量..."

    # 导出开发环境变量
    export ENV=development
    export NODE_ENV=development
    export GO_ENV=development
    export DEBUG=true
    export GIN_MODE=debug

    # 🔓 认证配置 - 开发环境跳过认证
    export PAAS_AUTH_ENABLED=false
    export PAAS_DEV_MODE=true
    export PAAS_DEV_TOKEN=dev-token-2024-secure
    export PAAS_JWT_SECRET=dev-jwt-secret-key-not-for-production

    # 开发用户信息
    export PAAS_DEV_USER_ID=dev-user-001
    export PAAS_DEV_USER_TENANT_ID=dev-tenant-001
    export PAAS_DEV_USER_USERNAME=开发者
    export PAAS_DEV_USER_EMAIL=developer@localhost
    export PAAS_DEV_USER_ROLES=admin,developer,script_executor,cicd_manager,config_manager

    # 显示警告
    export SHOW_DEV_MODE_WARNING=true
    export DEV_MODE_BANNER="🔓 开发模式已启用 - 认证已跳过"

    # 数据库配置
    export DATABASE_DRIVER=sqlite
    export DB_DRIVER=sqlite
    export DB_LOG_LEVEL=info

    # 日志配置
    export LOG_LEVEL=debug
    export LOG_FORMAT=text
    export LOG_OUTPUT=stdout

    # Redis配置
    export REDIS_ADDR=localhost:6379
    export REDIS_PASSWORD=
    export REDIS_DB=0

    # Docker配置
    export DOCKER_HOST=unix:///var/run/docker.sock
    export DOCKER_API_VERSION=1.41

    # 监控配置
    export METRICS_ENABLED=true
    export HEALTH_CHECK_INTERVAL=10s
    export PPROF_ENABLED=true

    # 服务端口配置
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local env_var="$(echo "$service" | tr '[:lower:]' '[:upper:]' | tr '-' '_')_PORT"
        export "$env_var"="$port"
    done

    log_info "环境变量设置完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    local directories=(
        "bin"
        "data"
        "data/storage"
        "data/workspaces"
        "data/artifacts"
        "data/templates"
        "data/configs"
        "data/faas"
        "data/faas/workspaces"
        "data/faas/artifacts"
        "logs"
        "tmp"
        "configs"
    )

    for dir in "${directories[@]}"; do
        if [[ ! -d "$PROJECT_ROOT/$dir" ]]; then
            mkdir -p "$PROJECT_ROOT/$dir"
            log_debug "创建目录: $dir"
        fi
    done

    # 设置权限
    chmod 755 "$PROJECT_ROOT/data" "$PROJECT_ROOT/logs" "$PROJECT_ROOT/tmp" 2>/dev/null || true

    log_info "目录创建完成"
}

# 清理数据
clean_data() {
    log_info "清理开发环境数据..."

    # 停止所有服务
    if [[ -f "$SCRIPT_DIR/stop-dev-mode.sh" ]]; then
        log_info "停止现有服务..."
        bash "$SCRIPT_DIR/stop-dev-mode.sh" --clean-workspace
    fi

    # 清理数据目录
    local clean_dirs=(
        "data/workspaces"
        "data/artifacts"
        "data/storage"
        "data/faas"
        "logs"
        "tmp"
    )

    for dir in "${clean_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            rm -rf "$PROJECT_ROOT/$dir"/*
            log_debug "清理目录: $dir"
        fi
    done

    # 清理数据库文件
    find "$PROJECT_ROOT/data" -name "*.db" -delete 2>/dev/null || true
    find "$PROJECT_ROOT/data" -name "*.lock" -delete 2>/dev/null || true

    log_info "数据清理完成"
}

# 生成开发配置文件
generate_dev_configs() {
    log_info "生成开发环境配置文件..."

    local config_dir="$PROJECT_ROOT/configs"

    # 为每个服务生成开发配置
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local config_file="$config_dir/${service}.dev.yaml"

        if [[ ! -f "$config_file" ]]; then
            log_debug "生成 $service 开发配置..."

            cat > "$config_file" << EOF
# $service 开发环境配置
server:
  port: $port
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 数据库配置
database:
  driver: sqlite
  dsn: "./data/${service}.db"
  log_level: info
  auto_migrate: true

# Redis 配置
redis:
  addr: "localhost:6379"
  password: ""
  db: 0

# 日志配置
logging:
  level: debug
  format: text
  output: stdout

# 开发模式配置
dev:
  enabled: true
  auth_disabled: true
  cors_enabled: true
  pprof_enabled: true

# 监控配置
monitoring:
  metrics_enabled: true
  health_check_enabled: true
EOF
            log_debug "生成配置文件: $config_file"
        fi
    done

    log_info "配置文件生成完成"
}

# 构建服务
build_services() {
    log_info "构建服务..."

    cd "$PROJECT_ROOT"

    local services_to_build=()
    if [[ -n "$SELECTED_SERVICES" ]]; then
        IFS=',' read -ra services_to_build <<< "$SELECTED_SERVICES"
    else
        services_to_build=("${!SERVICES[@]}")
    fi

    for service in "${services_to_build[@]}"; do
        if [[ -n "${SERVICES[$service]}" ]]; then
            log_debug "构建 $service..."

            local cmd_path="./cmd/$service"
            if [[ -d "$cmd_path" ]]; then
                # 设置构建变量
                local version=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
                local build_time=$(date -u '+%Y-%m-%d_%H:%M:%S')
                local git_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")

                go build \
                    -ldflags="-s -w -X main.version=${version} -X main.buildTime=${build_time} -X main.gitCommit=${git_commit}" \
                    -o "bin/$service" \
                    "$cmd_path"

                if [[ $? -eq 0 ]]; then
                    log_debug "$service 构建成功"
                else
                    log_error "$service 构建失败"
                    exit 1
                fi
            else
                log_warn "$service 源码目录不存在: $cmd_path"
            fi
        fi
    done

    log_info "服务构建完成"
}

# 检查端口占用
check_port_available() {
    local port=$1
    local service_name=$2

    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被 $service_name 占用"
        local pids=$(lsof -ti:$port)
        log_warn "占用进程 PID: $pids"

        read -p "是否终止占用进程并继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "$pids" | xargs kill -TERM 2>/dev/null || true
            sleep 2

            # 检查是否还有进程占用
            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                log_warn "强制终止占用进程..."
                lsof -ti:$port | xargs kill -KILL 2>/dev/null || true
                sleep 1
            fi
            return 0
        else
            return 1
        fi
    fi
    return 0
}

# 启动单个服务
start_service() {
    local service_name=$1
    local port="${SERVICES[$service_name]}"
    local config_file="$PROJECT_ROOT/configs/${service_name}.dev.yaml"
    local binary_path="$PROJECT_ROOT/bin/$service_name"
    local log_file="$PROJECT_ROOT/logs/${service_name}.log"
    local pid_file="$PROJECT_ROOT/logs/${service_name}.pid"

    log_info "启动 ${service_name} (端口: $port)..."

    # 检查二进制文件是否存在
    if [[ ! -f "$binary_path" ]]; then
        log_error "$service_name 二进制文件不存在: $binary_path"
        return 1
    fi

    # 检查配置文件是否存在
    if [[ ! -f "$config_file" ]]; then
        log_warn "$service_name 配置文件不存在，使用默认配置"
        config_file=""
    fi

    # 检查端口是否可用
    if ! check_port_available "$port" "$service_name"; then
        log_error "端口 $port 被占用，无法启动 $service_name"
        return 1
    fi

    # 构建启动命令
    local start_cmd="$binary_path"
    if [[ -n "$config_file" ]]; then
        start_cmd="$start_cmd --config=$config_file"
    fi

    # 启动服务 (后台运行)
    cd "$PROJECT_ROOT"
    nohup $start_cmd > "$log_file" 2>&1 &
    local pid=$!

    # 保存PID
    echo $pid > "$pid_file"

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if kill -0 $pid 2>/dev/null; then
        log_info "${service_name} 启动成功 (PID: $pid, Port: $port)"

        # 健康检查
        local health_url="http://localhost:$port/health"
        local retry_count=0
        local max_retries=15

        log_debug "等待 $service_name 健康检查..."
        while [ $retry_count -lt $max_retries ]; do
            if curl -s -f "$health_url" > /dev/null 2>&1; then
                log_info "${service_name} 健康检查通过 ✅"
                return 0
            fi
            retry_count=$((retry_count + 1))
            sleep 2
        done

        log_warn "${service_name} 健康检查失败，但服务可能仍在启动中"
        return 0
    else
        log_error "${service_name} 启动失败"
        cat "$log_file" | tail -10
        return 1
    fi
}

# 并行启动服务
start_service_parallel() {
    local service_name=$1
    start_service "$service_name" &
}

# 启动所有服务
start_all_services() {
    local services_to_start=()
    if [[ -n "$SELECTED_SERVICES" ]]; then
        IFS=',' read -ra services_to_start <<< "$SELECTED_SERVICES"
    else
        # 按依赖顺序启动服务
        services_to_start=("user-service" "config-service" "app-manager" "cicd-service" "script-service" "faas-service" "api-gateway")
    fi

    log_info "启动服务: ${services_to_start[*]}"

    local failed_services=()

    if [[ "$PARALLEL_START" == "true" ]]; then
        log_info "并行启动模式..."

        # 并行启动所有服务
        for service in "${services_to_start[@]}"; do
            if [[ -n "${SERVICES[$service]}" ]]; then
                start_service_parallel "$service"
            else
                log_warn "未知服务: $service"
            fi
        done

        # 等待所有后台任务完成
        wait

        # 检查启动结果
        for service in "${services_to_start[@]}"; do
            local pid_file="$PROJECT_ROOT/logs/${service}.pid"
            if [[ -f "$pid_file" ]]; then
                local pid=$(cat "$pid_file")
                if ! kill -0 "$pid" 2>/dev/null; then
                    failed_services+=("$service")
                fi
            else
                failed_services+=("$service")
            fi
        done
    else
        log_info "顺序启动模式..."

        # 顺序启动服务
        for service in "${services_to_start[@]}"; do
            if [[ -n "${SERVICES[$service]}" ]]; then
                if ! start_service "$service"; then
                    failed_services+=("$service")
                    if [[ "$CONTINUE_ON_ERROR" != "true" ]]; then
                        log_error "服务 $service 启动失败，停止启动流程"
                        break
                    fi
                fi
                # 服务间启动间隔
                sleep 2
            else
                log_warn "未知服务: $service"
            fi
        done
    fi

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_error "以下服务启动失败: ${failed_services[*]}"
        return 1
    else
        log_info "所有服务启动完成 ✅"
        return 0
    fi
}

# 显示服务状态
show_service_status() {
    echo
    log_info "服务状态检查..."
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                        服务状态                            ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"

    local running_services=()
    local failed_services=()

    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local pid_file="$PROJECT_ROOT/logs/${service}.pid"
        local status="❌"
        local status_text="未运行"

        # 检查PID文件和进程状态
        if [[ -f "$pid_file" ]]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                # 检查端口监听
                if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                    status="✅"
                    status_text="运行中"
                    running_services+=("$service")
                else
                    status="⚠️"
                    status_text="进程存在但端口未监听"
                    failed_services+=("$service")
                fi
            else
                status="❌"
                status_text="进程已退出"
                failed_services+=("$service")
            fi
        else
            failed_services+=("$service")
        fi

        # 格式化服务名称
        local service_display=""
        case $service in
            "user-service") service_display="👤 用户认证服务" ;;
            "api-gateway") service_display="🚪 API网关服务" ;;
            "app-manager") service_display="📱 应用管理服务" ;;
            "cicd-service") service_display="🚀 CI/CD服务" ;;
            "config-service") service_display="⚙️  配置服务" ;;
            "script-service") service_display="📜 脚本执行服务" ;;
            "faas-service") service_display="⚡ FaaS服务" ;;
            *) service_display="🔧 $service" ;;
        esac

        printf "${CYAN}║  %s %s: %s (端口:%s)${NC}\n" "$status" "$service_display" "$status_text" "$port"
    done

    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  📚 API文档和管理界面:                                     ║${NC}"

    for service in "${running_services[@]}"; do
        local port="${SERVICES[$service]}"
        case $service in
            "api-gateway")
                echo -e "${CYAN}║    - API网关: http://localhost:$port                      ║${NC}"
                ;;
            "user-service")
                echo -e "${CYAN}║    - 用户管理: http://localhost:$port/swagger             ║${NC}"
                ;;
            "app-manager")
                echo -e "${CYAN}║    - 应用管理: http://localhost:$port/swagger             ║${NC}"
                ;;
            "cicd-service")
                echo -e "${CYAN}║    - CI/CD: http://localhost:$port/swagger                ║${NC}"
                ;;
            "config-service")
                echo -e "${CYAN}║    - 配置服务: http://localhost:$port/swagger             ║${NC}"
                ;;
            "script-service")
                echo -e "${CYAN}║    - 脚本执行: http://localhost:$port/swagger             ║${NC}"
                ;;
            "faas-service")
                echo -e "${CYAN}║    - FaaS服务: http://localhost:$port/swagger             ║${NC}"
                ;;
        esac
    done

    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"

    # 显示统计信息
    echo
    log_info "启动统计: ${#running_services[@]} 个服务运行中, ${#failed_services[@]} 个服务失败"

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_warn "失败的服务: ${failed_services[*]}"
        echo -e "${YELLOW}💡 提示: 查看日志文件了解失败原因: tail -f logs/[service-name].log${NC}"
    fi

    echo
}

# 显示使用说明
show_usage_info() {
    echo
    log_info "开发环境使用说明："
    echo -e "${PURPLE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                      使用说明                              ║${NC}"
    echo -e "${PURPLE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${PURPLE}║  🔓 认证已禁用，所有API请求无需Token                       ║${NC}"
    echo -e "${PURPLE}║  🧑‍💻 默认使用开发用户身份 (admin权限)                      ║${NC}"
    echo -e "${PURPLE}║  📝 日志文件位于 logs/ 目录                                ║${NC}"
    echo -e "${PURPLE}║  🛑 停止服务: ./scripts/stop-dev-mode.sh                   ║${NC}"
    echo -e "${PURPLE}║  📊 查看日志: tail -f logs/[service-name].log              ║${NC}"
    echo -e "${PURPLE}║  🔄 重启服务: ./scripts/start-dev-mode.sh -c               ║${NC}"
    echo -e "${PURPLE}║  📋 查看状态: ps aux | grep -E '(user|api|app|cicd)'       ║${NC}"
    echo -e "${PURPLE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${PURPLE}║  🔧 常用命令:                                              ║${NC}"
    echo -e "${PURPLE}║    - 查看所有日志: tail -f logs/*.log                      ║${NC}"
    echo -e "${PURPLE}║    - 检查端口占用: netstat -tlnp | grep -E '808[0-7]'      ║${NC}"
    echo -e "${PURPLE}║    - 重新构建: go build -o bin/[service] cmd/[service]     ║${NC}"
    echo -e "${PURPLE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${YELLOW}⚠️  注意：此为开发环境配置，请勿在生产环境使用！${NC}"
    echo

    # 显示环境变量信息
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}🔧 当前环境变量:${NC}"
        echo -e "${BLUE}  ENV: $ENV${NC}"
        echo -e "${BLUE}  PAAS_DEV_MODE: $PAAS_DEV_MODE${NC}"
        echo -e "${BLUE}  PAAS_AUTH_ENABLED: $PAAS_AUTH_ENABLED${NC}"
        echo -e "${BLUE}  LOG_LEVEL: $LOG_LEVEL${NC}"
        echo -e "${BLUE}  DATABASE_DRIVER: $DATABASE_DRIVER${NC}"
        echo
    fi
}

# 主函数
main() {
    # 默认参数
    local clean_data=false
    local skip_build=false
    local skip_deps=false
    local config_only=false
    PARALLEL_START=false
    VERBOSE=false
    CONTINUE_ON_ERROR=false
    SELECTED_SERVICES=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--services)
                SELECTED_SERVICES="$2"
                shift 2
                ;;
            -p|--parallel)
                PARALLEL_START=true
                shift
                ;;
            -c|--clean)
                clean_data=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --config-only)
                config_only=true
                shift
                ;;
            --continue-on-error)
                CONTINUE_ON_ERROR=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    echo -e "${GREEN}🚀 启动PaaS平台开发环境${NC}"
    echo -e "${GREEN}项目根目录: $PROJECT_ROOT${NC}"
    echo

    # 显示开发模式警告
    show_dev_warning

    # 检查必要工具
    if [[ "$skip_deps" != "true" ]]; then
        check_prerequisites
    fi

    # 设置环境变量
    setup_dev_env

    # 清理数据
    if [[ "$clean_data" == "true" ]]; then
        clean_data
    fi

    # 创建目录
    create_directories

    # 生成配置文件
    generate_dev_configs

    # 仅生成配置文件模式
    if [[ "$config_only" == "true" ]]; then
        log_info "配置文件生成完成，退出"
        exit 0
    fi

    # 构建服务
    if [[ "$skip_build" != "true" ]]; then
        build_services
    fi

    # 启动服务
    if start_all_services; then
        # 显示状态
        show_service_status

        # 显示使用说明
        show_usage_info

        log_info "开发环境启动完成！ 🎉"
    else
        log_error "开发环境启动失败！"
        echo
        echo -e "${YELLOW}💡 故障排除建议:${NC}"
        echo -e "${YELLOW}1. 检查日志文件: tail -f logs/*.log${NC}"
        echo -e "${YELLOW}2. 检查端口占用: netstat -tlnp | grep -E '808[0-7]'${NC}"
        echo -e "${YELLOW}3. 重新构建: $0 --clean${NC}"
        echo -e "${YELLOW}4. 查看详细输出: $0 --verbose${NC}"
        exit 1
    fi
}

# 信号处理
trap 'log_info "收到中断信号，正在停止服务..."; bash "$SCRIPT_DIR/stop-dev-mode.sh"; exit 0' INT TERM

# 执行主函数
main "$@"
