#!/bin/bash

# PaaS平台开发环境停止脚本
# 此脚本用于停止所有开发环境中运行的服务

set -e  # 遇到错误时退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "${VERBOSE:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 服务配置 (与启动脚本保持一致)
declare -A SERVICES=(
    ["user-service"]="8083"
    ["api-gateway"]="8080"
    ["app-manager"]="8081"
    ["cicd-service"]="8082"
    ["config-service"]="8084"
    ["script-service"]="8085"
    ["faas-service"]="8087"
)

# 显示帮助信息
show_help() {
    cat << EOF
PaaS 平台开发环境停止脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -s, --services SERVICES 指定要停止的服务 (逗号分隔)
    -f, --force             强制停止 (使用 SIGKILL)
    -v, --verbose           详细输出
    --clean-workspace       同时清理工作空间数据
    --clean-all             清理所有数据 (包括数据库)
    --keep-logs             保留日志文件

可用服务:
$(for service in "${!SERVICES[@]}"; do echo "    - $service (端口: ${SERVICES[$service]})"; done)

示例:
    $0                              # 停止所有服务
    $0 -s user-service,api-gateway  # 仅停止指定服务
    $0 -f                           # 强制停止所有服务
    $0 --clean-workspace            # 停止服务并清理工作空间
    $0 --clean-all                  # 停止服务并清理所有数据

EOF
}

# 停止单个服务
stop_service() {
    local service_name=$1
    local force_kill=${2:-false}
    local pid_file="$PROJECT_ROOT/logs/${service_name}.pid"
    local port="${SERVICES[$service_name]}"

    log_info "停止 ${service_name}..."

    local stopped=false

    # 通过PID文件停止
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")

        if kill -0 "$pid" 2>/dev/null; then
            log_debug "通过PID停止 ${service_name} (PID: $pid)..."

            if [[ "$force_kill" == "true" ]]; then
                kill -KILL "$pid" 2>/dev/null || true
                sleep 1
            else
                kill -TERM "$pid" 2>/dev/null || true

                # 等待进程优雅退出
                local count=0
                while kill -0 "$pid" 2>/dev/null && [ $count -lt 15 ]; do
                    sleep 1
                    count=$((count + 1))
                done

                # 如果进程仍在运行，强制终止
                if kill -0 "$pid" 2>/dev/null; then
                    log_warn "强制终止 ${service_name}..."
                    kill -KILL "$pid" 2>/dev/null || true
                    sleep 1
                fi
            fi

            if ! kill -0 "$pid" 2>/dev/null; then
                log_info "${service_name} 已停止 ✅"
                stopped=true
            fi
        else
            log_debug "${service_name} 进程不存在 (PID: $pid)"
        fi

        # 删除PID文件
        rm -f "$pid_file"
    fi

    # 通过端口停止 (备用方法)
    if [[ "$stopped" != "true" && -n "$port" ]]; then
        stop_service_by_port "$port" "$service_name" "$force_kill"
    fi

    # 通过进程名停止 (最后的方法)
    if [[ "$stopped" != "true" ]]; then
        stop_service_by_name "$service_name" "$force_kill"
    fi
}

# 通过端口停止服务
stop_service_by_port() {
    local port=$1
    local service_name=$2
    local force_kill=${3:-false}

    local pids=$(lsof -ti:$port 2>/dev/null || true)

    if [[ -n "$pids" ]]; then
        log_debug "通过端口停止 ${service_name} (端口: $port, PID: $pids)..."

        if [[ "$force_kill" == "true" ]]; then
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
            sleep 1
        else
            echo "$pids" | xargs kill -TERM 2>/dev/null || true

            # 等待进程退出
            sleep 3

            # 检查是否还有进程占用端口
            local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
            if [[ -n "$remaining_pids" ]]; then
                log_warn "强制终止占用端口 $port 的进程..."
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
                sleep 1
            fi
        fi

        # 验证端口是否已释放
        if ! lsof -ti:$port >/dev/null 2>&1; then
            log_debug "端口 $port 已释放"
            return 0
        else
            log_warn "端口 $port 仍被占用"
            return 1
        fi
    else
        log_debug "端口 $port 未被占用"
        return 0
    fi
}

# 通过进程名停止服务
stop_service_by_name() {
    local service_name=$1
    local force_kill=${2:-false}

    local pids=$(pgrep -f "$service_name" 2>/dev/null || true)

    if [[ -n "$pids" ]]; then
        log_debug "通过进程名停止 ${service_name} (PID: $pids)..."

        if [[ "$force_kill" == "true" ]]; then
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
        else
            echo "$pids" | xargs kill -TERM 2>/dev/null || true
            sleep 2

            # 检查是否还有进程在运行
            local remaining_pids=$(pgrep -f "$service_name" 2>/dev/null || true)
            if [[ -n "$remaining_pids" ]]; then
                log_warn "强制停止剩余的 $service_name 进程: $remaining_pids"
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
            fi
        fi

        log_debug "$service_name 进程已停止"
        return 0
    else
        log_debug "$service_name 进程未运行"
        return 0
    fi
}

# 停止所有服务
stop_all_services() {
    local force_kill=${1:-false}
    local services_to_stop=()

    if [[ -n "$SELECTED_SERVICES" ]]; then
        IFS=',' read -ra services_to_stop <<< "$SELECTED_SERVICES"
    else
        # 按相反顺序停止服务 (与启动顺序相反)
        services_to_stop=("api-gateway" "faas-service" "script-service" "cicd-service" "app-manager" "config-service" "user-service")
    fi

    log_info "停止服务: ${services_to_stop[*]}"

    local failed_services=()

    for service in "${services_to_stop[@]}"; do
        if [[ -n "${SERVICES[$service]}" ]]; then
            if ! stop_service "$service" "$force_kill"; then
                failed_services+=("$service")
            fi
            # 服务间停止间隔
            sleep 1
        else
            log_warn "未知服务: $service"
        fi
    done

    # 停止可能的pprof端口
    log_debug "停止pprof端口..."
    local pprof_ports=(6061 6062 6063 6064 6065 6066 6067)
    for port in "${pprof_ports[@]}"; do
        stop_service_by_port "$port" "pprof-$port" "$force_kill" >/dev/null 2>&1 || true
    done

    # 清理可能的Go开发进程
    log_debug "清理Go开发进程..."
    local go_pids=$(pgrep -f "go run" 2>/dev/null || true)
    if [[ -n "$go_pids" ]]; then
        log_debug "停止Go开发进程: $go_pids"
        if [[ "$force_kill" == "true" ]]; then
            echo "$go_pids" | xargs kill -KILL 2>/dev/null || true
        else
            echo "$go_pids" | xargs kill -TERM 2>/dev/null || true
        fi
    fi

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_warn "以下服务停止失败: ${failed_services[*]}"
        return 1
    else
        log_info "所有服务已停止 ✅"
        return 0
    fi
}

# 清理临时文件
cleanup_temp_files() {
    local clean_workspace=${1:-false}
    local clean_all=${2:-false}
    local keep_logs=${3:-false}

    log_info "清理临时文件..."

    # 清理PID文件
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        rm -f "$PROJECT_ROOT/logs"/*.pid
        log_debug "清理PID文件"
    fi

    # 清理锁文件
    if [[ -d "$PROJECT_ROOT/data" ]]; then
        find "$PROJECT_ROOT/data" -name "*.lock" -delete 2>/dev/null || true
        log_debug "清理锁文件"
    fi

    # 清理临时目录
    if [[ -d "$PROJECT_ROOT/tmp" ]]; then
        rm -rf "$PROJECT_ROOT/tmp"/*
        log_debug "清理临时目录"
    fi

    # 清理工作空间数据
    if [[ "$clean_workspace" == "true" ]]; then
        log_warn "清理工作空间数据..."
        local workspace_dirs=(
            "data/workspaces"
            "data/artifacts"
            "data/faas/workspaces"
            "data/faas/artifacts"
        )

        for dir in "${workspace_dirs[@]}"; do
            if [[ -d "$PROJECT_ROOT/$dir" ]]; then
                rm -rf "$PROJECT_ROOT/$dir"/*
                log_debug "清理目录: $dir"
            fi
        done
    fi

    # 清理所有数据
    if [[ "$clean_all" == "true" ]]; then
        log_warn "清理所有数据..."

        # 清理数据库文件
        find "$PROJECT_ROOT/data" -name "*.db" -delete 2>/dev/null || true
        find "$PROJECT_ROOT/data" -name "*.sqlite" -delete 2>/dev/null || true
        log_debug "清理数据库文件"

        # 清理配置文件
        find "$PROJECT_ROOT/data/configs" -type f -delete 2>/dev/null || true
        log_debug "清理配置数据"

        # 清理存储文件
        if [[ -d "$PROJECT_ROOT/data/storage" ]]; then
            rm -rf "$PROJECT_ROOT/data/storage"/*
            log_debug "清理存储文件"
        fi
    fi

    # 清理日志文件
    if [[ "$keep_logs" != "true" ]]; then
        if [[ -d "$PROJECT_ROOT/logs" ]]; then
            # 保留最近的日志文件，删除旧的
            find "$PROJECT_ROOT/logs" -name "*.log" -mtime +1 -delete 2>/dev/null || true
            log_debug "清理旧日志文件"
        fi
    fi

    log_info "临时文件清理完成"
}

# 显示停止状态
show_stop_status() {
    echo
    log_info "服务停止状态检查..."
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                      服务停止状态                          ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"

    local running_services=()
    local stopped_services=()

    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local pid_file="$PROJECT_ROOT/logs/${service}.pid"
        local status="✅"
        local status_text="已停止"

        # 检查PID文件
        if [[ -f "$pid_file" ]]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                status="❌"
                status_text="仍在运行"
                running_services+=("$service")
            else
                stopped_services+=("$service")
            fi
        else
            # 检查端口监听
            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                status="⚠️"
                status_text="端口仍被占用"
                running_services+=("$service")
            else
                stopped_services+=("$service")
            fi
        fi

        # 格式化服务名称
        local service_display=""
        case $service in
            "user-service") service_display="👤 用户认证服务" ;;
            "api-gateway") service_display="🚪 API网关服务" ;;
            "app-manager") service_display="📱 应用管理服务" ;;
            "cicd-service") service_display="🚀 CI/CD服务" ;;
            "config-service") service_display="⚙️  配置服务" ;;
            "script-service") service_display="📜 脚本执行服务" ;;
            "faas-service") service_display="⚡ FaaS服务" ;;
            *) service_display="🔧 $service" ;;
        esac

        printf "${CYAN}║  %s %s: %s (端口:%s)${NC}\n" "$status" "$service_display" "$status_text" "$port"
    done

    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"

    # 显示统计信息
    echo
    log_info "停止统计: ${#stopped_services[@]} 个服务已停止, ${#running_services[@]} 个服务仍在运行"

    if [[ ${#running_services[@]} -gt 0 ]]; then
        log_warn "仍在运行的服务: ${running_services[*]}"
        echo -e "${YELLOW}💡 提示: 使用 -f 选项强制停止: $0 -f${NC}"
        return 1
    else
        log_info "所有服务已成功停止 ✅"
        return 0
    fi
}

# 这个函数已被 show_help 替代，保留兼容性
show_usage() {
    show_help
}

# 主函数
main() {
    # 默认参数
    local force_kill=false
    local clean_workspace=false
    local clean_all=false
    local keep_logs=false
    VERBOSE=false
    SELECTED_SERVICES=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--services)
                SELECTED_SERVICES="$2"
                shift 2
                ;;
            -f|--force)
                force_kill=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --clean-workspace)
                clean_workspace=true
                shift
                ;;
            --clean-all)
                clean_all=true
                clean_workspace=true
                shift
                ;;
            --keep-logs)
                keep_logs=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    echo -e "${RED}🛑 停止PaaS平台开发环境${NC}"
    echo -e "${RED}项目根目录: $PROJECT_ROOT${NC}"
    echo

    if [[ "$force_kill" == "true" ]]; then
        log_warn "使用强制停止模式 (SIGKILL)"
    fi

    # 停止所有服务
    if stop_all_services "$force_kill"; then
        log_info "服务停止成功"
    else
        log_warn "部分服务停止失败"
    fi

    # 清理临时文件
    cleanup_temp_files "$clean_workspace" "$clean_all" "$keep_logs"

    # 显示停止状态
    if show_stop_status; then
        log_info "开发环境已完全停止 🎉"

        echo
        echo -e "${YELLOW}💡 提示：${NC}"
        echo -e "${YELLOW}• 重新启动开发环境: ./scripts/start-dev-mode.sh${NC}"
        echo -e "${YELLOW}• 查看日志文件: ls -la logs/${NC}"
        echo -e "${YELLOW}• 清理所有数据: $0 --clean-all${NC}"
        echo
        exit 0
    else
        log_error "开发环境停止不完整"
        echo
        echo -e "${YELLOW}💡 故障排除建议:${NC}"
        echo -e "${YELLOW}1. 强制停止: $0 -f${NC}"
        echo -e "${YELLOW}2. 检查进程: ps aux | grep -E '(user|api|app|cicd|config|script|faas)'${NC}"
        echo -e "${YELLOW}3. 检查端口: netstat -tlnp | grep -E '808[0-7]'${NC}"
        echo -e "${YELLOW}4. 手动终止: kill -9 \$(pgrep -f 'service-name')${NC}"
        exit 1
    fi
}

# 信号处理
trap 'log_info "收到中断信号，正在强制停止..."; force_kill=true' INT TERM

# 执行主函数
main "$@"
