#!/bin/bash

# 测试本地构建模式脚本

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查 Docker 环境
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 守护进程未运行"
        exit 1
    fi
    
    log_success "Docker 环境正常"
}

# 构建测试镜像
build_test_image() {
    log_info "构建测试镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 创建简单的测试 Dockerfile
    cat > Dockerfile.test << 'EOF'
FROM alpine:latest
RUN echo "本地构建测试镜像" > /test.txt
CMD ["cat", "/test.txt"]
EOF
    
    # 构建镜像
    docker build -f Dockerfile.test -t paas-local-test:latest .
    
    # 清理临时文件
    rm -f Dockerfile.test
    
    log_success "测试镜像构建完成"
}

# 测试本地构建模式
test_local_build_mode() {
    log_info "测试本地构建模式..."
    
    # 设置环境变量
    export BUILD_MODE=local
    export DOCKER_REGISTRY=local
    
    # 启动本地开发环境
    log_info "启动本地开发环境..."
    cd "$PROJECT_ROOT"
    
    # 使用本地配置启动服务
    docker-compose -f docker-compose.local.yml up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose -f docker-compose.local.yml ps postgres | grep -q "Up"; then
        log_success "PostgreSQL 启动成功"
    else
        log_error "PostgreSQL 启动失败"
        return 1
    fi
    
    if docker-compose -f docker-compose.local.yml ps redis | grep -q "Up"; then
        log_success "Redis 启动成功"
    else
        log_error "Redis 启动失败"
        return 1
    fi
}

# 测试 CI/CD 服务本地构建
test_cicd_local_build() {
    log_info "测试 CI/CD 服务本地构建..."
    
    cd "$PROJECT_ROOT"
    
    # 使用本地配置构建 CI/CD 服务
    docker build \
        -f deployments/docker/cicd-service.Dockerfile \
        -t paas-cicd-service:local \
        --build-arg CONFIG_FILE=configs/cicd-service.local.yaml \
        .
    
    if [[ $? -eq 0 ]]; then
        log_success "CI/CD 服务镜像构建成功"
    else
        log_error "CI/CD 服务镜像构建失败"
        return 1
    fi
    
    # 运行 CI/CD 服务容器
    log_info "启动 CI/CD 服务容器..."
    docker run -d \
        --name paas-cicd-test \
        --network paas-network \
        -p 8082:8082 \
        -e BUILD_MODE=local \
        -e DOCKER_REGISTRY=local \
        -e DATABASE_DSN="host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai" \
        -e REDIS_ADDR="redis:6379" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        paas-cicd-service:local
    
    # 等待服务启动
    log_info "等待 CI/CD 服务启动..."
    sleep 15
    
    # 检查服务健康状态
    if curl -f http://localhost:8082/health &> /dev/null; then
        log_success "CI/CD 服务启动成功"
    else
        log_error "CI/CD 服务启动失败"
        docker logs paas-cicd-test
        return 1
    fi
}

# 测试本地构建功能
test_local_build_functionality() {
    log_info "测试本地构建功能..."
    
    # 创建测试流水线配置
    cat > /tmp/test-pipeline.json << 'EOF'
{
  "name": "本地构建测试流水线",
  "description": "测试本地构建模式的流水线",
  "app_id": "test-app",
  "config": {
    "spec": {
      "triggers": [
        {
          "type": "manual"
        }
      ],
      "stages": [
        {
          "name": "构建阶段",
          "steps": [
            {
              "name": "Docker 构建",
              "type": "docker_build",
              "config": {
                "dockerfile": "Dockerfile.test",
                "context": ".",
                "tags": ["test-app:local"]
              }
            },
            {
              "name": "Docker 推送",
              "type": "docker_push",
              "config": {
                "tags": ["test-app:local"]
              }
            }
          ]
        }
      ]
    }
  }
}
EOF
    
    # 通过 API 创建流水线
    log_info "创建测试流水线..."
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d @/tmp/test-pipeline.json \
        http://localhost:8082/api/v1/pipelines)
    
    if echo "$response" | grep -q "id"; then
        log_success "测试流水线创建成功"
        pipeline_id=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        log_info "流水线 ID: $pipeline_id"
    else
        log_error "测试流水线创建失败"
        echo "响应: $response"
        return 1
    fi
    
    # 触发构建
    log_info "触发构建..."
    build_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"pipeline_id\":\"$pipeline_id\",\"branch\":\"main\",\"commit_hash\":\"test123\",\"trigger_type\":\"manual\"}" \
        http://localhost:8082/api/v1/builds)
    
    if echo "$build_response" | grep -q "id"; then
        log_success "构建任务创建成功"
        build_id=$(echo "$build_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        log_info "构建 ID: $build_id"
    else
        log_error "构建任务创建失败"
        echo "响应: $build_response"
        return 1
    fi
    
    # 等待构建完成
    log_info "等待构建完成..."
    for i in {1..30}; do
        build_status=$(curl -s http://localhost:8082/api/v1/builds/$build_id | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        
        case "$build_status" in
            "success")
                log_success "构建成功完成"
                return 0
                ;;
            "failed")
                log_error "构建失败"
                return 1
                ;;
            "running"|"pending")
                log_info "构建进行中... ($build_status)"
                sleep 5
                ;;
            *)
                log_warning "未知构建状态: $build_status"
                sleep 5
                ;;
        esac
    done
    
    log_error "构建超时"
    return 1
}

# 清理测试环境
cleanup() {
    log_info "清理测试环境..."
    
    # 停止并删除测试容器
    docker stop paas-cicd-test &> /dev/null || true
    docker rm paas-cicd-test &> /dev/null || true
    
    # 删除测试镜像
    docker rmi paas-local-test:latest &> /dev/null || true
    docker rmi paas-cicd-service:local &> /dev/null || true
    docker rmi test-app:local &> /dev/null || true
    
    # 停止本地开发环境
    cd "$PROJECT_ROOT"
    docker-compose -f docker-compose.local.yml down &> /dev/null || true
    
    # 清理临时文件
    rm -f /tmp/test-pipeline.json
    
    log_success "清理完成"
}

# 主函数
main() {
    log_info "开始本地构建模式测试..."
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试步骤
    check_docker
    build_test_image
    test_local_build_mode
    test_cicd_local_build
    test_local_build_functionality
    
    log_success "所有测试通过！本地构建模式工作正常。"
}

# 执行主函数
main "$@"
