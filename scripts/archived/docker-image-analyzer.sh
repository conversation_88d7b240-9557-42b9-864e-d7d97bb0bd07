#!/bin/bash

# Docker镜像分析工具
# 分析镜像大小、层结构和优化建议

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_DIR="$PROJECT_ROOT/logs"
readonly REPORT_DIR="$PROJECT_ROOT/reports"
readonly REPORT_FILE="$REPORT_DIR/image-analysis-$(date +%Y%m%d-%H%M%S).md"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_title() {
    echo -e "\n${CYAN}📊 $1${NC}\n"
}

# 初始化环境
init_environment() {
    mkdir -p "$LOG_DIR" "$REPORT_DIR"
    log_info "Docker镜像分析工具启动"
    log_info "报告文件: $REPORT_FILE"
}

# 获取镜像信息
get_image_info() {
    local image="$1"
    
    # 检查镜像是否存在
    if ! docker images "$image" --format "{{.Repository}}" | grep -q "$(echo "$image" | cut -d: -f1)"; then
        return 1
    fi
    
    # 获取镜像基本信息
    local info=$(docker images "$image" --format "{{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}")
    echo "$info"
}

# 分析镜像层
analyze_image_layers() {
    local image="$1"
    
    print_title "分析镜像层: $image"
    
    echo "## 镜像层分析: $image" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 获取镜像历史
    local history=$(docker history "$image" --no-trunc --format "table {{.CreatedBy}}\t{{.Size}}")
    
    echo "### 镜像层历史" >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    echo "$history" >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 分析大层
    local large_layers=$(docker history "$image" --format "{{.Size}}\t{{.CreatedBy}}" | \
        grep -E "MB|GB" | sort -hr | head -5)
    
    if [[ -n "$large_layers" ]]; then
        echo "### 最大的5个镜像层" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "大小        创建命令" >> "$REPORT_FILE"
        echo "$large_layers" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # 显示到控制台
    echo "$history"
    echo ""
    
    if [[ -n "$large_layers" ]]; then
        log_info "最大的镜像层:"
        echo "$large_layers"
        echo ""
    fi
}

# 分析镜像内容
analyze_image_content() {
    local image="$1"
    
    print_title "分析镜像内容: $image"
    
    echo "## 镜像内容分析: $image" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 创建临时容器分析内容
    local container_id=$(docker create "$image" 2>/dev/null || echo "")
    
    if [[ -n "$container_id" ]]; then
        # 分析文件系统大小
        log_info "分析文件系统..."
        
        local fs_analysis=$(docker export "$container_id" | tar -tv | \
            awk '{print $3, $9}' | sort -nr | head -20 2>/dev/null || echo "分析失败")
        
        if [[ "$fs_analysis" != "分析失败" ]]; then
            echo "### 最大的20个文件" >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
            echo "大小(字节)  文件路径" >> "$REPORT_FILE"
            echo "$fs_analysis" >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            
            log_info "最大的文件:"
            echo "$fs_analysis" | head -10
            echo ""
        fi
        
        # 分析目录大小
        log_info "分析目录结构..."
        
        local dir_analysis=$(docker export "$container_id" | tar -tv | \
            awk '{print $9}' | grep -E "^[^/]+/$" | sort | uniq -c | sort -nr 2>/dev/null || echo "")
        
        if [[ -n "$dir_analysis" ]]; then
            echo "### 顶级目录文件数量" >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
            echo "文件数  目录" >> "$REPORT_FILE"
            echo "$dir_analysis" >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            
            log_info "目录文件数量:"
            echo "$dir_analysis"
            echo ""
        fi
        
        # 清理临时容器
        docker rm "$container_id" >/dev/null 2>&1
    else
        log_warning "无法创建临时容器进行内容分析"
    fi
}

# 生成优化建议
generate_optimization_suggestions() {
    local image="$1"
    local size="$2"
    
    print_title "生成优化建议: $image"
    
    echo "## 优化建议: $image" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    local suggestions=()
    
    # 基于镜像大小的建议
    local size_mb=$(echo "$size" | sed 's/MB//' | sed 's/GB/*1024/' | bc 2>/dev/null || echo "0")
    
    if (( $(echo "$size_mb > 500" | bc -l) )); then
        suggestions+=("🔍 镜像大小较大($size)，建议使用多阶段构建减小体积")
    fi
    
    if (( $(echo "$size_mb > 1000" | bc -l) )); then
        suggestions+=("⚠️ 镜像大小超过1GB，强烈建议优化")
    fi
    
    # 基于镜像历史的建议
    local layer_count=$(docker history "$image" --format "{{.ID}}" | wc -l)
    
    if [[ $layer_count -gt 20 ]]; then
        suggestions+=("📦 镜像层数过多($layer_count层)，建议合并RUN指令")
    fi
    
    # 检查基础镜像
    local base_image=$(docker history "$image" --format "{{.CreatedBy}}" | tail -1)
    
    if [[ "$base_image" == *"ubuntu"* ]] || [[ "$base_image" == *"debian"* ]]; then
        suggestions+=("🏔️ 建议使用Alpine Linux作为基础镜像以减小体积")
    fi
    
    # 检查是否包含包管理器缓存
    if docker run --rm "$image" sh -c "ls /var/cache/apk/ 2>/dev/null | wc -l" 2>/dev/null | grep -q -v "^0$"; then
        suggestions+=("🧹 发现APK缓存，建议在Dockerfile中添加清理命令")
    fi
    
    if docker run --rm "$image" sh -c "ls /var/lib/apt/lists/ 2>/dev/null | wc -l" 2>/dev/null | grep -q -v "^0$"; then
        suggestions+=("🧹 发现APT缓存，建议在Dockerfile中添加清理命令")
    fi
    
    # 输出建议
    if [[ ${#suggestions[@]} -gt 0 ]]; then
        echo "### 优化建议" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        for suggestion in "${suggestions[@]}"; do
            echo "- $suggestion" >> "$REPORT_FILE"
            log_info "$suggestion"
        done
        
        echo "" >> "$REPORT_FILE"
    else
        echo "### 优化建议" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        echo "✅ 镜像已经过良好优化，暂无明显改进建议。" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        log_success "镜像已经过良好优化"
    fi
}

# 比较镜像大小
compare_images() {
    local images=("$@")
    
    print_title "镜像大小比较"
    
    echo "## 镜像大小比较" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "| 镜像 | 标签 | 大小 | 创建时间 |" >> "$REPORT_FILE"
    echo "|------|------|------|----------|" >> "$REPORT_FILE"
    
    log_info "镜像大小比较:"
    printf "%-40s %-15s %-15s %-20s\n" "镜像" "标签" "大小" "创建时间"
    printf "%-40s %-15s %-15s %-20s\n" "----" "----" "----" "--------"
    
    for image in "${images[@]}"; do
        local info=$(get_image_info "$image")
        
        if [[ -n "$info" ]]; then
            local repo=$(echo "$info" | cut -f1)
            local tag=$(echo "$info" | cut -f2)
            local size=$(echo "$info" | cut -f5)
            local created=$(echo "$info" | cut -f4)
            
            printf "%-40s %-15s %-15s %-20s\n" "$repo" "$tag" "$size" "$created"
            echo "| $repo | $tag | $size | $created |" >> "$REPORT_FILE"
        fi
    done
    
    echo "" >> "$REPORT_FILE"
    echo ""
}

# 分析单个镜像
analyze_single_image() {
    local image="$1"
    
    log_info "分析镜像: $image"
    
    # 获取镜像信息
    local info=$(get_image_info "$image")
    
    if [[ -z "$info" ]]; then
        log_error "镜像不存在: $image"
        return 1
    fi
    
    local size=$(echo "$info" | cut -f5)
    local created=$(echo "$info" | cut -f4)
    
    log_info "镜像大小: $size"
    log_info "创建时间: $created"
    
    # 写入报告头部
    echo "# Docker镜像分析报告" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "**生成时间:** $(date)" >> "$REPORT_FILE"
    echo "**分析镜像:** $image" >> "$REPORT_FILE"
    echo "**镜像大小:** $size" >> "$REPORT_FILE"
    echo "**创建时间:** $created" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 执行各种分析
    analyze_image_layers "$image"
    analyze_image_content "$image"
    generate_optimization_suggestions "$image" "$size"
    
    log_success "镜像分析完成"
}

# 分析所有PaaS平台镜像
analyze_paas_images() {
    print_title "分析PaaS平台镜像"
    
    local registry="${REGISTRY:-localhost:5000}"
    local namespace="${NAMESPACE:-paas}"
    
    local services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "ci-cd-service"
        "monitoring-service"
        "notification-service"
        "load-balancer"
        "web"
    )
    
    local existing_images=()
    
    # 检查哪些镜像存在
    for service in "${services[@]}"; do
        local image="$registry/$namespace/$service:latest"
        if get_image_info "$image" >/dev/null 2>&1; then
            existing_images+=("$image")
        else
            log_warning "镜像不存在: $image"
        fi
    done
    
    if [[ ${#existing_images[@]} -eq 0 ]]; then
        log_error "没有找到PaaS平台镜像"
        return 1
    fi
    
    # 写入报告头部
    echo "# PaaS平台Docker镜像分析报告" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "**生成时间:** $(date)" >> "$REPORT_FILE"
    echo "**分析镜像数量:** ${#existing_images[@]}" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 比较所有镜像
    compare_images "${existing_images[@]}"
    
    # 分析每个镜像
    for image in "${existing_images[@]}"; do
        analyze_image_layers "$image"
        
        local size=$(get_image_info "$image" | cut -f5)
        generate_optimization_suggestions "$image" "$size"
        
        echo "---" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    done
    
    # 生成总结
    generate_summary_report "${existing_images[@]}"
    
    log_success "PaaS平台镜像分析完成"
}

# 生成总结报告
generate_summary_report() {
    local images=("$@")
    
    echo "## 总结报告" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 计算总大小
    local total_size=0
    local largest_image=""
    local largest_size=0
    
    for image in "${images[@]}"; do
        local info=$(get_image_info "$image")
        local size_str=$(echo "$info" | cut -f5)
        
        # 简单的大小比较（仅支持MB和GB）
        if [[ "$size_str" == *"GB"* ]]; then
            local size_num=$(echo "$size_str" | sed 's/GB//')
            local size_mb=$(echo "$size_num * 1024" | bc)
        else
            local size_mb=$(echo "$size_str" | sed 's/MB//')
        fi
        
        total_size=$(echo "$total_size + $size_mb" | bc)
        
        if (( $(echo "$size_mb > $largest_size" | bc -l) )); then
            largest_size=$size_mb
            largest_image="$image"
        fi
    done
    
    echo "### 统计信息" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "- **镜像总数:** ${#images[@]}" >> "$REPORT_FILE"
    echo "- **总大小:** ${total_size}MB" >> "$REPORT_FILE"
    echo "- **最大镜像:** $largest_image (${largest_size}MB)" >> "$REPORT_FILE"
    echo "- **平均大小:** $(echo "scale=2; $total_size / ${#images[@]}" | bc)MB" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 优化建议总结
    echo "### 总体优化建议" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "1. **使用多阶段构建** - 分离构建环境和运行环境" >> "$REPORT_FILE"
    echo "2. **选择合适的基础镜像** - 优先使用Alpine Linux" >> "$REPORT_FILE"
    echo "3. **清理缓存文件** - 删除包管理器缓存和临时文件" >> "$REPORT_FILE"
    echo "4. **合并镜像层** - 减少RUN指令数量" >> "$REPORT_FILE"
    echo "5. **定期更新** - 保持基础镜像和依赖的最新版本" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    log_info "总结报告已生成"
    log_info "镜像总数: ${#images[@]}"
    log_info "总大小: ${total_size}MB"
    log_info "最大镜像: $largest_image (${largest_size}MB)"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker镜像分析工具

用法: $0 [选项] [镜像名称]

选项:
  -h, --help          显示此帮助信息
  -a, --all           分析所有PaaS平台镜像
  -c, --compare       比较多个镜像
  -r, --report        生成详细报告

示例:
  $0 nginx:alpine                    # 分析单个镜像
  $0 --all                          # 分析所有PaaS平台镜像
  $0 --compare nginx:alpine ubuntu  # 比较多个镜像

EOF
}

# 主函数
main() {
    init_environment
    
    case "${1:-help}" in
        "-h"|"--help"|"help")
            show_help
            ;;
        "-a"|"--all")
            analyze_paas_images
            ;;
        "-c"|"--compare")
            shift
            compare_images "$@"
            ;;
        "-r"|"--report")
            shift
            analyze_single_image "$1"
            ;;
        *)
            if [[ -n "${1:-}" ]]; then
                analyze_single_image "$1"
            else
                show_help
            fi
            ;;
    esac
    
    if [[ -f "$REPORT_FILE" ]]; then
        log_success "分析报告已保存: $REPORT_FILE"
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
