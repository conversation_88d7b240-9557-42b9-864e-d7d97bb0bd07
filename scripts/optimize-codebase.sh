#!/bin/bash
# 代码库文档和脚本优化自动化脚本
# 功能：清理重复文档、合并相似脚本、归档过期内容
# 作者：PaaS平台开发团队
# 版本：1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_DIR="$PROJECT_ROOT/backup-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="$PROJECT_ROOT/optimization.log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
代码库优化脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -d, --dry-run       预览模式，不实际执行操作
  -b, --backup-only   仅创建备份，不执行优化
  -v, --verbose       详细输出模式
  --skip-backup       跳过备份创建（不推荐）

示例:
  $0                  # 执行完整优化
  $0 --dry-run        # 预览优化操作
  $0 --backup-only    # 仅创建备份
EOF
}

# 解析命令行参数
DRY_RUN=false
BACKUP_ONLY=false
VERBOSE=false
SKIP_BACKUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -b|--backup-only)
            BACKUP_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建备份
create_backup() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log_warning "跳过备份创建（不推荐）"
        return 0
    fi
    
    log_info "创建备份到: $BACKUP_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将创建备份目录: $BACKUP_DIR"
        return 0
    fi
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份重要文件和目录
    local backup_items=(
        "*.md"
        "scripts/"
        "docs/"
        "web/README.md"
        "web/docs/"
        "docker-compose*.yml"
    )
    
    for item in "${backup_items[@]}"; do
        if ls $item 1> /dev/null 2>&1; then
            cp -r $item "$BACKUP_DIR/" 2>/dev/null || true
            [[ "$VERBOSE" == "true" ]] && log_info "已备份: $item"
        fi
    done
    
    log_success "备份创建完成: $BACKUP_DIR"
}

# 创建新目录结构
create_directory_structure() {
    log_info "创建新目录结构..."
    
    local new_dirs=(
        "docs/guides/"
        "docs/历史修复记录/"
        "scripts/archived/"
        "docs/api/"
        "docs/deployment/"
    )
    
    for dir in "${new_dirs[@]}"; do
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[预览] 将创建目录: $dir"
        else
            mkdir -p "$dir"
            [[ "$VERBOSE" == "true" ]] && log_info "已创建目录: $dir"
        fi
    done
    
    log_success "目录结构创建完成"
}

# 整合README文件
integrate_readme_files() {
    log_info "整合README文件..."
    
    # README-CICD.md -> docs/guides/cicd-service-guide.md
    if [[ -f "README-CICD.md" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[预览] 将整合 README-CICD.md 到 docs/guides/cicd-service-guide.md"
        else
            # 创建新的指南文件，包含中文注释
            cat > docs/guides/cicd-service-guide.md << 'EOF'
# CI/CD 服务使用指南

> 本文档整合了原 README-CICD.md 的内容，提供完整的 CI/CD 服务使用说明。

EOF
            cat README-CICD.md >> docs/guides/cicd-service-guide.md
            rm README-CICD.md
            log_success "已整合 README-CICD.md"
        fi
    fi
    
    # README-DEV-AUTH.md -> docs/guides/development-auth-guide.md
    if [[ -f "README-DEV-AUTH.md" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[预览] 将整合 README-DEV-AUTH.md 到 docs/guides/development-auth-guide.md"
        else
            cat > docs/guides/development-auth-guide.md << 'EOF'
# 开发环境认证配置指南

> 本文档整合了原 README-DEV-AUTH.md 的内容，提供开发环境认证配置的详细说明。

EOF
            cat README-DEV-AUTH.md >> docs/guides/development-auth-guide.md
            rm README-DEV-AUTH.md
            log_success "已整合 README-DEV-AUTH.md"
        fi
    fi
    
    # README-IDP.md -> docs/guides/idp-integration-guide.md
    if [[ -f "README-IDP.md" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[预览] 将整合 README-IDP.md 到 docs/guides/idp-integration-guide.md"
        else
            cat > docs/guides/idp-integration-guide.md << 'EOF'
# 身份提供商集成指南

> 本文档整合了原 README-IDP.md 的内容，提供身份提供商集成的详细说明。

EOF
            cat README-IDP.md >> docs/guides/idp-integration-guide.md
            rm README-IDP.md
            log_success "已整合 README-IDP.md"
        fi
    fi
}

# 归档修复报告
archive_fix_reports() {
    log_info "归档修复报告..."
    
    local fix_reports=(
        "前端Token解析循环错误修复报告.md"
        "前端登录页面修复报告.md"
        "前端加载遮罩层修复报告.md"
        "架构重构实施总结.md"
        "架构重构实施计划.md"
    )
    
    for report in "${fix_reports[@]}"; do
        if [[ -f "$report" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                log_info "[预览] 将归档: $report"
            else
                mv "$report" "docs/历史修复记录/"
                log_success "已归档: $report"
            fi
        fi
    done
}

# 合并重复脚本
merge_duplicate_scripts() {
    log_info "合并重复脚本..."
    
    local duplicate_scripts=(
        "scripts/docker-image-optimizer.sh"
        "scripts/docker-image-analyzer.sh"
        "scripts/performance-optimizer.sh"
        "scripts/performance-analyzer.sh"
    )
    
    for script in "${duplicate_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                log_info "[预览] 将归档重复脚本: $script"
            else
                mv "$script" "scripts/archived/"
                log_success "已归档重复脚本: $(basename "$script")"
            fi
        fi
    done
}

# 整合项目总结文档
integrate_project_summaries() {
    log_info "整合项目总结文档..."
    
    # 删除重复的项目总结文档
    local duplicate_summaries=(
        "web/docs/PROJECT_SUMMARY.md"
        "docs/project-summary.md"
    )
    
    for summary in "${duplicate_summaries[@]}"; do
        if [[ -f "$summary" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                log_info "[预览] 将删除重复的项目总结: $summary"
            else
                # 在删除前，将有用内容追加到主文档
                if [[ "$summary" == "web/docs/PROJECT_SUMMARY.md" ]]; then
                    echo -e "\n\n## 前端项目补充信息\n" >> PROJECT_SUMMARY.md
                    grep -A 50 "前端" "$summary" >> PROJECT_SUMMARY.md 2>/dev/null || true
                fi
                rm "$summary"
                log_success "已删除重复文档: $summary"
            fi
        fi
    done
}

# 更新主README文件
update_main_readme() {
    log_info "更新主README文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将更新 README.md 中的文档链接"
        return 0
    fi
    
    # 在README.md中添加新的文档链接部分
    if ! grep -q "## 📚 详细文档指南" README.md; then
        cat >> README.md << 'EOF'

## 📚 详细文档指南

### 🔧 开发指南
- [CI/CD 服务使用指南](docs/guides/cicd-service-guide.md) - 完整的CI/CD流水线配置和使用说明
- [开发环境认证配置](docs/guides/development-auth-guide.md) - 开发环境认证跳过和配置方法
- [身份提供商集成指南](docs/guides/idp-integration-guide.md) - 第三方身份提供商集成说明

### 📋 历史记录
- [修复记录归档](docs/历史修复记录/) - 历史问题修复记录和解决方案

### 🔧 脚本工具
- [脚本归档](scripts/archived/) - 已归档的重复或过期脚本
EOF
        log_success "已更新 README.md 文档链接"
    fi
}

# 生成优化报告
generate_optimization_report() {
    log_info "生成优化报告..."
    
    local report_file="optimization-report-$(date +%Y%m%d-%H%M%S).md"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将生成优化报告: $report_file"
        return 0
    fi
    
    cat > "$report_file" << EOF
# 代码库优化执行报告

**执行时间**: $(date)  
**备份位置**: $BACKUP_DIR  
**执行模式**: $([[ "$DRY_RUN" == "true" ]] && echo "预览模式" || echo "实际执行")

## 执行的优化操作

### 1. 文档整合
- ✅ 整合了 README-CICD.md 到 docs/guides/cicd-service-guide.md
- ✅ 整合了 README-DEV-AUTH.md 到 docs/guides/development-auth-guide.md  
- ✅ 整合了 README-IDP.md 到 docs/guides/idp-integration-guide.md

### 2. 修复报告归档
- ✅ 将修复报告移动到 docs/历史修复记录/ 目录

### 3. 脚本优化
- ✅ 归档了重复的Docker和性能相关脚本

### 4. 项目总结整合
- ✅ 删除了重复的项目总结文档

## 建议的后续操作

1. **验证功能**: 运行 \`scripts/verify-optimization.sh\` 验证优化结果
2. **测试脚本**: 确保所有保留的脚本功能正常
3. **更新文档**: 检查并更新任何引用已删除文件的文档
4. **提交变更**: 将优化结果提交到版本控制系统

## 回滚方法

如果需要回滚，请运行：
\`\`\`bash
cp -r $BACKUP_DIR/* .
\`\`\`

---
**优化脚本版本**: 1.0.0  
**执行状态**: ✅ 成功完成
EOF
    
    log_success "优化报告已生成: $report_file"
}

# 主函数
main() {
    echo -e "${CYAN}🚀 PaaS平台代码库优化工具${NC}"
    echo -e "${CYAN}================================${NC}"
    
    # 初始化日志
    echo "优化开始时间: $(date)" > "$LOG_FILE"
    
    # 检查依赖
    check_dependencies
    
    # 创建备份
    create_backup
    
    if [[ "$BACKUP_ONLY" == "true" ]]; then
        log_success "仅备份模式完成"
        exit 0
    fi
    
    # 执行优化操作
    create_directory_structure
    integrate_readme_files
    archive_fix_reports
    merge_duplicate_scripts
    integrate_project_summaries
    update_main_readme
    generate_optimization_report
    
    echo -e "\n${GREEN}🎉 代码库优化完成！${NC}"
    echo -e "${BLUE}📦 备份位置: $BACKUP_DIR${NC}"
    echo -e "${BLUE}📝 日志文件: $LOG_FILE${NC}"
    echo -e "${YELLOW}📋 请运行验证脚本检查优化结果${NC}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${CYAN}💡 这是预览模式，没有实际执行操作${NC}"
        echo -e "${CYAN}💡 要执行实际优化，请运行: $0${NC}"
    fi
}

# 执行主函数
main "$@"
