#!/bin/bash

# PaaS 平台服务停止脚本
# 用于安全停止所有 PaaS 平台相关服务
# 支持生产环境和开发环境的服务停止

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "${VERBOSE:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 服务配置 (包含所有PaaS服务)
declare -A SERVICES=(
    ["user-service"]="8083"
    ["api-gateway"]="8080"
    ["app-manager"]="8081"
    ["cicd-service"]="8082"
    ["config-service"]="8084"
    ["script-service"]="8085"
    ["faas-service"]="8087"
    ["loadbalancer-service"]="8086"
    ["monitor-service"]="8088"
)

# 外部服务端口
declare -A EXTERNAL_SERVICES=(
    ["redis"]="6379"
    ["postgresql"]="5432"
    ["elasticsearch"]="9200"
    ["kibana"]="5601"
    ["prometheus"]="9090"
    ["grafana"]="3000"
)

# 显示帮助信息
show_help() {
    cat << EOF
PaaS 平台服务停止脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -s, --services SERVICES 指定要停止的服务 (逗号分隔)
    -f, --force             强制停止 (使用 SIGKILL)
    -v, --verbose           详细输出
    -e, --external          同时停止外部服务 (Redis, PostgreSQL等)
    --dev-mode              开发模式停止 (调用 stop-dev-mode.sh)
    --production            生产模式停止
    --docker                停止 Docker 容器
    --clean-workspace       清理工作空间数据
    --dry-run               仅显示将要停止的服务，不实际执行

可用的 PaaS 服务:
$(for service in "${!SERVICES[@]}"; do echo "    - $service (端口: ${SERVICES[$service]})"; done)

可用的外部服务:
$(for service in "${!EXTERNAL_SERVICES[@]}"; do echo "    - $service (端口: ${EXTERNAL_SERVICES[$service]})"; done)

示例:
    $0                              # 停止所有 PaaS 服务
    $0 -s user-service,api-gateway  # 仅停止指定服务
    $0 -f                           # 强制停止所有服务
    $0 -e                           # 停止所有服务包括外部服务
    $0 --dev-mode                   # 开发模式停止
    $0 --production                 # 生产模式停止
    $0 --docker                     # 停止 Docker 容器
    $0 --dry-run                    # 预览模式

EOF
}

# 停止服务函数 (增强版)
stop_service() {
    local service_name=$1
    local force_kill=${2:-false}
    local pid_file="$PROJECT_ROOT/logs/${service_name}.pid"
    local port="${SERVICES[$service_name]}"

    log_info "停止 ${service_name}..."

    local stopped=false

    # 方法1: 通过PID文件停止
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")

        if ps -p $pid > /dev/null 2>&1; then
            log_debug "通过PID停止 ${service_name} (PID: $pid)..."

            if [[ "$force_kill" == "true" ]]; then
                kill -KILL $pid 2>/dev/null || true
                sleep 1
            else
                kill -TERM $pid 2>/dev/null || true

                # 等待进程优雅退出
                local count=0
                while ps -p $pid > /dev/null 2>&1 && [ $count -lt 30 ]; do
                    sleep 1
                    ((count++))
                done

                # 如果进程仍在运行，强制终止
                if ps -p $pid > /dev/null 2>&1; then
                    log_warning "强制终止 ${service_name}..."
                    kill -KILL $pid 2>/dev/null || true
                    sleep 1
                fi
            fi

            if ! ps -p $pid > /dev/null 2>&1; then
                log_success "${service_name} 已停止 ✅"
                stopped=true
            fi
        else
            log_debug "${service_name} PID 文件存在但进程不在运行"
        fi

        # 删除PID文件
        rm -f "$pid_file"
    fi

    # 方法2: 通过端口停止
    if [[ "$stopped" != "true" && -n "$port" ]]; then
        stop_service_by_port "$port" "$service_name" "$force_kill"
        stopped=true
    fi

    # 方法3: 通过进程名停止
    if [[ "$stopped" != "true" ]]; then
        stop_service_by_name "$service_name" "$force_kill"
    fi
}

# 通过端口停止服务
stop_service_by_port() {
    local port=$1
    local service_name=$2
    local force_kill=${3:-false}

    local pids=$(lsof -ti:$port 2>/dev/null || true)

    if [[ -n "$pids" ]]; then
        log_debug "通过端口停止 ${service_name} (端口: $port, PID: $pids)..."

        if [[ "$force_kill" == "true" ]]; then
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
            sleep 1
        else
            echo "$pids" | xargs kill -TERM 2>/dev/null || true
            sleep 3

            # 检查是否还有进程占用端口
            local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
            if [[ -n "$remaining_pids" ]]; then
                log_warning "强制终止占用端口 $port 的进程..."
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
                sleep 1
            fi
        fi

        return 0
    else
        log_debug "端口 $port 未被占用"
        return 0
    fi
}

# 通过进程名停止服务
stop_service_by_name() {
    local service_name=$1
    local force_kill=${2:-false}

    local pids=$(pgrep -f "$service_name" 2>/dev/null || true)

    if [[ -n "$pids" ]]; then
        log_debug "通过进程名停止 ${service_name} (PID: $pids)..."

        if [[ "$force_kill" == "true" ]]; then
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
        else
            echo "$pids" | xargs kill -TERM 2>/dev/null || true
            sleep 2

            # 检查是否还有进程在运行
            local remaining_pids=$(pgrep -f "$service_name" 2>/dev/null || true)
            if [[ -n "$remaining_pids" ]]; then
                log_warning "强制停止剩余的 $service_name 进程: $remaining_pids"
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
            fi
        fi

        return 0
    else
        log_debug "$service_name 进程未运行"
        return 0
    fi
}

# 停止外部服务
stop_external_services() {
    local force_kill=${1:-false}

    log_info "停止外部服务..."

    for service in "${!EXTERNAL_SERVICES[@]}"; do
        local port="${EXTERNAL_SERVICES[$service]}"
        log_debug "检查外部服务 $service (端口: $port)..."

        stop_service_by_port "$port" "$service" "$force_kill"
    done

    # 停止 Docker 容器 (如果存在)
    if command -v docker &> /dev/null; then
        log_debug "停止相关 Docker 容器..."

        local containers=$(docker ps -q --filter "label=paas.service" 2>/dev/null || true)
        if [[ -n "$containers" ]]; then
            log_info "停止 PaaS Docker 容器: $containers"
            echo "$containers" | xargs docker stop 2>/dev/null || true
        fi
    fi
}

# 停止 Docker 容器
stop_docker_containers() {
    if ! command -v docker &> /dev/null; then
        log_warning "Docker 未安装，跳过容器停止"
        return 0
    fi

    log_info "停止 Docker 容器..."

    # 停止所有 PaaS 相关容器
    local paas_containers=$(docker ps -q --filter "name=paas-" 2>/dev/null || true)
    if [[ -n "$paas_containers" ]]; then
        log_info "停止 PaaS 容器: $paas_containers"
        echo "$paas_containers" | xargs docker stop 2>/dev/null || true
    fi

    # 停止开发环境容器
    local dev_containers=$(docker ps -q --filter "label=env=development" 2>/dev/null || true)
    if [[ -n "$dev_containers" ]]; then
        log_info "停止开发环境容器: $dev_containers"
        echo "$dev_containers" | xargs docker stop 2>/dev/null || true
    fi

    log_success "Docker 容器停止完成"
}

# 停止所有 PaaS 服务
stop_all_paas_services() {
    local force_kill=${1:-false}
    local services_to_stop=()

    if [[ -n "$SELECTED_SERVICES" ]]; then
        IFS=',' read -ra services_to_stop <<< "$SELECTED_SERVICES"
    else
        # 按相反顺序停止服务 (与启动顺序相反)
        services_to_stop=("api-gateway" "faas-service" "script-service" "cicd-service" "app-manager" "config-service" "user-service" "loadbalancer-service" "monitor-service")
    fi

    log_info "停止 PaaS 服务: ${services_to_stop[*]}"

    local failed_services=()

    for service in "${services_to_stop[@]}"; do
        if [[ -n "${SERVICES[$service]}" ]]; then
            if ! stop_service "$service" "$force_kill"; then
                failed_services+=("$service")
            fi
            sleep 1
        else
            log_warning "未知服务: $service"
        fi
    done

    # 清理其他可能的进程
    log_info "清理其他相关进程..."

    # 停止可能的 Go 开发进程
    local go_pids=$(pgrep -f "go run" 2>/dev/null || true)
    if [[ -n "$go_pids" ]]; then
        log_info "停止 Go 开发进程: $go_pids"
        if [[ "$force_kill" == "true" ]]; then
            echo "$go_pids" | xargs kill -KILL 2>/dev/null || true
        else
            echo "$go_pids" | xargs kill -TERM 2>/dev/null || true
        fi
    fi

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_warning "以下服务停止失败: ${failed_services[*]}"
        return 1
    else
        log_success "所有 PaaS 服务已停止 ✅"
        return 0
    fi
}

# 显示服务状态
show_service_status() {
    echo
    log_info "服务状态检查..."
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                      服务停止状态                          ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"

    local running_services=()
    local stopped_services=()

    # 检查 PaaS 服务
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local status="✅"
        local status_text="已停止"

        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            status="❌"
            status_text="仍在运行"
            running_services+=("$service")
        else
            stopped_services+=("$service")
        fi

        printf "${CYAN}║  %s %s: %s (端口:%s)${NC}\n" "$status" "$service" "$status_text" "$port"
    done

    # 检查外部服务 (如果启用)
    if [[ "$CHECK_EXTERNAL" == "true" ]]; then
        echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
        echo -e "${CYAN}║                      外部服务状态                          ║${NC}"
        echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"

        for service in "${!EXTERNAL_SERVICES[@]}"; do
            local port="${EXTERNAL_SERVICES[$service]}"
            local status="✅"
            local status_text="已停止"

            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                status="❌"
                status_text="仍在运行"
            fi

            printf "${CYAN}║  %s %s: %s (端口:%s)${NC}\n" "$status" "$service" "$status_text" "$port"
        done
    fi

    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"

    # 显示统计信息
    echo
    log_info "停止统计: ${#stopped_services[@]} 个服务已停止, ${#running_services[@]} 个服务仍在运行"

    if [[ ${#running_services[@]} -gt 0 ]]; then
        log_warning "仍在运行的服务: ${running_services[*]}"
        return 1
    else
        log_success "所有服务已成功停止 ✅"
        return 0
    fi
}

# 主函数
main() {
    # 默认参数
    local force_kill=false
    local stop_external=false
    local dev_mode=false
    local production_mode=false
    local stop_docker=false
    local clean_workspace=false
    local dry_run=false
    VERBOSE=false
    SELECTED_SERVICES=""
    CHECK_EXTERNAL=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--services)
                SELECTED_SERVICES="$2"
                shift 2
                ;;
            -f|--force)
                force_kill=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -e|--external)
                stop_external=true
                CHECK_EXTERNAL=true
                shift
                ;;
            --dev-mode)
                dev_mode=true
                shift
                ;;
            --production)
                production_mode=true
                shift
                ;;
            --docker)
                stop_docker=true
                shift
                ;;
            --clean-workspace)
                clean_workspace=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    echo -e "${RED}🛑 停止 PaaS 平台服务${NC}"
    echo -e "${RED}项目根目录: $PROJECT_ROOT${NC}"
    echo

    # 开发模式停止
    if [[ "$dev_mode" == "true" ]]; then
        log_info "使用开发模式停止脚本..."
        if [[ -f "$SCRIPT_DIR/stop-dev-mode.sh" ]]; then
            local dev_args=""
            [[ "$force_kill" == "true" ]] && dev_args="$dev_args -f"
            [[ "$VERBOSE" == "true" ]] && dev_args="$dev_args -v"
            [[ "$clean_workspace" == "true" ]] && dev_args="$dev_args --clean-workspace"
            [[ -n "$SELECTED_SERVICES" ]] && dev_args="$dev_args -s $SELECTED_SERVICES"

            exec bash "$SCRIPT_DIR/stop-dev-mode.sh" $dev_args
        else
            log_error "开发模式停止脚本不存在: $SCRIPT_DIR/stop-dev-mode.sh"
            exit 1
        fi
    fi

    # 预览模式
    if [[ "$dry_run" == "true" ]]; then
        log_info "预览模式 - 将要停止的服务:"

        local services_to_show=()
        if [[ -n "$SELECTED_SERVICES" ]]; then
            IFS=',' read -ra services_to_show <<< "$SELECTED_SERVICES"
        else
            services_to_show=("${!SERVICES[@]}")
        fi

        for service in "${services_to_show[@]}"; do
            if [[ -n "${SERVICES[$service]}" ]]; then
                echo "  - $service (端口: ${SERVICES[$service]})"
            fi
        done

        [[ "$stop_external" == "true" ]] && echo "  - 外部服务: ${!EXTERNAL_SERVICES[*]}"
        [[ "$stop_docker" == "true" ]] && echo "  - Docker 容器"

        exit 0
    fi

    # 生产模式警告
    if [[ "$production_mode" == "true" ]]; then
        echo -e "${RED}⚠️  生产模式停止 - 这将停止所有服务！${NC}"
        read -p "确认要在生产环境停止所有服务吗? (输入 'YES' 确认): " -r
        if [[ $REPLY != "YES" ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi

    if [[ "$force_kill" == "true" ]]; then
        log_warning "使用强制停止模式 (SIGKILL)"
    fi

    # 停止 PaaS 服务
    if stop_all_paas_services "$force_kill"; then
        log_success "PaaS 服务停止成功"
    else
        log_warning "部分 PaaS 服务停止失败"
    fi

    # 停止外部服务
    if [[ "$stop_external" == "true" ]]; then
        stop_external_services "$force_kill"
    fi

    # 停止 Docker 容器
    if [[ "$stop_docker" == "true" ]]; then
        stop_docker_containers
    fi

    # 显示服务状态
    if show_service_status; then
        log_success "所有服务停止完成 🎉"

        # 显示日志文件位置
        echo
        echo -e "${PURPLE}📁 日志文件位置:${NC}"
        if [[ -d "$PROJECT_ROOT/logs" ]]; then
            find "$PROJECT_ROOT/logs" -name "*.log" -type f | head -10 | while read -r logfile; do
                echo "  - $(basename "$logfile"): $logfile"
            done
        fi

        echo
        echo -e "${YELLOW}💡 提示：${NC}"
        echo -e "${YELLOW}• 重新启动服务: ./scripts/start-dev-mode.sh${NC}"
        echo -e "${YELLOW}• 查看日志: tail -f logs/[service-name].log${NC}"
        echo -e "${YELLOW}• 检查端口: netstat -tlnp | grep -E '808[0-9]'${NC}"
        echo

        exit 0
    else
        log_error "服务停止不完整"
        echo
        echo -e "${YELLOW}💡 故障排除建议:${NC}"
        echo -e "${YELLOW}1. 强制停止: $0 -f${NC}"
        echo -e "${YELLOW}2. 检查进程: ps aux | grep -E '(user|api|app|cicd|config|script|faas)'${NC}"
        echo -e "${YELLOW}3. 检查端口: netstat -tlnp | grep -E '808[0-9]'${NC}"
        echo -e "${YELLOW}4. 手动终止: kill -9 \$(pgrep -f 'service-name')${NC}"
        exit 1
    fi
}

# 信号处理
trap 'log_info "收到中断信号，正在强制停止..."; force_kill=true' INT TERM

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
