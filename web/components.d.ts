/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AccessibilityPanel: typeof import('./src/components/accessibility/AccessibilityPanel.vue')['default']
    Button: typeof import('./src/components/ui/Button.vue')['default']
    Card: typeof import('./src/components/ui/Card.vue')['default']
    CodeEditor: typeof import('./src/components/scripts/CodeEditor.vue')['default']
    ConfirmDialog: typeof import('./src/components/common/ConfirmDialog.vue')['default']
    DataTable: typeof import('./src/components/ui/DataTable.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTree: typeof import('element-plus/es')['ElTree']
    FormField: typeof import('./src/components/ui/FormField.vue')['default']
    KeyboardNavigation: typeof import('./src/components/accessibility/KeyboardNavigation.vue')['default']
    LanguageDetector: typeof import('./src/components/i18n/LanguageDetector.vue')['default']
    LanguageSwitcher: typeof import('./src/components/ui/LanguageSwitcher.vue')['default']
    LoadingCard: typeof import('./src/components/common/LoadingCard.vue')['default']
    MobileNavGroup: typeof import('./src/components/mobile/MobileNavGroup.vue')['default']
    MobileNavigation: typeof import('./src/components/mobile/MobileNavigation.vue')['default']
    MobileNavItem: typeof import('./src/components/mobile/MobileNavItem.vue')['default']
    OptimizedChart: typeof import('./src/components/charts/OptimizedChart.vue')['default']
    PageHeader: typeof import('./src/components/common/PageHeader.vue')['default']
    PageHeaderTest: typeof import('./src/components/common/PageHeaderTest.vue')['default']
    RealtimeChart: typeof import('./src/components/charts/RealtimeChart.vue')['default']
    ResponsiveLayout: typeof import('./src/components/layout/ResponsiveLayout.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuntimeBadge: typeof import('./src/components/scripts/RuntimeBadge.vue')['default']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    SidebarItem: typeof import('./src/components/layout/SidebarItem.vue')['default']
    SidebarMenu: typeof import('./src/components/layout/SidebarMenu.vue')['default']
    StatusTag: typeof import('./src/components/common/StatusTag.vue')['default']
    TaskProgress: typeof import('./src/components/scripts/TaskProgress.vue')['default']
    TaskStatusBadge: typeof import('./src/components/scripts/TaskStatusBadge.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
