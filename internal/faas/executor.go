package faas

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// FunctionExecutor FaaS函数执行器
type FunctionExecutor struct {
	containerManager container.ContainerManager
	containerPool    *ContainerPool
	logger           logger.Logger
	config           *ExecutorConfig
	metrics          *ExecutorMetrics
	mutex            sync.RWMutex
}

// ExecutorConfig 执行器配置
type ExecutorConfig struct {
	MaxConcurrentExecutions int           `yaml:"max_concurrent_executions"` // 最大并发执行数
	DefaultTimeout          time.Duration `yaml:"default_timeout"`           // 默认超时时间
	MaxExecutionTime        time.Duration `yaml:"max_execution_time"`        // 最大执行时间
	ContainerPoolSize       int           `yaml:"container_pool_size"`       // 容器池大小
	PrewarmContainers       int           `yaml:"prewarm_containers"`        // 预热容器数量
	CleanupInterval         time.Duration `yaml:"cleanup_interval"`          // 清理间隔
	ResourceLimits          *ResourceLimits `yaml:"resource_limits"`         // 资源限制
}

// ResourceLimits 资源限制配置
type ResourceLimits struct {
	CPULimit    string `yaml:"cpu_limit"`    // CPU限制，如 "0.5"
	MemoryLimit string `yaml:"memory_limit"` // 内存限制，如 "512m"
	DiskLimit   string `yaml:"disk_limit"`   // 磁盘限制，如 "1g"
}

// ExecutorMetrics 执行器指标
type ExecutorMetrics struct {
	TotalExecutions     int64         `json:"total_executions"`
	SuccessfulExecutions int64        `json:"successful_executions"`
	FailedExecutions    int64         `json:"failed_executions"`
	AverageExecutionTime time.Duration `json:"average_execution_time"`
	ActiveExecutions    int64         `json:"active_executions"`
	ContainerPoolUsage  int           `json:"container_pool_usage"`
	mutex               sync.RWMutex
}

// FunctionRequest 函数执行请求
type FunctionRequest struct {
	FunctionID   string                 `json:"function_id" binding:"required"`
	FunctionName string                 `json:"function_name" binding:"required"`
	Runtime      string                 `json:"runtime" binding:"required"` // nodejs, python, go
	Code         string                 `json:"code" binding:"required"`
	Handler      string                 `json:"handler"`                    // 入口函数
	Environment  map[string]string      `json:"environment"`                // 环境变量
	Parameters   map[string]interface{} `json:"parameters"`                 // 函数参数
	Timeout      time.Duration          `json:"timeout"`                    // 执行超时
	MemoryLimit  string                 `json:"memory_limit"`               // 内存限制
	RequestID    string                 `json:"request_id"`                 // 请求ID
}

// FunctionResponse 函数执行响应
type FunctionResponse struct {
	RequestID     string        `json:"request_id"`
	FunctionID    string        `json:"function_id"`
	Status        string        `json:"status"`        // success, error, timeout
	Result        interface{}   `json:"result"`        // 执行结果
	Error         string        `json:"error"`         // 错误信息
	ExecutionTime time.Duration `json:"execution_time"` // 执行时间
	MemoryUsage   int64         `json:"memory_usage"`   // 内存使用量
	Logs          []string      `json:"logs"`           // 执行日志
	ContainerID   string        `json:"container_id"`   // 容器ID
	StartTime     time.Time     `json:"start_time"`     // 开始时间
	EndTime       time.Time     `json:"end_time"`       // 结束时间
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	Request     *FunctionRequest
	Container   *container.Container
	StartTime   time.Time
	Timeout     time.Duration
	CancelFunc  context.CancelFunc
	Logs        []string
	mutex       sync.Mutex
}

// NewFunctionExecutor 创建函数执行器
func NewFunctionExecutor(containerManager container.ContainerManager, config *ExecutorConfig, logger logger.Logger) *FunctionExecutor {
	if config == nil {
		config = getDefaultExecutorConfig()
	}

	executor := &FunctionExecutor{
		containerManager: containerManager,
		logger:           logger,
		config:           config,
		metrics: &ExecutorMetrics{
			TotalExecutions:      0,
			SuccessfulExecutions: 0,
			FailedExecutions:     0,
			AverageExecutionTime: 0,
			ActiveExecutions:     0,
			ContainerPoolUsage:   0,
		},
	}

	// 初始化容器池
	executor.containerPool = NewContainerPool(containerManager, config.ContainerPoolSize, logger)

	// 启动预热容器
	go executor.prewarmContainers()

	// 启动清理任务
	go executor.startCleanupTask()

	return executor
}

// getDefaultExecutorConfig 获取默认配置
func getDefaultExecutorConfig() *ExecutorConfig {
	return &ExecutorConfig{
		MaxConcurrentExecutions: 100,
		DefaultTimeout:          30 * time.Second,
		MaxExecutionTime:        5 * time.Minute,
		ContainerPoolSize:       10,
		PrewarmContainers:       3,
		CleanupInterval:         5 * time.Minute,
		ResourceLimits: &ResourceLimits{
			CPULimit:    "0.5",
			MemoryLimit: "512m",
			DiskLimit:   "1g",
		},
	}
}

// ExecuteFunction 执行函数
func (fe *FunctionExecutor) ExecuteFunction(ctx context.Context, request *FunctionRequest) (*FunctionResponse, error) {
	startTime := time.Now()
	
	// 验证请求
	if err := fe.validateRequest(request); err != nil {
		return nil, fmt.Errorf("请求验证失败: %w", err)
	}

	// 检查并发限制
	if !fe.checkConcurrencyLimit() {
		return nil, fmt.Errorf("超过最大并发执行数限制: %d", fe.config.MaxConcurrentExecutions)
	}

	// 增加活跃执行计数
	fe.incrementActiveExecutions()
	defer fe.decrementActiveExecutions()

	// 设置超时
	timeout := request.Timeout
	if timeout == 0 {
		timeout = fe.config.DefaultTimeout
	}
	if timeout > fe.config.MaxExecutionTime {
		timeout = fe.config.MaxExecutionTime
	}

	execCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 创建执行上下文
	executionContext := &ExecutionContext{
		Request:    request,
		StartTime:  startTime,
		Timeout:    timeout,
		CancelFunc: cancel,
		Logs:       make([]string, 0),
	}

	// 执行函数
	response, err := fe.doExecuteFunction(execCtx, executionContext)
	
	// 更新指标
	fe.updateMetrics(response, err, time.Since(startTime))

	if err != nil {
		fe.logger.Error("函数执行失败", "function_id", request.FunctionID, "error", err)
		return nil, err
	}

	fe.logger.Info("函数执行成功", "function_id", request.FunctionID, 
		"execution_time", response.ExecutionTime, "status", response.Status)

	return response, nil
}

// validateRequest 验证请求
func (fe *FunctionExecutor) validateRequest(request *FunctionRequest) error {
	if request.FunctionID == "" {
		return fmt.Errorf("函数ID不能为空")
	}
	if request.FunctionName == "" {
		return fmt.Errorf("函数名称不能为空")
	}
	if request.Runtime == "" {
		return fmt.Errorf("运行时不能为空")
	}
	if request.Code == "" {
		return fmt.Errorf("函数代码不能为空")
	}
	
	// 验证运行时支持
	supportedRuntimes := []string{"nodejs", "python", "go", "java"}
	runtimeSupported := false
	for _, runtime := range supportedRuntimes {
		if request.Runtime == runtime {
			runtimeSupported = true
			break
		}
	}
	if !runtimeSupported {
		return fmt.Errorf("不支持的运行时: %s", request.Runtime)
	}

	return nil
}

// checkConcurrencyLimit 检查并发限制
func (fe *FunctionExecutor) checkConcurrencyLimit() bool {
	fe.metrics.mutex.RLock()
	defer fe.metrics.mutex.RUnlock()
	return fe.metrics.ActiveExecutions < int64(fe.config.MaxConcurrentExecutions)
}

// incrementActiveExecutions 增加活跃执行计数
func (fe *FunctionExecutor) incrementActiveExecutions() {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	fe.metrics.ActiveExecutions++
}

// decrementActiveExecutions 减少活跃执行计数
func (fe *FunctionExecutor) decrementActiveExecutions() {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	fe.metrics.ActiveExecutions--
}

// updateMetrics 更新指标
func (fe *FunctionExecutor) updateMetrics(response *FunctionResponse, err error, duration time.Duration) {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	
	fe.metrics.TotalExecutions++
	
	if err != nil || (response != nil && response.Status == "error") {
		fe.metrics.FailedExecutions++
	} else {
		fe.metrics.SuccessfulExecutions++
	}
	
	// 更新平均执行时间
	if fe.metrics.TotalExecutions > 0 {
		totalTime := fe.metrics.AverageExecutionTime * time.Duration(fe.metrics.TotalExecutions-1)
		fe.metrics.AverageExecutionTime = (totalTime + duration) / time.Duration(fe.metrics.TotalExecutions)
	}
}

// doExecuteFunction 执行函数的核心逻辑
func (fe *FunctionExecutor) doExecuteFunction(ctx context.Context, execCtx *ExecutionContext) (*FunctionResponse, error) {
	// 获取或创建容器
	containerInfo, err := fe.getOrCreateContainer(ctx, execCtx.Request)
	if err != nil {
		return nil, fmt.Errorf("获取容器失败: %w", err)
	}

	execCtx.Container = containerInfo
	defer fe.cleanupContainer(containerInfo)

	// 准备函数代码和参数
	if err := fe.prepareFunction(ctx, execCtx); err != nil {
		return nil, fmt.Errorf("准备函数失败: %w", err)
	}

	// 执行函数
	result, err := fe.runFunction(ctx, execCtx)
	if err != nil {
		return &FunctionResponse{
			RequestID:     execCtx.Request.RequestID,
			FunctionID:    execCtx.Request.FunctionID,
			Status:        "error",
			Error:         err.Error(),
			ExecutionTime: time.Since(execCtx.StartTime),
			ContainerID:   containerInfo.ID,
			StartTime:     execCtx.StartTime,
			EndTime:       time.Now(),
			Logs:          execCtx.Logs,
		}, nil
	}

	// 构建响应
	response := &FunctionResponse{
		RequestID:     execCtx.Request.RequestID,
		FunctionID:    execCtx.Request.FunctionID,
		Status:        "success",
		Result:        result,
		ExecutionTime: time.Since(execCtx.StartTime),
		ContainerID:   containerInfo.ID,
		StartTime:     execCtx.StartTime,
		EndTime:       time.Now(),
		Logs:          execCtx.Logs,
	}

	// 获取内存使用情况
	if stats, err := fe.containerManager.GetContainerStats(ctx, containerInfo.ID); err == nil {
		response.MemoryUsage = stats.MemoryUsage
	}

	return response, nil
}

// getOrCreateContainer 获取或创建容器
func (fe *FunctionExecutor) getOrCreateContainer(ctx context.Context, request *FunctionRequest) (*container.Container, error) {
	// 尝试从容器池获取
	if poolContainer := fe.containerPool.GetContainer(request.Runtime); poolContainer != nil {
		fe.logger.Debug("从容器池获取容器", "container_id", poolContainer.ID, "runtime", request.Runtime)
		return poolContainer, nil
	}

	// 创建新容器
	containerConfig := fe.buildContainerConfig(request)
	containerInfo, err := fe.containerManager.CreateContainer(ctx, containerConfig)
	if err != nil {
		return nil, fmt.Errorf("创建容器失败: %w", err)
	}

	// 启动容器
	if err := fe.containerManager.StartContainer(ctx, containerInfo.ID); err != nil {
		fe.containerManager.RemoveContainer(ctx, containerInfo.ID, true)
		return nil, fmt.Errorf("启动容器失败: %w", err)
	}

	fe.logger.Info("创建新容器", "container_id", containerInfo.ID, "runtime", request.Runtime)
	return containerInfo, nil
}

// buildContainerConfig 构建容器配置
func (fe *FunctionExecutor) buildContainerConfig(request *FunctionRequest) *container.ContainerConfig {
	// 根据运行时选择镜像
	image := fe.getRuntimeImage(request.Runtime)

	// 构建环境变量
	env := make([]string, 0)
	for k, v := range request.Environment {
		env = append(env, fmt.Sprintf("%s=%s", k, v))
	}

	// 添加函数相关环境变量
	env = append(env, fmt.Sprintf("FUNCTION_ID=%s", request.FunctionID))
	env = append(env, fmt.Sprintf("FUNCTION_NAME=%s", request.FunctionName))
	env = append(env, fmt.Sprintf("HANDLER=%s", request.Handler))

	// 内存限制
	memoryLimit := request.MemoryLimit
	if memoryLimit == "" {
		memoryLimit = fe.config.ResourceLimits.MemoryLimit
	}

	return &container.ContainerConfig{
		Image:      image,
		Name:       fmt.Sprintf("faas-%s-%d", request.FunctionID, time.Now().Unix()),
		Command:    []string{},
		Env:        env,
		WorkingDir: "/app",
		Resources: &container.ResourceConfig{
			CPULimit:    fe.config.ResourceLimits.CPULimit,
			MemoryLimit: memoryLimit,
			DiskLimit:   fe.config.ResourceLimits.DiskLimit,
		},
		AutoRemove: false, // 手动管理容器生命周期
		Labels: map[string]string{
			"paas.function.id":   request.FunctionID,
			"paas.function.name": request.FunctionName,
			"paas.runtime":       request.Runtime,
			"paas.type":          "faas",
		},
	}
}

// getRuntimeImage 获取运行时镜像
func (fe *FunctionExecutor) getRuntimeImage(runtime string) string {
	images := map[string]string{
		"nodejs": "node:18-alpine",
		"python": "python:3.9-alpine",
		"go":     "golang:1.21-alpine",
		"java":   "openjdk:11-jre-slim",
	}

	if image, exists := images[runtime]; exists {
		return image
	}

	return "alpine:latest" // 默认镜像
}

// prepareFunction 准备函数代码和参数
func (fe *FunctionExecutor) prepareFunction(ctx context.Context, execCtx *ExecutionContext) error {
	request := execCtx.Request

	// 将函数代码写入容器
	codeFile := "/tmp/function." + fe.getFileExtension(request.Runtime)
	writeCodeCmd := []string{"sh", "-c", fmt.Sprintf("echo '%s' > %s", request.Code, codeFile)}

	if _, err := fe.containerManager.ExecCommand(ctx, execCtx.Container.ID, writeCodeCmd); err != nil {
		return fmt.Errorf("写入函数代码失败: %w", err)
	}

	// 将参数写入容器
	if len(request.Parameters) > 0 {
		paramsJSON, _ := json.Marshal(request.Parameters)
		writeParamsCmd := []string{"sh", "-c", fmt.Sprintf("echo '%s' > /tmp/params.json", string(paramsJSON))}

		if _, err := fe.containerManager.ExecCommand(ctx, execCtx.Container.ID, writeParamsCmd); err != nil {
			return fmt.Errorf("写入函数参数失败: %w", err)
		}
	}

	execCtx.addLog("函数代码和参数准备完成")
	return nil
}

// getFileExtension 获取文件扩展名
func (fe *FunctionExecutor) getFileExtension(runtime string) string {
	extensions := map[string]string{
		"nodejs": "js",
		"python": "py",
		"go":     "go",
		"java":   "java",
	}

	if ext, exists := extensions[runtime]; exists {
		return ext
	}

	return "txt"
}

// GetMetrics 获取执行器指标
func (fe *FunctionExecutor) GetMetrics() *ExecutorMetrics {
	fe.metrics.mutex.RLock()
	defer fe.metrics.mutex.RUnlock()

	// 返回副本
	return &ExecutorMetrics{
		TotalExecutions:      fe.metrics.TotalExecutions,
		SuccessfulExecutions: fe.metrics.SuccessfulExecutions,
		FailedExecutions:     fe.metrics.FailedExecutions,
		AverageExecutionTime: fe.metrics.AverageExecutionTime,
		ActiveExecutions:     fe.metrics.ActiveExecutions,
		ContainerPoolUsage:   fe.metrics.ContainerPoolUsage,
	}
}

// prewarmContainers 预热容器
func (fe *FunctionExecutor) prewarmContainers() {
	fe.logger.Info("开始预热容器", "prewarm_count", fe.config.PrewarmContainers)

	// 为每种支持的运行时预热容器
	runtimes := []string{"nodejs", "python", "go", "java"}

	for _, runtime := range runtimes {
		if err := fe.containerPool.PrewarmContainers(runtime, fe.config.PrewarmContainers); err != nil {
			fe.logger.Error("预热容器失败", "runtime", runtime, "error", err)
		}
	}

	fe.logger.Info("容器预热完成")
}

// startCleanupTask 启动清理任务
func (fe *FunctionExecutor) startCleanupTask() {
	ticker := time.NewTicker(fe.config.CleanupInterval)
	defer ticker.Stop()

	fe.logger.Info("启动容器清理任务", "interval", fe.config.CleanupInterval)

	for range ticker.C {
		fe.performCleanup()
	}
}

// performCleanup 执行清理操作
func (fe *FunctionExecutor) performCleanup() {
	fe.logger.Debug("开始执行清理任务")

	// 清理容器池中的空闲容器
	// 容器池有自己的清理逻辑，这里主要是触发清理
	stats := fe.containerPool.GetPoolStats()
	fe.logger.Debug("清理前容器池状态", "stats", stats)

	// 更新容器池使用率指标
	fe.updateContainerPoolMetrics()

	fe.logger.Debug("清理任务完成")
}

// updateContainerPoolMetrics 更新容器池指标
func (fe *FunctionExecutor) updateContainerPoolMetrics() {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()

	stats := fe.containerPool.GetPoolStats()
	if totalContainers, ok := stats["total_containers"].(int); ok {
		fe.metrics.ContainerPoolUsage = totalContainers
	}
}

// cleanupContainer 清理容器
func (fe *FunctionExecutor) cleanupContainer(containerInfo *container.Container) {
	if containerInfo == nil {
		return
	}

	fe.logger.Debug("清理容器", "container_id", containerInfo.ID)

	// 尝试将容器返回到池中复用
	// 如果池已满或容器状态不佳，容器池会自动销毁容器
	fe.containerPool.ReturnContainer(containerInfo, "unknown") // 运行时信息可能已丢失

	fe.logger.Debug("容器清理完成", "container_id", containerInfo.ID)
}

// runFunction 运行函数的核心逻辑
func (fe *FunctionExecutor) runFunction(ctx context.Context, execCtx *ExecutionContext) (interface{}, error) {
	request := execCtx.Request
	containerInfo := execCtx.Container

	fe.logger.Debug("开始执行函数", "function_id", request.FunctionID,
		"container_id", containerInfo.ID, "runtime", request.Runtime)

	execCtx.addLog("开始执行函数")

	// 根据运行时构建执行命令
	cmd, err := fe.buildExecutionCommand(request)
	if err != nil {
		return nil, fmt.Errorf("构建执行命令失败: %w", err)
	}

	execCtx.addLog(fmt.Sprintf("执行命令: %v", cmd))

	// 在容器中执行命令
	result, err := fe.containerManager.ExecCommand(ctx, containerInfo.ID, cmd)
	if err != nil {
		execCtx.addLog(fmt.Sprintf("命令执行失败: %v", err))
		return nil, fmt.Errorf("函数执行失败: %w", err)
	}

	execCtx.addLog("函数执行完成")

	// 解析执行结果 - 使用 result.Stdout 而不是整个 result 对象
	return fe.parseExecutionResult(result.Stdout, request.Runtime)
}

// buildExecutionCommand 构建执行命令
func (fe *FunctionExecutor) buildExecutionCommand(request *FunctionRequest) ([]string, error) {
	switch request.Runtime {
	case "nodejs":
		return fe.buildNodeJSCommand(request)
	case "python":
		return fe.buildPythonCommand(request)
	case "go":
		return fe.buildGoCommand(request)
	case "java":
		return fe.buildJavaCommand(request)
	default:
		return nil, fmt.Errorf("不支持的运行时: %s", request.Runtime)
	}
}

// buildNodeJSCommand 构建 Node.js 执行命令
func (fe *FunctionExecutor) buildNodeJSCommand(request *FunctionRequest) ([]string, error) {
	// 创建执行脚本
	script := fmt.Sprintf(`
const fs = require('fs');

// 读取函数代码
const functionCode = fs.readFileSync('/tmp/function.js', 'utf8');

// 读取参数
let params = {};
try {
    if (fs.existsSync('/tmp/params.json')) {
        params = JSON.parse(fs.readFileSync('/tmp/params.json', 'utf8'));
    }
} catch (e) {
    console.error('参数解析失败:', e.message);
}

// 执行函数
try {
    const func = new Function('params', functionCode + '; return ' + (%s || 'main') + '(params);');
    const result = func(params);

    // 处理 Promise
    if (result && typeof result.then === 'function') {
        result.then(res => {
            console.log(JSON.stringify({success: true, result: res}));
        }).catch(err => {
            console.log(JSON.stringify({success: false, error: err.message}));
        });
    } else {
        console.log(JSON.stringify({success: true, result: result}));
    }
} catch (error) {
    console.log(JSON.stringify({success: false, error: error.message}));
}
`, request.Handler)

	return []string{"node", "-e", script}, nil
}

// buildPythonCommand 构建 Python 执行命令
func (fe *FunctionExecutor) buildPythonCommand(request *FunctionRequest) ([]string, error) {
	script := fmt.Sprintf(`
import json
import sys
import traceback

# 读取函数代码
with open('/tmp/function.py', 'r') as f:
    function_code = f.read()

# 读取参数
params = {}
try:
    with open('/tmp/params.json', 'r') as f:
        params = json.load(f)
except FileNotFoundError:
    pass
except Exception as e:
    print(json.dumps({"success": False, "error": f"参数解析失败: {str(e)}"}))
    sys.exit(1)

# 执行函数
try:
    # 创建执行环境
    exec_globals = {}
    exec(function_code, exec_globals)

    # 调用处理函数
    handler_name = %q or 'main'
    if handler_name in exec_globals:
        result = exec_globals[handler_name](params)
        print(json.dumps({"success": True, "result": result}))
    else:
        print(json.dumps({"success": False, "error": f"找不到处理函数: {handler_name}"}))
except Exception as e:
    error_msg = traceback.format_exc()
    print(json.dumps({"success": False, "error": error_msg}))
`, request.Handler)

	return []string{"python3", "-c", script}, nil
}

// buildGoCommand 构建 Go 执行命令
func (fe *FunctionExecutor) buildGoCommand(request *FunctionRequest) ([]string, error) {
	// Go 需要编译，这里简化处理
	script := fmt.Sprintf(`
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)

%s

func main() {
	// 读取参数
	var params map[string]interface{}
	if data, err := ioutil.ReadFile("/tmp/params.json"); err == nil {
		json.Unmarshal(data, &params)
	}

	// 调用处理函数
	result, err := %s(params)
	if err != nil {
		response := map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}
		json.NewEncoder(os.Stdout).Encode(response)
	} else {
		response := map[string]interface{}{
			"success": true,
			"result":  result,
		}
		json.NewEncoder(os.Stdout).Encode(response)
	}
}
`, request.Code, request.Handler)

	// 返回编译和执行命令
	return []string{"sh", "-c", "cd /tmp && echo '" + script + "' > main.go && go run main.go"}, nil
}

// buildJavaCommand 构建 Java 执行命令
func (fe *FunctionExecutor) buildJavaCommand(request *FunctionRequest) ([]string, error) {
	// Java 需要编译，这里简化处理
	className := "Function"
	if request.Handler != "" {
		className = request.Handler
	}

	javaCode := fmt.Sprintf(`
import java.io.*;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;

public class %s {
    %s

    public static void main(String[] args) {
        try {
            // 读取参数
            Map<String, Object> params = new HashMap<>();
            File paramsFile = new File("/tmp/params.json");
            if (paramsFile.exists()) {
                ObjectMapper mapper = new ObjectMapper();
                params = mapper.readValue(paramsFile, Map.class);
            }

            // 调用处理函数
            Object result = handle(params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("result", result);

            ObjectMapper mapper = new ObjectMapper();
            System.out.println(mapper.writeValueAsString(response));

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());

            try {
                ObjectMapper mapper = new ObjectMapper();
                System.out.println(mapper.writeValueAsString(response));
            } catch (Exception ex) {
                System.out.println("{\"success\":false,\"error\":\"" + ex.getMessage() + "\"}");
            }
        }
    }
}
`, className, request.Code)

	return []string{"sh", "-c", fmt.Sprintf("cd /tmp && echo '%s' > %s.java && javac %s.java && java %s",
		javaCode, className, className, className)}, nil
}

// parseExecutionResult 解析执行结果
func (fe *FunctionExecutor) parseExecutionResult(output string, runtime string) (interface{}, error) {
	// 尝试解析 JSON 格式的结果
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(output), &result); err != nil {
		// 如果不是 JSON 格式，直接返回原始输出
		return output, nil
	}

	// 检查执行是否成功
	if success, ok := result["success"].(bool); ok && success {
		return result["result"], nil
	} else if errorMsg, ok := result["error"].(string); ok {
		return nil, fmt.Errorf("函数执行错误: %s", errorMsg)
	}

	return result, nil
}

// addLog 添加日志到执行上下文
func (ec *ExecutionContext) addLog(message string) {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()
	ec.Logs = append(ec.Logs, fmt.Sprintf("[%s] %s", time.Now().Format("15:04:05"), message))
}
