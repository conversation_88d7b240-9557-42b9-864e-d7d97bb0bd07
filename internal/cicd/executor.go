package cicd

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// BuildExecutor 构建执行器接口
type BuildExecutor interface {
	// ExecuteBuild 执行构建任务
	ExecuteBuild(ctx context.Context, build *Build) error
	
	// ExecuteStage 执行构建阶段
	ExecuteStage(ctx context.Context, stage *BuildStage, build *Build) error
	
	// ExecuteStep 执行构建步骤
	ExecuteStep(ctx context.Context, step *BuildStep, stage *BuildStage, build *Build) error
	
	// CancelBuild 取消构建
	CancelBuild(ctx context.Context, buildID string) error
}

// DefaultBuildExecutor 默认构建执行器实现
type DefaultBuildExecutor struct {
	service       Service
	logger        Logger
	workspaceBase string
	dockerClient  DockerClient
	config        *BuildExecutorConfig
}

// BuildExecutorConfig 构建执行器配置
type BuildExecutorConfig struct {
	LocalBuildMode bool   `json:"local_build_mode"` // 本地构建模式
	WorkspaceDir   string `json:"workspace_dir"`    // 工作空间目录
	RegistryURL    string `json:"registry_url"`     // 镜像仓库地址
}

// NewBuildExecutor 创建构建执行器
func NewBuildExecutor(service Service, logger Logger, workspaceBase string) BuildExecutor {
	dockerClient, _ := NewDockerClient(logger)

	return &DefaultBuildExecutor{
		service:       service,
		logger:        logger,
		workspaceBase: workspaceBase,
		dockerClient:  dockerClient,
		config: &BuildExecutorConfig{
			LocalBuildMode: false, // 默认非本地模式
			WorkspaceDir:   workspaceBase,
		},
	}
}

// NewLocalBuildExecutor 创建本地构建执行器
func NewLocalBuildExecutor(service Service, logger Logger, workspaceBase string) BuildExecutor {
	dockerClient, _ := NewDockerClient(logger)

	return &DefaultBuildExecutor{
		service:       service,
		logger:        logger,
		workspaceBase: workspaceBase,
		dockerClient:  dockerClient,
		config: &BuildExecutorConfig{
			LocalBuildMode: true, // 本地构建模式
			WorkspaceDir:   workspaceBase,
		},
	}
}

// ExecuteBuild 执行构建任务
func (e *DefaultBuildExecutor) ExecuteBuild(ctx context.Context, build *Build) error {
	e.logger.Info("开始执行构建", "build_id", build.ID, "number", build.Number)
	
	// 更新构建状态为运行中
	if err := e.service.UpdateBuildStatus(ctx, build.ID, BuildStatusRunning, "开始执行构建..."); err != nil {
		return fmt.Errorf("更新构建状态失败: %w", err)
	}
	
	// 创建构建工作空间
	workspace := e.createWorkspace(build)
	defer e.cleanupWorkspace(workspace)
	
	// 获取构建详情（包含阶段和步骤）
	buildDetail, err := e.service.GetBuild(ctx, build.ID)
	if err != nil {
		return fmt.Errorf("获取构建详情失败: %w", err)
	}
	
	// 执行构建阶段
	for _, stage := range buildDetail.Stages {
		if err := e.ExecuteStage(ctx, &stage, buildDetail); err != nil {
			e.logger.Error("执行构建阶段失败", "error", err, "stage_id", stage.ID, "build_id", build.ID)
			e.service.UpdateBuildStatus(ctx, build.ID, BuildStatusFailed, fmt.Sprintf("阶段 '%s' 执行失败: %v", stage.Name, err))
			return err
		}
	}
	
	// 构建成功
	e.service.UpdateBuildStatus(ctx, build.ID, BuildStatusSuccess, "构建完成")
	e.logger.Info("构建执行完成", "build_id", build.ID)
	return nil
}

// ExecuteStage 执行构建阶段
func (e *DefaultBuildExecutor) ExecuteStage(ctx context.Context, stage *BuildStage, build *Build) error {
	e.logger.Info("开始执行阶段", "stage_id", stage.ID, "name", stage.Name, "build_id", build.ID)
	
	// 更新阶段状态为运行中
	startTime := time.Now()
	e.service.UpdateStageStatus(ctx, stage.ID, BuildStageStatusRunning, "开始执行阶段...")
	
	// 执行阶段中的步骤
	for _, step := range stage.Steps {
		if err := e.ExecuteStep(ctx, &step, stage, build); err != nil {
			// 阶段失败
			endTime := time.Now()
			duration := int(endTime.Sub(startTime).Seconds())
			
			// 更新阶段状态
			e.updateStageCompletion(stage.ID, BuildStageStatusFailed, endTime, duration, err.Error())
			return fmt.Errorf("步骤 '%s' 执行失败: %w", step.Name, err)
		}
	}
	
	// 阶段成功
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	e.updateStageCompletion(stage.ID, BuildStageStatusSuccess, endTime, duration, "")
	
	e.logger.Info("构建阶段完成", "stage_id", stage.ID, "name", stage.Name, "duration", duration)
	return nil
}

// ExecuteStep 执行构建步骤
func (e *DefaultBuildExecutor) ExecuteStep(ctx context.Context, step *BuildStep, stage *BuildStage, build *Build) error {
	e.logger.Info("开始执行步骤", "step_id", step.ID, "name", step.Name, "type", step.Type)
	
	// 更新步骤状态为运行中
	startTime := time.Now()
	e.service.UpdateStepStatus(ctx, step.ID, BuildStepStatusRunning, "开始执行步骤...", 0)
	
	// 根据步骤类型执行具体逻辑
	var err error
	var exitCode int
	var logOutput string
	
	switch step.Type {
	case "git_checkout":
		err, exitCode, logOutput = e.executeGitCheckout(ctx, step, build)
	case "shell":
		err, exitCode, logOutput = e.executeShell(ctx, step, build)
	case "docker_build":
		err, exitCode, logOutput = e.executeDockerBuild(ctx, step, build)
	case "docker_push":
		err, exitCode, logOutput = e.executeDockerPush(ctx, step, build)
	case "deploy":
		err, exitCode, logOutput = e.executeDeploy(ctx, step, build)
	case "cache":
		err, exitCode, logOutput = e.executeCache(ctx, step, build)
	case "setup_python":
		err, exitCode, logOutput = e.executeSetupPython(ctx, step, build)
	default:
		err = fmt.Errorf("不支持的步骤类型: %s", step.Type)
		exitCode = 1
		logOutput = fmt.Sprintf("错误: %v", err)
	}
	
	// 更新步骤状态
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	
	var status BuildStepStatus
	if err != nil {
		status = BuildStepStatusFailed
		e.logger.Error("构建步骤失败", "error", err, "step_id", step.ID, "exit_code", exitCode)
	} else {
		status = BuildStepStatusSuccess
		e.logger.Info("构建步骤完成", "step_id", step.ID, "duration", duration)
	}
	
	e.updateStepCompletion(step.ID, status, endTime, duration, logOutput, exitCode)
	
	return err
}

// CancelBuild 取消构建
func (e *DefaultBuildExecutor) CancelBuild(ctx context.Context, buildID string) error {
	e.logger.Info("取消构建", "build_id", buildID)
	
	// TODO: 实现构建取消逻辑
	// 1. 停止正在运行的进程
	// 2. 清理工作空间
	// 3. 更新构建状态
	
	return e.service.CancelBuild(ctx, buildID)
}

// isLocalBuildMode 检查是否为本地构建模式
func (e *DefaultBuildExecutor) isLocalBuildMode() bool {
	if e.config != nil {
		return e.config.LocalBuildMode
	}
	return false
}

// 私有辅助方法

// createWorkspace 创建构建工作空间
func (e *DefaultBuildExecutor) createWorkspace(build *Build) string {
	workspace := filepath.Join(e.workspaceBase, fmt.Sprintf("build-%s", build.ID))
	os.MkdirAll(workspace, 0755)
	e.logger.Debug("创建构建工作空间", "workspace", workspace, "build_id", build.ID)
	return workspace
}

// cleanupWorkspace 清理构建工作空间
func (e *DefaultBuildExecutor) cleanupWorkspace(workspace string) {
	if err := os.RemoveAll(workspace); err != nil {
		e.logger.Error("清理工作空间失败", "error", err, "workspace", workspace)
	} else {
		e.logger.Debug("清理工作空间成功", "workspace", workspace)
	}
}

// updateStageCompletion 更新阶段完成状态
func (e *DefaultBuildExecutor) updateStageCompletion(stageID string, status BuildStageStatus, endTime time.Time, duration int, errorMsg string) {
	// TODO: 直接更新数据库，避免通过 service 层
	// 这里暂时使用 service 层方法
	ctx := context.Background()
	logMsg := fmt.Sprintf("阶段完成，状态: %s，耗时: %d秒", status, duration)
	if errorMsg != "" {
		logMsg += fmt.Sprintf("，错误: %s", errorMsg)
	}
	e.service.UpdateStageStatus(ctx, stageID, status, logMsg)
}

// updateStepCompletion 更新步骤完成状态
func (e *DefaultBuildExecutor) updateStepCompletion(stepID string, status BuildStepStatus, endTime time.Time, duration int, logOutput string, exitCode int) {
	// TODO: 直接更新数据库，避免通过 service 层
	// 这里暂时使用 service 层方法
	ctx := context.Background()
	logMsg := fmt.Sprintf("步骤完成，状态: %s，耗时: %d秒，退出码: %d", status, duration, exitCode)
	if logOutput != "" {
		logMsg += fmt.Sprintf("\n输出:\n%s", logOutput)
	}
	e.service.UpdateStepStatus(ctx, stepID, status, logMsg, exitCode)
}

// executeGitCheckout 执行 Git 检出
func (e *DefaultBuildExecutor) executeGitCheckout(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行 Git 检出", "step_id", step.ID, "commit", build.CommitHash)
	
	// TODO: 实现 Git 检出逻辑
	// 1. 克隆仓库
	// 2. 检出指定提交
	// 3. 处理子模块
	
	// 模拟执行
	time.Sleep(2 * time.Second)
	
	logOutput := fmt.Sprintf("Git 检出完成\n提交: %s\n分支: %s\n作者: %s", 
		build.CommitHash, build.Branch, build.Author)
	
	return nil, 0, logOutput
}

// executeShell 执行 Shell 命令
func (e *DefaultBuildExecutor) executeShell(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行 Shell 命令", "step_id", step.ID)
	
	// TODO: 从步骤配置中获取命令
	// 这里使用模拟命令
	commands := []string{"echo 'Hello World'", "ls -la", "pwd"}
	
	var output strings.Builder
	var lastExitCode int
	
	for _, cmd := range commands {
		e.logger.Debug("执行命令", "command", cmd)
		
		// 执行命令
		execCmd := exec.CommandContext(ctx, "sh", "-c", cmd)
		cmdOutput, err := execCmd.CombinedOutput()
		
		output.WriteString(fmt.Sprintf("$ %s\n", cmd))
		output.Write(cmdOutput)
		output.WriteString("\n")
		
		if err != nil {
			if exitError, ok := err.(*exec.ExitError); ok {
				lastExitCode = exitError.ExitCode()
			} else {
				lastExitCode = 1
			}
			return fmt.Errorf("命令执行失败: %w", err), lastExitCode, output.String()
		}
	}
	
	return nil, 0, output.String()
}

// executeDockerBuild 执行 Docker 构建
func (e *DefaultBuildExecutor) executeDockerBuild(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行 Docker 构建", "step_id", step.ID)

	// 解析步骤配置
	config := step.Config
	if config == nil {
		return fmt.Errorf("Docker 构建步骤缺少配置"), 1, "错误: 缺少构建配置"
	}

	// 构建镜像标签
	imageTags := config.Tags
	if len(imageTags) == 0 {
		// 使用默认标签
		imageTags = []string{fmt.Sprintf("%s:%s", build.AppID, build.CommitHash[:8])}
	}

	// 构建选项
	buildOptions := BuildImageOptions{
		Dockerfile: config.Dockerfile,
		Tags:       imageTags,
		BuildArgs:  config.BuildArgs,
		NoCache:    false, // 开发环境启用缓存
		Pull:       false, // 开发环境不总是拉取
	}

	// 创建构建上下文
	buildContext, err := CreateBuildContext(config.Context, config.Dockerfile)
	if err != nil {
		logOutput := fmt.Sprintf("创建构建上下文失败: %v", err)
		return err, 1, logOutput
	}

	// 执行构建
	if err := e.dockerClient.BuildImage(ctx, buildContext, buildOptions); err != nil {
		logOutput := fmt.Sprintf("Docker 镜像构建失败: %v", err)
		return err, 1, logOutput
	}

	// 更新构建记录中的镜像标签
	if len(imageTags) > 0 {
		build.ImageTag = imageTags[0]
	}

	logOutput := fmt.Sprintf("Docker 镜像构建完成\n镜像标签: %v\n构建时间: %s",
		imageTags, time.Now().Format(time.RFC3339))

	e.logger.Info("Docker 构建完成", "step_id", step.ID, "tags", imageTags)
	return nil, 0, logOutput
}

// executeDockerPush 执行 Docker 推送
func (e *DefaultBuildExecutor) executeDockerPush(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行 Docker 推送", "step_id", step.ID)

	// 检查是否为本地构建模式
	if e.isLocalBuildMode() {
		e.logger.Info("本地构建模式，跳过镜像推送", "step_id", step.ID)
		logOutput := fmt.Sprintf("本地构建模式，跳过镜像推送\n镜像标签: %s\n跳过时间: %s",
			build.ImageTag, time.Now().Format(time.RFC3339))
		return nil, 0, logOutput
	}

	// 解析步骤配置
	config := step.Config
	if config == nil {
		return fmt.Errorf("Docker 推送步骤缺少配置"), 1, "错误: 缺少推送配置"
	}

	// 获取要推送的镜像标签
	imageTags := config.Tags
	if len(imageTags) == 0 && build.ImageTag != "" {
		imageTags = []string{build.ImageTag}
	}

	if len(imageTags) == 0 {
		return fmt.Errorf("没有找到要推送的镜像标签"), 1, "错误: 没有镜像标签"
	}

	// 推送选项
	pushOptions := PushImageOptions{
		Registry: RegistryAuth{
			Username:      config.Registry.Username,
			Password:      config.Registry.Password,
			ServerAddress: config.Registry.URL,
		},
		LocalOnly: e.isLocalBuildMode(), // 根据构建模式设置
	}

	var logOutput strings.Builder
	logOutput.WriteString("Docker 镜像推送结果:\n")

	// 推送所有标签
	for _, tag := range imageTags {
		if err := e.dockerClient.PushImage(ctx, tag, pushOptions); err != nil {
			errorMsg := fmt.Sprintf("推送镜像 %s 失败: %v", tag, err)
			logOutput.WriteString(errorMsg + "\n")
			return err, 1, logOutput.String()
		}
		logOutput.WriteString(fmt.Sprintf("✓ 推送成功: %s\n", tag))
	}

	logOutput.WriteString(fmt.Sprintf("推送完成时间: %s", time.Now().Format(time.RFC3339)))

	e.logger.Info("Docker 推送完成", "step_id", step.ID, "tags", imageTags)
	return nil, 0, logOutput.String()
}

// executeDeploy 执行部署
func (e *DefaultBuildExecutor) executeDeploy(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行部署", "step_id", step.ID)
	
	// TODO: 实现部署逻辑
	// 1. 根据部署策略执行部署
	// 2. 更新服务配置
	// 3. 健康检查
	
	// 模拟执行
	time.Sleep(4 * time.Second)
	
	logOutput := fmt.Sprintf("应用部署完成\n镜像: %s\n部署时间: %s", 
		build.ImageTag, time.Now().Format(time.RFC3339))
	
	return nil, 0, logOutput
}

// executeCache 执行缓存操作
func (e *DefaultBuildExecutor) executeCache(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行缓存操作", "step_id", step.ID)
	
	// TODO: 实现缓存逻辑
	// 1. 恢复缓存
	// 2. 保存缓存
	// 3. 缓存键计算
	
	// 模拟执行
	time.Sleep(1 * time.Second)
	
	logOutput := "缓存操作完成\n缓存键: deps-abc123\n缓存大小: 150MB"
	
	return nil, 0, logOutput
}

// executeSetupPython 执行 Python 环境设置
func (e *DefaultBuildExecutor) executeSetupPython(ctx context.Context, step *BuildStep, build *Build) (error, int, string) {
	e.logger.Info("执行 Python 环境设置", "step_id", step.ID)
	
	// TODO: 实现 Python 环境设置逻辑
	// 1. 安装指定版本的 Python
	// 2. 设置虚拟环境
	// 3. 安装依赖包
	
	// 模拟执行
	time.Sleep(2 * time.Second)
	
	logOutput := "Python 环境设置完成\nPython 版本: 3.11\n虚拟环境: /tmp/venv"
	
	return nil, 0, logOutput
}
