package cicd

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service CI/CD 服务接口
type Service interface {
	// 流水线管理
	CreatePipeline(ctx context.Context, req *CreatePipelineRequest) (*Pipeline, error)
	GetPipeline(ctx context.Context, pipelineID string) (*Pipeline, error)
	ListPipelines(ctx context.Context, appID string) ([]*Pipeline, error)
	UpdatePipeline(ctx context.Context, pipelineID string, req *UpdatePipelineRequest) (*Pipeline, error)
	DeletePipeline(ctx context.Context, pipelineID string) error
	
	// 构建管理
	CreateBuild(ctx context.Context, req *CreateBuildRequest) (*Build, error)
	GetBuild(ctx context.Context, buildID string) (*Build, error)
	ListBuilds(ctx context.Context, filter *BuildFilter) ([]*Build, int64, error)
	CancelBuild(ctx context.Context, buildID string) error
	RetryBuild(ctx context.Context, buildID string) (*Build, error)
	
	// 构建执行
	ExecuteBuild(ctx context.Context, buildID string) error
	UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error
	UpdateStageStatus(ctx context.Context, stageID string, status BuildStageStatus, log string) error
	UpdateStepStatus(ctx context.Context, stepID string, status BuildStepStatus, log string, exitCode int) error
	
	// 部署管理
	CreateDeployment(ctx context.Context, req *CreateDeploymentRequest) (*Deployment, error)
	GetDeployment(ctx context.Context, deploymentID string) (*Deployment, error)
	ListDeployments(ctx context.Context, filter *DeploymentFilter) ([]*Deployment, int64, error)
	RollbackDeployment(ctx context.Context, deploymentID string) error
	
	// 环境管理
	CreateEnvironment(ctx context.Context, req *CreateEnvironmentRequest) (*Environment, error)
	GetEnvironment(ctx context.Context, envID string) (*Environment, error)
	ListEnvironments(ctx context.Context, tenantID string) ([]*Environment, error)
	
	// Webhook 处理
	HandleWebhook(ctx context.Context, source string, payload interface{}) error

	// 构建执行器管理
	SetBuildExecutor(executor BuildExecutor)

	// 构建节点管理
	RegisterBuildNode(ctx context.Context, req *RegisterNodeRequest) (*BuildNode, error)
	GetBuildNode(ctx context.Context, nodeID string) (*BuildNode, error)
	ListBuildNodes(ctx context.Context) ([]*BuildNode, error)
	UpdateNodeStatus(ctx context.Context, nodeID string, status NodeStatus) error
}

// CICDService CI/CD 服务实现
type CICDService struct {
	db            *gorm.DB
	logger        Logger
	buildExecutor BuildExecutor
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// NewCICDService 创建 CI/CD 服务
func NewCICDService(db *gorm.DB, logger Logger) Service {
	return &CICDService{
		db:     db,
		logger: logger,
	}
}

// SetBuildExecutor 设置构建执行器
func (s *CICDService) SetBuildExecutor(executor BuildExecutor) {
	s.buildExecutor = executor
}

// CreatePipeline 创建流水线
func (s *CICDService) CreatePipeline(ctx context.Context, req *CreatePipelineRequest) (*Pipeline, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查应用是否存在流水线
	var existingPipeline Pipeline
	err := s.db.Where("app_id = ? AND name = ?", req.AppID, req.Name).First(&existingPipeline).Error
	if err == nil {
		return nil, fmt.Errorf("应用已存在名为 '%s' 的流水线", req.Name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查流水线失败: %w", err)
	}
	
	// 创建流水线
	pipeline := &Pipeline{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		AppID:       req.AppID,
		Config:      req.Config,
		Status:      PipelineStatusActive,
		TenantID:    req.TenantID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	if err := s.db.Create(pipeline).Error; err != nil {
		return nil, fmt.Errorf("创建流水线失败: %w", err)
	}
	
	s.logger.Info("流水线创建成功", "pipeline_id", pipeline.ID, "name", pipeline.Name, "app_id", req.AppID)
	return pipeline, nil
}

// GetPipeline 获取流水线详情
func (s *CICDService) GetPipeline(ctx context.Context, pipelineID string) (*Pipeline, error) {
	var pipeline Pipeline
	err := s.db.Preload("Builds").First(&pipeline, "id = ?", pipelineID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("流水线不存在: %s", pipelineID)
		}
		return nil, fmt.Errorf("获取流水线失败: %w", err)
	}
	return &pipeline, nil
}

// ListPipelines 获取流水线列表
func (s *CICDService) ListPipelines(ctx context.Context, appID string) ([]*Pipeline, error) {
	var pipelines []*Pipeline
	err := s.db.Where("app_id = ? AND status != ?", appID, PipelineStatusDeleted).
		Order("created_at DESC").Find(&pipelines).Error
	if err != nil {
		return nil, fmt.Errorf("获取流水线列表失败: %w", err)
	}
	return pipelines, nil
}

// CreateBuild 创建构建任务
func (s *CICDService) CreateBuild(ctx context.Context, req *CreateBuildRequest) (*Build, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 获取流水线信息
	pipeline, err := s.GetPipeline(ctx, req.PipelineID)
	if err != nil {
		return nil, err
	}
	
	// 生成构建编号
	var lastBuild Build
	s.db.Where("pipeline_id = ?", req.PipelineID).Order("number DESC").First(&lastBuild)
	buildNumber := lastBuild.Number + 1
	
	// 创建构建任务
	build := &Build{
		ID:          uuid.New().String(),
		PipelineID:  req.PipelineID,
		AppID:       pipeline.AppID,
		Number:      buildNumber,
		Branch:      req.Branch,
		CommitHash:  req.CommitHash,
		CommitMsg:   req.CommitMsg,
		Author:      req.Author,
		Status:      BuildStatusPending,
		TriggerType: req.TriggerType,
		TriggerBy:   req.TriggerBy,
		StartTime:   time.Now(),
	}
	
	if err := s.db.Create(build).Error; err != nil {
		return nil, fmt.Errorf("创建构建任务失败: %w", err)
	}
	
	// 创建构建阶段和步骤
	if err := s.createBuildStages(ctx, build, pipeline.Config.Spec.Stages); err != nil {
		s.logger.Error("创建构建阶段失败", "error", err, "build_id", build.ID)
	}
	
	// 将构建任务加入队列
	if err := s.enqueueBuild(ctx, build.ID, req.Priority); err != nil {
		s.logger.Error("构建任务入队失败", "error", err, "build_id", build.ID)
	}
	
	s.logger.Info("构建任务创建成功", "build_id", build.ID, "number", buildNumber, "app_id", pipeline.AppID)
	return build, nil
}

// GetBuild 获取构建详情
func (s *CICDService) GetBuild(ctx context.Context, buildID string) (*Build, error) {
	var build Build
	err := s.db.Preload("Pipeline").Preload("Stages.Steps").First(&build, "id = ?", buildID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("构建任务不存在: %s", buildID)
		}
		return nil, fmt.Errorf("获取构建任务失败: %w", err)
	}
	return &build, nil
}

// ListBuilds 获取构建列表
func (s *CICDService) ListBuilds(ctx context.Context, filter *BuildFilter) ([]*Build, int64, error) {
	query := s.db.Model(&Build{})
	
	// 应用过滤条件
	if filter != nil {
		if filter.AppID != "" {
			query = query.Where("app_id = ?", filter.AppID)
		}
		if filter.PipelineID != "" {
			query = query.Where("pipeline_id = ?", filter.PipelineID)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Branch != "" {
			query = query.Where("branch = ?", filter.Branch)
		}
		if filter.TriggerType != "" {
			query = query.Where("trigger_type = ?", filter.TriggerType)
		}
	}
	
	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取构建总数失败: %w", err)
	}
	
	// 分页查询
	var builds []*Build
	offset := 0
	limit := 20
	if filter != nil {
		if filter.Page > 0 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		if filter.PageSize > 0 && filter.PageSize <= 100 {
			limit = filter.PageSize
		}
	}
	
	err := query.Preload("Pipeline").Offset(offset).Limit(limit).
		Order("start_time DESC").Find(&builds).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取构建列表失败: %w", err)
	}
	
	return builds, total, nil
}

// CancelBuild 取消构建
func (s *CICDService) CancelBuild(ctx context.Context, buildID string) error {
	// 获取构建任务
	build, err := s.GetBuild(ctx, buildID)
	if err != nil {
		return err
	}
	
	// 检查构建状态
	if build.Status != BuildStatusPending && build.Status != BuildStatusRunning {
		return fmt.Errorf("构建任务状态为 %s，无法取消", build.Status)
	}
	
	// 更新构建状态
	endTime := time.Now()
	duration := int(endTime.Sub(build.StartTime).Seconds())
	
	err = s.db.Model(build).Updates(map[string]interface{}{
		"status":   BuildStatusCanceled,
		"end_time": &endTime,
		"duration": duration,
	}).Error
	if err != nil {
		return fmt.Errorf("取消构建失败: %w", err)
	}
	
	// 取消所有运行中的阶段和步骤
	s.db.Model(&BuildStage{}).Where("build_id = ? AND status IN ?", 
		buildID, []BuildStageStatus{BuildStageStatusPending, BuildStageStatusRunning}).
		Update("status", BuildStageStatusCanceled)
		
	s.db.Model(&BuildStep{}).Where("stage_id IN (SELECT id FROM build_stages WHERE build_id = ?) AND status IN ?",
		buildID, []BuildStepStatus{BuildStepStatusPending, BuildStepStatusRunning}).
		Update("status", BuildStepStatusCanceled)
	
	// 从构建队列中移除
	s.db.Model(&BuildQueue{}).Where("build_id = ?", buildID).Update("status", QueueStatusCanceled)
	
	s.logger.Info("构建任务已取消", "build_id", buildID)
	return nil
}

// createBuildStages 创建构建阶段
func (s *CICDService) createBuildStages(ctx context.Context, build *Build, stages []Stage) error {
	for _, stageConfig := range stages {
		stage := &BuildStage{
			ID:      uuid.New().String(),
			BuildID: build.ID,
			Name:    stageConfig.Name,
			Status:  BuildStageStatusPending,
		}
		
		if err := s.db.Create(stage).Error; err != nil {
			return fmt.Errorf("创建构建阶段失败: %w", err)
		}
		
		// 创建构建步骤
		for _, stepConfig := range stageConfig.Steps {
			step := &BuildStep{
				ID:      uuid.New().String(),
				StageID: stage.ID,
				Name:    stepConfig.Name,
				Type:    stepConfig.Type,
				Status:  BuildStepStatusPending,
			}
			
			if err := s.db.Create(step).Error; err != nil {
				return fmt.Errorf("创建构建步骤失败: %w", err)
			}
		}
	}
	
	return nil
}

// enqueueBuild 将构建任务加入队列
func (s *CICDService) enqueueBuild(ctx context.Context, buildID string, priority int) error {
	queue := &BuildQueue{
		ID:        uuid.New().String(),
		BuildID:   buildID,
		Priority:  priority,
		Status:    QueueStatusWaiting,
		CreatedAt: time.Now(),
	}
	
	if err := s.db.Create(queue).Error; err != nil {
		return fmt.Errorf("构建任务入队失败: %w", err)
	}
	
	s.logger.Info("构建任务已入队", "build_id", buildID, "priority", priority)
	return nil
}

// ExecuteBuild 执行构建
func (s *CICDService) ExecuteBuild(ctx context.Context, buildID string) error {
	// 获取构建任务
	build, err := s.GetBuild(ctx, buildID)
	if err != nil {
		return err
	}

	// 如果有构建执行器，使用执行器执行构建
	if s.buildExecutor != nil {
		return s.buildExecutor.ExecuteBuild(ctx, build)
	}

	// 否则使用默认的执行逻辑
	// 更新构建状态为运行中
	if err := s.UpdateBuildStatus(ctx, buildID, BuildStatusRunning, "开始执行构建..."); err != nil {
		return err
	}

	// 执行构建阶段
	for _, stage := range build.Stages {
		if err := s.executeStage(ctx, &stage, build); err != nil {
			s.logger.Error("执行构建阶段失败", "error", err, "stage_id", stage.ID, "build_id", buildID)
			s.UpdateBuildStatus(ctx, buildID, BuildStatusFailed, fmt.Sprintf("阶段 '%s' 执行失败: %v", stage.Name, err))
			return err
		}
	}

	// 构建成功
	s.UpdateBuildStatus(ctx, buildID, BuildStatusSuccess, "构建完成")
	s.logger.Info("构建执行完成", "build_id", buildID)
	return nil
}

// executeStage 执行构建阶段
func (s *CICDService) executeStage(ctx context.Context, stage *BuildStage, build *Build) error {
	// 更新阶段状态为运行中
	startTime := time.Now()
	s.UpdateStageStatus(ctx, stage.ID, BuildStageStatusRunning, "开始执行阶段...")
	s.db.Model(stage).Update("start_time", &startTime)
	
	// 执行阶段中的步骤
	for _, step := range stage.Steps {
		if err := s.executeStep(ctx, &step, stage, build); err != nil {
			// 阶段失败
			endTime := time.Now()
			duration := int(endTime.Sub(startTime).Seconds())
			s.db.Model(stage).Updates(map[string]interface{}{
				"status":   BuildStageStatusFailed,
				"end_time": &endTime,
				"duration": duration,
				"error_msg": err.Error(),
			})
			return err
		}
	}
	
	// 阶段成功
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	s.db.Model(stage).Updates(map[string]interface{}{
		"status":   BuildStageStatusSuccess,
		"end_time": &endTime,
		"duration": duration,
	})
	
	s.logger.Info("构建阶段完成", "stage_id", stage.ID, "name", stage.Name)
	return nil
}

// executeStep 执行构建步骤
func (s *CICDService) executeStep(ctx context.Context, step *BuildStep, stage *BuildStage, build *Build) error {
	// 更新步骤状态为运行中
	startTime := time.Now()
	s.UpdateStepStatus(ctx, step.ID, BuildStepStatusRunning, "开始执行步骤...", 0)
	s.db.Model(step).Update("start_time", &startTime)
	
	// TODO: 根据步骤类型执行具体逻辑
	// 这里先模拟执行过程
	time.Sleep(5 * time.Second)
	
	// 步骤成功
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	s.db.Model(step).Updates(map[string]interface{}{
		"status":    BuildStepStatusSuccess,
		"end_time":  &endTime,
		"duration":  duration,
		"exit_code": 0,
	})
	
	s.logger.Info("构建步骤完成", "step_id", step.ID, "name", step.Name)
	return nil
}

// UpdateBuildStatus 更新构建状态
func (s *CICDService) UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error {
	updates := map[string]interface{}{
		"status":    status,
		"build_log": log,
	}
	
	// 如果构建完成，设置结束时间
	if status == BuildStatusSuccess || status == BuildStatusFailed || status == BuildStatusCanceled || status == BuildStatusTimeout {
		endTime := time.Now()
		updates["end_time"] = &endTime
		
		// 计算构建时长
		var build Build
		if err := s.db.First(&build, "id = ?", buildID).Error; err == nil {
			duration := int(endTime.Sub(build.StartTime).Seconds())
			updates["duration"] = duration
		}
	}
	
	err := s.db.Model(&Build{}).Where("id = ?", buildID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新构建状态失败: %w", err)
	}
	
	s.logger.Info("构建状态更新", "build_id", buildID, "status", status)
	return nil
}

// UpdateStageStatus 更新阶段状态
func (s *CICDService) UpdateStageStatus(ctx context.Context, stageID string, status BuildStageStatus, log string) error {
	err := s.db.Model(&BuildStage{}).Where("id = ?", stageID).Updates(map[string]interface{}{
		"status": status,
		"log":    log,
	}).Error
	if err != nil {
		return fmt.Errorf("更新阶段状态失败: %w", err)
	}
	
	s.logger.Debug("阶段状态更新", "stage_id", stageID, "status", status)
	return nil
}

// UpdateStepStatus 更新步骤状态
func (s *CICDService) UpdateStepStatus(ctx context.Context, stepID string, status BuildStepStatus, log string, exitCode int) error {
	err := s.db.Model(&BuildStep{}).Where("id = ?", stepID).Updates(map[string]interface{}{
		"status":    status,
		"log":       log,
		"exit_code": exitCode,
	}).Error
	if err != nil {
		return fmt.Errorf("更新步骤状态失败: %w", err)
	}
	
	s.logger.Debug("步骤状态更新", "step_id", stepID, "status", status, "exit_code", exitCode)
	return nil
}

// RetryBuild 重试构建
func (s *CICDService) RetryBuild(ctx context.Context, buildID string) (*Build, error) {
	// 获取构建任务
	build, err := s.GetBuild(ctx, buildID)
	if err != nil {
		return nil, err
	}

	// 检查构建状态
	if build.Status != BuildStatusFailed && build.Status != BuildStatusCanceled && build.Status != BuildStatusTimeout {
		return nil, fmt.Errorf("构建任务状态为 %s，无法重试", build.Status)
	}

	// 创建新的构建任务
	req := &CreateBuildRequest{
		PipelineID:  build.PipelineID,
		Branch:      build.Branch,
		CommitHash:  build.CommitHash,
		CommitMsg:   build.CommitMsg,
		Author:      build.Author,
		TriggerType: "manual",
		TriggerBy:   build.TriggerBy,
		Priority:    1, // 重试构建优先级较高
	}

	return s.CreateBuild(ctx, req)
}

// UpdatePipeline 更新流水线
func (s *CICDService) UpdatePipeline(ctx context.Context, pipelineID string, req *UpdatePipelineRequest) (*Pipeline, error) {
	// 获取现有流水线
	pipeline, err := s.GetPipeline(ctx, pipelineID)
	if err != nil {
		return nil, err
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if req.Description != nil {
		updates["description"] = *req.Description
	}

	if req.Config != nil {
		// 验证配置
		if err := req.Config.Validate(); err != nil {
			return nil, fmt.Errorf("流水线配置验证失败: %w", err)
		}
		updates["config"] = *req.Config
	}

	if req.Status != nil {
		updates["status"] = *req.Status
	}

	updates["updated_at"] = time.Now()

	// 执行更新
	if err := s.db.Model(pipeline).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新流水线失败: %w", err)
	}

	s.logger.Info("流水线更新成功", "pipeline_id", pipelineID)
	return s.GetPipeline(ctx, pipelineID)
}

// DeletePipeline 删除流水线
func (s *CICDService) DeletePipeline(ctx context.Context, pipelineID string) error {
	// 检查是否有正在运行的构建
	var runningBuilds int64
	err := s.db.Model(&Build{}).Where("pipeline_id = ? AND status IN ?",
		pipelineID, []BuildStatus{BuildStatusPending, BuildStatusRunning}).Count(&runningBuilds).Error
	if err != nil {
		return fmt.Errorf("检查运行中构建失败: %w", err)
	}

	if runningBuilds > 0 {
		return fmt.Errorf("流水线有 %d 个构建正在运行，无法删除", runningBuilds)
	}

	// 软删除流水线
	err = s.db.Model(&Pipeline{}).Where("id = ?", pipelineID).
		Update("status", PipelineStatusDeleted).Error
	if err != nil {
		return fmt.Errorf("删除流水线失败: %w", err)
	}

	s.logger.Info("流水线删除成功", "pipeline_id", pipelineID)
	return nil
}

// CreateDeployment 创建部署
func (s *CICDService) CreateDeployment(ctx context.Context, req *CreateDeploymentRequest) (*Deployment, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 检查构建是否存在且成功
	build, err := s.GetBuild(ctx, req.BuildID)
	if err != nil {
		return nil, fmt.Errorf("构建不存在: %w", err)
	}

	if build.Status != BuildStatusSuccess {
		return nil, fmt.Errorf("构建状态为 %s，无法部署", build.Status)
	}

	// 创建部署记录
	deployment := &Deployment{
		ID:          uuid.New().String(),
		AppID:       req.AppID,
		BuildID:     req.BuildID,
		Environment: req.Environment,
		Version:     req.Version,
		ImageTag:    req.ImageTag,
		Strategy:    req.Strategy,
		Status:      DeploymentStatusPending,
		Config:      req.Config,
		StartTime:   time.Now(),
	}

	if err := s.db.Create(deployment).Error; err != nil {
		return nil, fmt.Errorf("创建部署记录失败: %w", err)
	}

	s.logger.Info("部署创建成功", "deployment_id", deployment.ID, "app_id", req.AppID, "environment", req.Environment)
	return deployment, nil
}

// GetDeployment 获取部署详情
func (s *CICDService) GetDeployment(ctx context.Context, deploymentID string) (*Deployment, error) {
	var deployment Deployment
	err := s.db.Preload("Build").First(&deployment, "id = ?", deploymentID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("部署不存在: %s", deploymentID)
		}
		return nil, fmt.Errorf("获取部署失败: %w", err)
	}
	return &deployment, nil
}

// ListDeployments 获取部署列表
func (s *CICDService) ListDeployments(ctx context.Context, filter *DeploymentFilter) ([]*Deployment, int64, error) {
	query := s.db.Model(&Deployment{})

	// 应用过滤条件
	if filter != nil {
		if filter.AppID != "" {
			query = query.Where("app_id = ?", filter.AppID)
		}
		if filter.Environment != "" {
			query = query.Where("environment = ?", filter.Environment)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取部署总数失败: %w", err)
	}

	// 分页查询
	var deployments []*Deployment
	offset := 0
	limit := 20
	if filter != nil {
		if filter.Page > 0 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		if filter.PageSize > 0 && filter.PageSize <= 100 {
			limit = filter.PageSize
		}
	}

	err := query.Preload("Build").Offset(offset).Limit(limit).
		Order("start_time DESC").Find(&deployments).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取部署列表失败: %w", err)
	}

	return deployments, total, nil
}

// RollbackDeployment 回滚部署
func (s *CICDService) RollbackDeployment(ctx context.Context, deploymentID string) error {
	// 获取当前部署
	deployment, err := s.GetDeployment(ctx, deploymentID)
	if err != nil {
		return err
	}

	// 检查部署状态
	if deployment.Status != DeploymentStatusSuccess {
		return fmt.Errorf("部署状态为 %s，无法回滚", deployment.Status)
	}

	// 查找上一个成功的部署
	var previousDeployment Deployment
	err = s.db.Where("app_id = ? AND environment = ? AND status = ? AND start_time < ?",
		deployment.AppID, deployment.Environment, DeploymentStatusSuccess, deployment.StartTime).
		Order("start_time DESC").First(&previousDeployment).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("未找到可回滚的部署版本")
		}
		return fmt.Errorf("查找回滚版本失败: %w", err)
	}

	// 创建回滚部署记录
	rollbackDeployment := &Deployment{
		ID:           uuid.New().String(),
		AppID:        deployment.AppID,
		BuildID:      previousDeployment.BuildID,
		Environment:  deployment.Environment,
		Version:      previousDeployment.Version,
		ImageTag:     previousDeployment.ImageTag,
		Strategy:     "rolling", // 回滚使用滚动更新策略
		Status:       DeploymentStatusPending,
		Config:       previousDeployment.Config,
		StartTime:    time.Now(),
		RollbackFrom: deploymentID,
	}

	if err := s.db.Create(rollbackDeployment).Error; err != nil {
		return fmt.Errorf("创建回滚部署失败: %w", err)
	}

	s.logger.Info("部署回滚成功", "deployment_id", deploymentID, "rollback_deployment_id", rollbackDeployment.ID)
	return nil
}

// CreateEnvironment 创建环境
func (s *CICDService) CreateEnvironment(ctx context.Context, req *CreateEnvironmentRequest) (*Environment, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 检查环境名称是否已存在
	var existingEnv Environment
	err := s.db.Where("name = ? AND tenant_id = ?", req.Name, req.TenantID).First(&existingEnv).Error
	if err == nil {
		return nil, fmt.Errorf("环境名称 '%s' 已存在", req.Name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查环境名称失败: %w", err)
	}

	// 创建环境
	environment := &Environment{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Cluster:     req.Cluster,
		Namespace:   req.Namespace,
		Config:      req.Config,
		Status:      "active",
		TenantID:    req.TenantID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(environment).Error; err != nil {
		return nil, fmt.Errorf("创建环境失败: %w", err)
	}

	s.logger.Info("环境创建成功", "environment_id", environment.ID, "name", environment.Name, "type", environment.Type)
	return environment, nil
}

// GetEnvironment 获取环境详情
func (s *CICDService) GetEnvironment(ctx context.Context, envID string) (*Environment, error) {
	var environment Environment
	err := s.db.First(&environment, "id = ?", envID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("环境不存在: %s", envID)
		}
		return nil, fmt.Errorf("获取环境失败: %w", err)
	}
	return &environment, nil
}

// ListEnvironments 获取环境列表
func (s *CICDService) ListEnvironments(ctx context.Context, tenantID string) ([]*Environment, error) {
	var environments []*Environment
	err := s.db.Where("tenant_id = ? AND status = ?", tenantID, "active").
		Order("created_at DESC").Find(&environments).Error
	if err != nil {
		return nil, fmt.Errorf("获取环境列表失败: %w", err)
	}
	return environments, nil
}

// HandleWebhook 处理 Webhook
func (s *CICDService) HandleWebhook(ctx context.Context, source string, payload interface{}) error {
	// 记录 Webhook 事件
	webhook := &Webhook{
		ID:        uuid.New().String(),
		Source:    source,
		Payload:   payload,
		Processed: false,
		CreatedAt: time.Now(),
	}

	// 解析 Webhook 载荷
	webhookPayload, err := s.parseWebhookPayload(source, payload)
	if err != nil {
		s.logger.Error("解析 Webhook 载荷失败", "error", err, "source", source)
		return fmt.Errorf("解析 Webhook 载荷失败: %w", err)
	}

	webhook.Event = webhookPayload.Event
	webhook.AppID = s.findAppByRepository(webhookPayload.Repository.FullName)

	// 保存 Webhook 记录
	if err := s.db.Create(webhook).Error; err != nil {
		s.logger.Error("保存 Webhook 记录失败", "error", err)
		return fmt.Errorf("保存 Webhook 记录失败: %w", err)
	}

	// 处理 Webhook 事件
	if err := s.processWebhookEvent(ctx, webhookPayload); err != nil {
		s.logger.Error("处理 Webhook 事件失败", "error", err, "webhook_id", webhook.ID)
		return fmt.Errorf("处理 Webhook 事件失败: %w", err)
	}

	// 标记为已处理
	s.db.Model(webhook).Update("processed", true)

	s.logger.Info("Webhook 处理成功", "webhook_id", webhook.ID, "source", source, "event", webhook.Event)
	return nil
}

// RegisterBuildNode 注册构建节点
func (s *CICDService) RegisterBuildNode(ctx context.Context, req *RegisterNodeRequest) (*BuildNode, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 检查节点名称是否已存在
	var existingNode BuildNode
	err := s.db.Where("name = ?", req.Name).First(&existingNode).Error
	if err == nil {
		return nil, fmt.Errorf("构建节点名称 '%s' 已存在", req.Name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查节点名称失败: %w", err)
	}

	// 创建构建节点
	node := &BuildNode{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Address:     req.Address,
		Capacity:    req.Capacity,
		CurrentLoad: 0,
		Labels:      req.Labels,
		Status:      NodeStatusOnline,
		LastSeen:    time.Now(),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(node).Error; err != nil {
		return nil, fmt.Errorf("注册构建节点失败: %w", err)
	}

	s.logger.Info("构建节点注册成功", "node_id", node.ID, "name", node.Name, "capacity", node.Capacity)
	return node, nil
}

// GetBuildNode 获取构建节点详情
func (s *CICDService) GetBuildNode(ctx context.Context, nodeID string) (*BuildNode, error) {
	var node BuildNode
	err := s.db.First(&node, "id = ?", nodeID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("构建节点不存在: %s", nodeID)
		}
		return nil, fmt.Errorf("获取构建节点失败: %w", err)
	}
	return &node, nil
}

// ListBuildNodes 获取构建节点列表
func (s *CICDService) ListBuildNodes(ctx context.Context) ([]*BuildNode, error) {
	var nodes []*BuildNode
	err := s.db.Order("created_at DESC").Find(&nodes).Error
	if err != nil {
		return nil, fmt.Errorf("获取构建节点列表失败: %w", err)
	}
	return nodes, nil
}

// UpdateNodeStatus 更新节点状态
func (s *CICDService) UpdateNodeStatus(ctx context.Context, nodeID string, status NodeStatus) error {
	err := s.db.Model(&BuildNode{}).Where("id = ?", nodeID).Updates(map[string]interface{}{
		"status":    status,
		"last_seen": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("更新节点状态失败: %w", err)
	}

	s.logger.Info("节点状态更新", "node_id", nodeID, "status", status)
	return nil
}

// GetConfigByID 根据ID获取流水线配置
func (s *CICDService) GetConfigByID(ctx context.Context, configID string) (*Pipeline, error) {
	var pipeline Pipeline
	err := s.db.First(&pipeline, "id = ?", configID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	return &pipeline, nil
}

// 辅助方法

// parseWebhookPayload 解析 Webhook 载荷
func (s *CICDService) parseWebhookPayload(source string, payload interface{}) (*WebhookPayload, error) {
	// 这里需要根据不同的 Git 服务解析载荷格式
	// 目前先返回一个基本的结构
	webhookPayload := &WebhookPayload{
		Source: source,
		Event:  "push", // 默认为 push 事件
	}

	// TODO: 实现具体的载荷解析逻辑
	// 根据 source 类型（gitea, github, gitlab）解析不同格式的载荷

	return webhookPayload, nil
}

// findAppByRepository 根据仓库名称查找应用ID
func (s *CICDService) findAppByRepository(repoFullName string) string {
	// TODO: 实现根据仓库名称查找应用ID的逻辑
	// 这需要与应用管理服务集成
	return ""
}

// processWebhookEvent 处理 Webhook 事件
func (s *CICDService) processWebhookEvent(ctx context.Context, payload *WebhookPayload) error {
	// 根据事件类型处理
	switch payload.Event {
	case "push":
		return s.handlePushEvent(ctx, payload)
	case "pull_request":
		return s.handlePullRequestEvent(ctx, payload)
	case "tag":
		return s.handleTagEvent(ctx, payload)
	default:
		s.logger.Warn("未支持的 Webhook 事件类型", "event", payload.Event)
		return nil
	}
}

// handlePushEvent 处理推送事件
func (s *CICDService) handlePushEvent(ctx context.Context, payload *WebhookPayload) error {
	// 查找相关的流水线
	var pipelines []*Pipeline
	err := s.db.Where("app_id = ? AND status = ?", payload.Repository.ID, PipelineStatusActive).Find(&pipelines).Error
	if err != nil {
		return fmt.Errorf("查找流水线失败: %w", err)
	}

	// 为每个匹配的流水线创建构建任务
	for _, pipeline := range pipelines {
		if s.shouldTriggerBuild(pipeline, payload) {
			buildReq := &CreateBuildRequest{
				PipelineID:  pipeline.ID,
				Branch:      s.extractBranchFromRef(payload.Ref),
				CommitHash:  payload.After,
				CommitMsg:   s.getLatestCommitMessage(payload.Commits),
				Author:      s.getLatestCommitAuthor(payload.Commits),
				TriggerType: "push",
				Priority:    0,
			}

			_, err := s.CreateBuild(ctx, buildReq)
			if err != nil {
				s.logger.Error("创建构建任务失败", "error", err, "pipeline_id", pipeline.ID)
				continue
			}

			s.logger.Info("Webhook 触发构建成功", "pipeline_id", pipeline.ID, "commit", payload.After)
		}
	}

	return nil
}

// handlePullRequestEvent 处理拉取请求事件
func (s *CICDService) handlePullRequestEvent(ctx context.Context, payload *WebhookPayload) error {
	// TODO: 实现拉取请求事件处理逻辑
	s.logger.Info("处理拉取请求事件", "repository", payload.Repository.FullName)
	return nil
}

// handleTagEvent 处理标签事件
func (s *CICDService) handleTagEvent(ctx context.Context, payload *WebhookPayload) error {
	// TODO: 实现标签事件处理逻辑
	s.logger.Info("处理标签事件", "repository", payload.Repository.FullName)
	return nil
}

// shouldTriggerBuild 判断是否应该触发构建
func (s *CICDService) shouldTriggerBuild(pipeline *Pipeline, payload *WebhookPayload) bool {
	branch := s.extractBranchFromRef(payload.Ref)

	// 检查流水线的触发条件
	for _, trigger := range pipeline.Config.Spec.Triggers {
		if trigger.Type == "push" {
			// 检查分支匹配
			if len(trigger.Branches) == 0 {
				return true // 没有分支限制，触发所有分支
			}

			for _, triggerBranch := range trigger.Branches {
				if triggerBranch == branch {
					return true
				}
			}
		}
	}

	return false
}

// extractBranchFromRef 从 ref 中提取分支名称
func (s *CICDService) extractBranchFromRef(ref string) string {
	// refs/heads/main -> main
	if len(ref) > 11 && ref[:11] == "refs/heads/" {
		return ref[11:]
	}
	return ref
}

// getLatestCommitMessage 获取最新提交消息
func (s *CICDService) getLatestCommitMessage(commits []Commit) string {
	if len(commits) > 0 {
		return commits[len(commits)-1].Message
	}
	return ""
}

// getLatestCommitAuthor 获取最新提交作者
func (s *CICDService) getLatestCommitAuthor(commits []Commit) string {
	if len(commits) > 0 {
		return commits[len(commits)-1].Author.Username
	}
	return ""
}
