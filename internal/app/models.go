package app

import (
	"time"
)

// Application 应用实体
type Application struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36)" validate:"required"`
	Name        string    `json:"name" gorm:"uniqueIndex;type:varchar(100);not null" validate:"required,min=1,max=100"`
	Description string    `json:"description" gorm:"type:text"`
	Language    string    `json:"language" gorm:"type:varchar(20);not null" validate:"required,oneof=nodejs python"`
	Framework   string    `json:"framework" gorm:"type:varchar(50)"`
	Version     string    `json:"version" gorm:"type:varchar(20);not null" validate:"required"`
	GitRepo     string    `json:"git_repo" gorm:"type:varchar(500)" validate:"url"`
	GitBranch   string    `json:"git_branch" gorm:"type:varchar(100);default:main"`
	Status      AppStatus `json:"status" gorm:"type:varchar(20);not null;default:created"`
	TenantID    string    `json:"tenant_id" gorm:"type:varchar(36);not null;index" validate:"required"`
	UserID      string    `json:"user_id" gorm:"type:varchar(36);not null;index" validate:"required"`
	Config      AppConfig `json:"config" gorm:"type:jsonb"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	
	// 关联关系
	Instances []AppInstance `json:"instances,omitempty" gorm:"foreignKey:AppID"`
	Builds    []BuildTask   `json:"builds,omitempty" gorm:"foreignKey:AppID"`
}

// AppStatus 应用状态枚举
type AppStatus string

const (
	AppStatusCreated      AppStatus = "created"       // 已创建
	AppStatusBuilding     AppStatus = "building"      // 构建中
	AppStatusBuildFailed  AppStatus = "build_failed"  // 构建失败
	AppStatusBuildSuccess AppStatus = "build_success" // 构建成功
	AppStatusDeploying    AppStatus = "deploying"     // 部署中
	AppStatusRunning      AppStatus = "running"       // 运行中
	AppStatusStopped      AppStatus = "stopped"       // 已停止
	AppStatusError        AppStatus = "error"         // 运行异常
	AppStatusDeleted      AppStatus = "deleted"       // 已删除
)

// AppConfig 应用配置
type AppConfig struct {
	Environment string            `json:"environment"` // 环境名称：development, staging, production
	Runtime     RuntimeConfig     `json:"runtime"`
	Build       BuildConfig       `json:"build"`
	Deploy      DeployConfig      `json:"deploy"`
	Service     ServiceConfig     `json:"service"`
	Ingress     IngressConfig     `json:"ingress"`
	Volumes     []VolumeConfig    `json:"volumes"`
}

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	Language  string `json:"language" validate:"required,oneof=nodejs python"`
	Version   string `json:"version" validate:"required"`
	Framework string `json:"framework"`
}

// BuildConfig 构建配置
type BuildConfig struct {
	Dockerfile string            `json:"dockerfile"`
	BuildArgs  map[string]string `json:"buildArgs"`
	Excludes   []string          `json:"excludes"`
}

// DeployConfig 部署配置
type DeployConfig struct {
	Port        int                    `json:"port" validate:"required,min=1,max=65535"`
	Instances   int                    `json:"instances" validate:"min=0,max=100"`
	Command     []string               `json:"command"`
	WorkingDir  string                 `json:"workingDir"`
	Resources   ResourceConfig         `json:"resources"`
	Env         map[string]string      `json:"env"`
	Secrets     []SecretRef            `json:"secrets"`
	HealthCheck HealthCheckConfig      `json:"healthCheck"`
	Autoscaling AutoscalingConfig      `json:"autoscaling"`
}

// ResourceConfig 资源配置
type ResourceConfig struct {
	Requests ResourceSpec `json:"requests"`
	Limits   ResourceSpec `json:"limits"`
}

// ResourceSpec 资源规格
type ResourceSpec struct {
	CPU    string `json:"cpu"`    // 例如: "500m"
	Memory string `json:"memory"` // 例如: "512Mi"
	Disk   string `json:"disk"`   // 例如: "1Gi"
}

// SecretRef 密钥引用
type SecretRef struct {
	Name string `json:"name" validate:"required"`
	Key  string `json:"key" validate:"required"`
	Env  string `json:"env" validate:"required"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Path                string `json:"path" validate:"required"`
	Port                int    `json:"port" validate:"required,min=1,max=65535"`
	InitialDelaySeconds int    `json:"initialDelaySeconds" validate:"min=0"`
	PeriodSeconds       int    `json:"periodSeconds" validate:"min=1"`
	TimeoutSeconds      int    `json:"timeoutSeconds" validate:"min=1"`
	FailureThreshold    int    `json:"failureThreshold" validate:"min=1"`
}

// AutoscalingConfig 自动扩缩容配置
type AutoscalingConfig struct {
	Enabled                    bool `json:"enabled"`
	MinReplicas                int  `json:"minReplicas" validate:"min=0"`
	MaxReplicas                int  `json:"maxReplicas" validate:"min=1"`
	TargetCPUUtilization       int  `json:"targetCPUUtilization" validate:"min=1,max=100"`
	TargetMemoryUtilization    int  `json:"targetMemoryUtilization" validate:"min=1,max=100"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Type  string       `json:"type" validate:"oneof=ClusterIP NodePort LoadBalancer"`
	Ports []PortConfig `json:"ports"`
}

// PortConfig 端口配置
type PortConfig struct {
	Name       string `json:"name" validate:"required"`
	Port       int    `json:"port" validate:"required,min=1,max=65535"`
	TargetPort int    `json:"targetPort" validate:"required,min=1,max=65535"`
	Protocol   string `json:"protocol" validate:"oneof=TCP UDP"`
}

// IngressConfig 路由配置
type IngressConfig struct {
	Enabled bool      `json:"enabled"`
	Host    string    `json:"host" validate:"hostname"`
	Path    string    `json:"path"`
	TLS     TLSConfig `json:"tls"`
}

// TLSConfig TLS 配置
type TLSConfig struct {
	Enabled    bool   `json:"enabled"`
	SecretName string `json:"secretName"`
}

// VolumeConfig 存储卷配置
type VolumeConfig struct {
	Name      string `json:"name" validate:"required"`
	Type      string `json:"type" validate:"oneof=persistent temporary"`
	Size      string `json:"size" validate:"required"`
	MountPath string `json:"mountPath" validate:"required"`
}

// AppInstance 应用实例
type AppInstance struct {
	ID           string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	AppID        string            `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Version      string            `json:"version" gorm:"type:varchar(20);not null"`
	Status       InstanceStatus    `json:"status" gorm:"type:varchar(20);not null"`
	ContainerID  string            `json:"container_id" gorm:"type:varchar(100)"`
	ImageTag     string            `json:"image_tag" gorm:"type:varchar(200)"`
	Port         int               `json:"port" gorm:"not null"`
	InternalIP   string            `json:"internal_ip" gorm:"type:varchar(45)"`
	HealthStatus string            `json:"health_status" gorm:"type:varchar(20);default:unknown"`
	LastHealthCheck time.Time      `json:"last_health_check"`
	Metrics      InstanceMetrics   `json:"metrics" gorm:"type:jsonb"`
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	
	// 关联关系
	Application Application `json:"application,omitempty" gorm:"foreignKey:AppID"`
}

// InstanceStatus 实例状态
type InstanceStatus string

const (
	InstanceStatusPending   InstanceStatus = "pending"   // 等待中
	InstanceStatusStarting  InstanceStatus = "starting"  // 启动中
	InstanceStatusRunning   InstanceStatus = "running"   // 运行中
	InstanceStatusStopping  InstanceStatus = "stopping"  // 停止中
	InstanceStatusStopped   InstanceStatus = "stopped"   // 已停止
	InstanceStatusFailed    InstanceStatus = "failed"    // 失败
	InstanceStatusUnknown   InstanceStatus = "unknown"   // 未知状态
)

// InstanceMetrics 实例指标
type InstanceMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`     // CPU 使用率 (%)
	MemoryUsage float64 `json:"memory_usage"`  // 内存使用率 (%)
	DiskUsage   float64 `json:"disk_usage"`    // 磁盘使用率 (%)
	NetworkIn   int64   `json:"network_in"`    // 网络入流量 (bytes)
	NetworkOut  int64   `json:"network_out"`   // 网络出流量 (bytes)
	RequestCount int64  `json:"request_count"` // 请求数量
	ErrorCount   int64  `json:"error_count"`   // 错误数量
	UpdatedAt    time.Time `json:"updated_at"`
}

// BuildTask 构建任务
type BuildTask struct {
	ID         string      `json:"id" gorm:"primaryKey;type:varchar(36)"`
	AppID      string      `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Branch     string      `json:"branch" gorm:"type:varchar(100);not null"`
	CommitHash string      `json:"commit_hash" gorm:"type:varchar(40);not null"`
	CommitMsg  string      `json:"commit_msg" gorm:"type:text"`
	Status     BuildStatus `json:"status" gorm:"type:varchar(20);not null"`
	ImageTag   string      `json:"image_tag" gorm:"type:varchar(200)"`
	BuildLog   string      `json:"build_log" gorm:"type:text"`
	ErrorMsg   string      `json:"error_msg" gorm:"type:text"`
	StartTime  time.Time   `json:"start_time" gorm:"autoCreateTime"`
	EndTime    *time.Time  `json:"end_time"`
	Duration   int         `json:"duration"` // 构建时长 (秒)
	
	// 关联关系
	Application Application `json:"application,omitempty" gorm:"foreignKey:AppID"`
}

// BuildStatus 构建状态
type BuildStatus string

const (
	BuildStatusPending  BuildStatus = "pending"  // 等待中
	BuildStatusRunning  BuildStatus = "running"  // 构建中
	BuildStatusSuccess  BuildStatus = "success"  // 成功
	BuildStatusFailed   BuildStatus = "failed"   // 失败
	BuildStatusCanceled BuildStatus = "canceled" // 已取消
)

// AppVersion 应用版本
type AppVersion struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	AppID       string    `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Version     string    `json:"version" gorm:"type:varchar(20);not null"`
	ImageTag    string    `json:"image_tag" gorm:"type:varchar(200);not null"`
	CommitHash  string    `json:"commit_hash" gorm:"type:varchar(40);not null"`
	Config      AppConfig `json:"config" gorm:"type:jsonb"`
	IsActive    bool      `json:"is_active" gorm:"default:false"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	Application Application `json:"application,omitempty" gorm:"foreignKey:AppID"`
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

func (AppInstance) TableName() string {
	return "app_instances"
}

func (BuildTask) TableName() string {
	return "build_tasks"
}

func (AppVersion) TableName() string {
	return "app_versions"
}
