package auth

import (
	"fmt"
	"strings"
	"time"
)

// CreateIDPProviderRequest 创建 IDP 提供商请求
type CreateIDPProviderRequest struct {
	Name           string              `json:"name" binding:"required"`
	Type           IDPProviderType     `json:"type" binding:"required"`
	Enabled        bool                `json:"enabled"`
	Config         IDPConfig           `json:"config" binding:"required"`
	Mapping        IDPAttributeMapping `json:"mapping" binding:"required"`
	TenantID       string              `json:"tenant_id" binding:"required"`
	Priority       int                 `json:"priority"`
	AutoCreateUser bool                `json:"auto_create_user"`
	AutoLinkUser   bool                `json:"auto_link_user"`
}

// Validate 验证创建 IDP 提供商请求
func (r *CreateIDPProviderRequest) Validate() error {
	if strings.TrimSpace(r.Name) == "" {
		return fmt.Errorf("IDP 提供商名称不能为空")
	}
	
	if r.Type == "" {
		return fmt.Errorf("IDP 提供商类型不能为空")
	}
	
	if r.TenantID == "" {
		return fmt.Errorf("租户 ID 不能为空")
	}
	
	// 根据类型验证配置
	switch r.Type {
	case IDPProviderOIDC, IDPProviderOAuth2:
		if r.Config.ClientID == "" {
			return fmt.Errorf("OAuth2/OIDC 配置缺少 Client ID")
		}
		if r.Config.ClientSecret == "" {
			return fmt.Errorf("OAuth2/OIDC 配置缺少 Client Secret")
		}
		if r.Config.AuthURL == "" {
			return fmt.Errorf("OAuth2/OIDC 配置缺少认证 URL")
		}
		if r.Config.TokenURL == "" {
			return fmt.Errorf("OAuth2/OIDC 配置缺少令牌 URL")
		}
	case IDPProviderSAML:
		if r.Config.EntityID == "" {
			return fmt.Errorf("SAML 配置缺少 Entity ID")
		}
		if r.Config.SSOURL == "" {
			return fmt.Errorf("SAML 配置缺少 SSO URL")
		}
	case IDPProviderLDAP:
		if r.Config.Host == "" {
			return fmt.Errorf("LDAP 配置缺少主机地址")
		}
		if r.Config.BaseDN == "" {
			return fmt.Errorf("LDAP 配置缺少 Base DN")
		}
	}
	
	// 验证属性映射
	if r.Mapping.Email == "" {
		return fmt.Errorf("属性映射缺少邮箱字段")
	}
	
	return nil
}

// UpdateIDPProviderRequest 更新 IDP 提供商请求
type UpdateIDPProviderRequest struct {
	Name           *string              `json:"name,omitempty"`
	Enabled        *bool                `json:"enabled,omitempty"`
	Config         *IDPConfig           `json:"config,omitempty"`
	Mapping        *IDPAttributeMapping `json:"mapping,omitempty"`
	Priority       *int                 `json:"priority,omitempty"`
	AutoCreateUser *bool                `json:"auto_create_user,omitempty"`
	AutoLinkUser   *bool                `json:"auto_link_user,omitempty"`
}

// Validate 验证更新 IDP 提供商请求
func (r *UpdateIDPProviderRequest) Validate() error {
	if r.Name != nil && strings.TrimSpace(*r.Name) == "" {
		return fmt.Errorf("IDP 提供商名称不能为空")
	}
	
	return nil
}

// IDPAuthInitResponse IDP 认证初始化响应
type IDPAuthInitResponse struct {
	AuthURL   string `json:"auth_url"`   // 认证 URL
	State     string `json:"state"`      // 状态参数
	Nonce     string `json:"nonce"`      // 随机数
	ExpiresAt int64  `json:"expires_at"` // 过期时间
}

// IDPAuthResult IDP 认证结果
type IDPAuthResult struct {
	Success     bool         `json:"success"`
	User        *User        `json:"user,omitempty"`
	IDPAccount  *IDPAccount  `json:"idp_account,omitempty"`
	TokenPair   *TokenPair   `json:"token_pair,omitempty"`
	IsNewUser   bool         `json:"is_new_user"`
	IsLinked    bool         `json:"is_linked"`
	ErrorCode   string       `json:"error_code,omitempty"`
	ErrorMsg    string       `json:"error_msg,omitempty"`
}

// IDPUserInfo IDP 用户信息
type IDPUserInfo struct {
	ID        string                 `json:"id"`
	Username  string                 `json:"username"`
	Email     string                 `json:"email"`
	FirstName string                 `json:"first_name"`
	LastName  string                 `json:"last_name"`
	Avatar    string                 `json:"avatar"`
	Phone     string                 `json:"phone"`
	Roles     []string               `json:"roles"`
	Groups    []string               `json:"groups"`
	RawData   map[string]interface{} `json:"raw_data"`
}

// CreateIDPSessionRequest 创建 IDP 会话请求
type CreateIDPSessionRequest struct {
	UserID        string    `json:"user_id" binding:"required"`
	IDPProviderID string    `json:"idp_provider_id" binding:"required"`
	IDPSessionID  string    `json:"idp_session_id"`
	AccessToken   string    `json:"access_token" binding:"required"`
	RefreshToken  string    `json:"refresh_token"`
	TokenType     string    `json:"token_type"`
	ExpiresAt     *time.Time `json:"expires_at"`
	Scope         string    `json:"scope"`
}

// Validate 验证创建 IDP 会话请求
func (r *CreateIDPSessionRequest) Validate() error {
	if r.UserID == "" {
		return fmt.Errorf("用户 ID 不能为空")
	}
	
	if r.IDPProviderID == "" {
		return fmt.Errorf("IDP 提供商 ID 不能为空")
	}
	
	if r.AccessToken == "" {
		return fmt.Errorf("访问令牌不能为空")
	}
	
	return nil
}

// LinkIDPAccountRequest 关联 IDP 账号请求
type LinkIDPAccountRequest struct {
	UserID        string `json:"user_id" binding:"required"`
	IDPProviderID string `json:"idp_provider_id" binding:"required"`
	IDPUserID     string `json:"idp_user_id" binding:"required"`
	IDPUsername   string `json:"idp_username"`
	IDPEmail      string `json:"idp_email"`
}

// Validate 验证关联 IDP 账号请求
func (r *LinkIDPAccountRequest) Validate() error {
	if r.UserID == "" {
		return fmt.Errorf("用户 ID 不能为空")
	}
	
	if r.IDPProviderID == "" {
		return fmt.Errorf("IDP 提供商 ID 不能为空")
	}
	
	if r.IDPUserID == "" {
		return fmt.Errorf("IDP 用户 ID 不能为空")
	}
	
	return nil
}

// UnlinkIDPAccountRequest 取消关联 IDP 账号请求
type UnlinkIDPAccountRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	AccountID string `json:"account_id" binding:"required"`
}

// Validate 验证取消关联 IDP 账号请求
func (r *UnlinkIDPAccountRequest) Validate() error {
	if r.UserID == "" {
		return fmt.Errorf("用户 ID 不能为空")
	}
	
	if r.AccountID == "" {
		return fmt.Errorf("账号 ID 不能为空")
	}
	
	return nil
}

// IDPLoginRequest IDP 登录请求
type IDPLoginRequest struct {
	ProviderName string `json:"provider_name" binding:"required"` // IDP 提供商名称
	TenantID     string `json:"tenant_id" binding:"required"`     // 租户 ID
	RedirectURL  string `json:"redirect_url"`                     // 登录成功后的重定向 URL
	IPAddress    string `json:"-"`                                // 客户端 IP（自动填充）
	UserAgent    string `json:"-"`                                // 用户代理（自动填充）
}

// Validate 验证 IDP 登录请求
func (r *IDPLoginRequest) Validate() error {
	if strings.TrimSpace(r.ProviderName) == "" {
		return fmt.Errorf("IDP 提供商名称不能为空")
	}
	
	if r.TenantID == "" {
		return fmt.Errorf("租户 ID 不能为空")
	}
	
	return nil
}

// IDPCallbackRequest IDP 回调请求
type IDPCallbackRequest struct {
	ProviderName string `json:"provider_name" binding:"required"` // IDP 提供商名称
	Code         string `json:"code" binding:"required"`          // 授权码
	State        string `json:"state" binding:"required"`         // 状态参数
	TenantID     string `json:"tenant_id" binding:"required"`     // 租户 ID
	IPAddress    string `json:"-"`                                // 客户端 IP（自动填充）
	UserAgent    string `json:"-"`                                // 用户代理（自动填充）
}

// Validate 验证 IDP 回调请求
func (r *IDPCallbackRequest) Validate() error {
	if strings.TrimSpace(r.ProviderName) == "" {
		return fmt.Errorf("IDP 提供商名称不能为空")
	}
	
	if r.Code == "" {
		return fmt.Errorf("授权码不能为空")
	}
	
	if r.State == "" {
		return fmt.Errorf("状态参数不能为空")
	}
	
	if r.TenantID == "" {
		return fmt.Errorf("租户 ID 不能为空")
	}
	
	return nil
}

// IDPProviderResponse IDP 提供商响应
type IDPProviderResponse struct {
	*IDPProvider
	AccountCount int `json:"account_count"` // 关联账号数量
}

// IDPAccountResponse IDP 账号响应
type IDPAccountResponse struct {
	*IDPAccount
	ProviderName string `json:"provider_name"` // 提供商名称
	UserInfo     *User  `json:"user_info"`     // 用户信息
}

// IDPSyncResult IDP 同步结果
type IDPSyncResult struct {
	Success      bool     `json:"success"`
	UpdatedUsers int      `json:"updated_users"`
	CreatedUsers int      `json:"created_users"`
	FailedUsers  int      `json:"failed_users"`
	Errors       []string `json:"errors,omitempty"`
}

// IDPStats IDP 统计信息
type IDPStats struct {
	TotalProviders   int64                  `json:"total_providers"`
	ActiveProviders  int64                  `json:"active_providers"`
	TotalAccounts    int64                  `json:"total_accounts"`
	ActiveAccounts   int64                  `json:"active_accounts"`
	ProviderStats    map[string]int         `json:"provider_stats"`
	RecentLogins     int                    `json:"recent_logins"`
	FailedLogins     int                    `json:"failed_logins"`
	LastSyncTime     *time.Time             `json:"last_sync_time"`
}
