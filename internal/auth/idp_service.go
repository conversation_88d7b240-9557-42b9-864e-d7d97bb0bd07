package auth

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// IDPService IDP 服务接口
type IDPService interface {
	// IDP 提供商管理
	CreateIDPProvider(ctx context.Context, req *CreateIDPProviderRequest) (*IDPProvider, error)
	GetIDPProvider(ctx context.Context, id string) (*IDPProvider, error)
	GetIDPProviderByName(ctx context.Context, name string, tenantID string) (*IDPProvider, error)
	ListIDPProviders(ctx context.Context, tenantID string) ([]*IDPProvider, error)
	UpdateIDPProvider(ctx context.Context, id string, req *UpdateIDPProviderRequest) (*IDPProvider, error)
	DeleteIDPProvider(ctx context.Context, id string) error
	
	// IDP 认证流程
	InitiateIDPAuth(ctx context.Context, providerID string, redirectURL string) (*IDPAuthInitResponse, error)
	HandleIDPCallback(ctx context.Context, providerID string, code string, state string) (*IDPAuthResult, error)
	ValidateIDPToken(ctx context.Context, providerID string, token string) (*IDPUserInfo, error)
	
	// 账号关联管理
	LinkIDPAccount(ctx context.Context, userID string, providerID string, idpUserID string) (*IDPAccount, error)
	UnlinkIDPAccount(ctx context.Context, userID string, accountID string) error
	GetUserIDPAccounts(ctx context.Context, userID string) ([]*IDPAccount, error)
	FindUserByIDPAccount(ctx context.Context, providerID string, idpUserID string) (*User, error)
	
	// 用户信息同步
	SyncUserFromIDP(ctx context.Context, accountID string) error
	SyncAllUsersFromIDP(ctx context.Context, providerID string) (*IDPSyncResult, error)

	// 用户登出
	LogoutUser(ctx context.Context, userID string) error

	// 统计和审计
	GetIDPStats(ctx context.Context) (*IDPStats, error)
	GetIDPAuditLogs(ctx context.Context) ([]*IDPAuditLog, error)

	// IDP 会话管理
	CreateIDPSession(ctx context.Context, req *CreateIDPSessionRequest) (*IDPSession, error)
	GetIDPSession(ctx context.Context, sessionID string) (*IDPSession, error)
	RefreshIDPSession(ctx context.Context, sessionID string) (*IDPSession, error)
	RevokeIDPSession(ctx context.Context, sessionID string) error
	
	// 审计日志
	LogIDPEvent(ctx context.Context, event *IDPAuditLog) error
}

// idpService IDP 服务实现
type idpService struct {
	db     *gorm.DB
	logger Logger
}

// NewIDPService 创建 IDP 服务
func NewIDPService(db *gorm.DB, logger Logger) IDPService {
	return &idpService{
		db:     db,
		logger: logger,
	}
}

// CreateIDPProvider 创建 IDP 提供商
func (s *idpService) CreateIDPProvider(ctx context.Context, req *CreateIDPProviderRequest) (*IDPProvider, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查名称是否已存在
	var existing IDPProvider
	if err := s.db.Where("name = ? AND tenant_id = ?", req.Name, req.TenantID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("IDP 提供商名称已存在")
	}
	
	provider := &IDPProvider{
		ID:             generateID(),
		Name:           req.Name,
		Type:           req.Type,
		Enabled:        req.Enabled,
		Config:         req.Config,
		Mapping:        req.Mapping,
		TenantID:       req.TenantID,
		Priority:       req.Priority,
		AutoCreateUser: req.AutoCreateUser,
		AutoLinkUser:   req.AutoLinkUser,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	
	if err := s.db.Create(provider).Error; err != nil {
		return nil, fmt.Errorf("创建 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商创建成功", "provider_id", provider.ID, "name", provider.Name)
	return provider, nil
}

// GetIDPProvider 获取 IDP 提供商
func (s *idpService) GetIDPProvider(ctx context.Context, id string) (*IDPProvider, error) {
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	return &provider, nil
}

// GetIDPProviderByName 根据名称获取 IDP 提供商
func (s *idpService) GetIDPProviderByName(ctx context.Context, name string, tenantID string) (*IDPProvider, error) {
	var provider IDPProvider
	if err := s.db.Where("name = ? AND tenant_id = ?", name, tenantID).First(&provider).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	return &provider, nil
}

// ListIDPProviders 获取 IDP 提供商列表
func (s *idpService) ListIDPProviders(ctx context.Context, tenantID string) ([]*IDPProvider, error) {
	var providers []*IDPProvider
	query := s.db.Where("tenant_id = ?", tenantID).Order("priority ASC, created_at ASC")
	
	if err := query.Find(&providers).Error; err != nil {
		return nil, fmt.Errorf("获取 IDP 提供商列表失败: %w", err)
	}
	
	return providers, nil
}

// UpdateIDPProvider 更新 IDP 提供商
func (s *idpService) UpdateIDPProvider(ctx context.Context, id string, req *UpdateIDPProviderRequest) (*IDPProvider, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	// 更新字段
	if req.Name != nil && *req.Name != "" {
		provider.Name = *req.Name
	}
	if req.Enabled != nil {
		provider.Enabled = *req.Enabled
	}
	if req.Config != nil {
		provider.Config = *req.Config
	}
	if req.Mapping != nil {
		provider.Mapping = *req.Mapping
	}
	if req.Priority != nil {
		provider.Priority = *req.Priority
	}
	if req.AutoCreateUser != nil {
		provider.AutoCreateUser = *req.AutoCreateUser
	}
	if req.AutoLinkUser != nil {
		provider.AutoLinkUser = *req.AutoLinkUser
	}
	
	provider.UpdatedAt = time.Now()
	
	if err := s.db.Save(&provider).Error; err != nil {
		return nil, fmt.Errorf("更新 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商更新成功", "provider_id", provider.ID, "name", provider.Name)
	return &provider, nil
}

// DeleteIDPProvider 删除 IDP 提供商
func (s *idpService) DeleteIDPProvider(ctx context.Context, id string) error {
	// 检查是否有关联的账号
	var count int64
	if err := s.db.Model(&IDPAccount{}).Where("idp_provider_id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("检查关联账号失败: %w", err)
	}
	
	if count > 0 {
		return fmt.Errorf("无法删除 IDP 提供商，存在 %d 个关联账号", count)
	}
	
	if err := s.db.Delete(&IDPProvider{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("删除 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商删除成功", "provider_id", id)
	return nil
}

// LinkIDPAccount 关联 IDP 账号
func (s *idpService) LinkIDPAccount(ctx context.Context, userID string, providerID string, idpUserID string) (*IDPAccount, error) {
	// 检查用户是否存在
	var user User
	if err := s.db.First(&user, "id = ?", userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	
	// 检查 IDP 提供商是否存在
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", providerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	// 检查是否已经关联
	var existing IDPAccount
	if err := s.db.Where("user_id = ? AND idp_provider_id = ?", userID, providerID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("用户已关联此 IDP 提供商")
	}
	
	// 检查 IDP 用户 ID 是否已被其他用户关联
	if err := s.db.Where("idp_provider_id = ? AND idp_user_id = ?", providerID, idpUserID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("此 IDP 账号已被其他用户关联")
	}
	
	account := &IDPAccount{
		ID:            generateID(),
		UserID:        userID,
		IDPProviderID: providerID,
		IDPUserID:     idpUserID,
		Status:        IDPAccountStatusActive,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	
	if err := s.db.Create(account).Error; err != nil {
		return nil, fmt.Errorf("关联 IDP 账号失败: %w", err)
	}
	
	// 如果这是用户的第一个 IDP 账号，设置为主要账号
	if user.PrimaryIDPID == "" {
		user.PrimaryIDPID = account.ID
		user.IsIDPUser = true
		s.db.Save(&user)
	}
	
	s.logger.Info("IDP 账号关联成功", "user_id", userID, "provider_id", providerID, "idp_user_id", idpUserID)
	return account, nil
}

// LogIDPEvent 记录 IDP 事件
func (s *idpService) LogIDPEvent(ctx context.Context, event *IDPAuditLog) error {
	event.ID = generateID()
	event.Timestamp = time.Now()
	
	if err := s.db.Create(event).Error; err != nil {
		s.logger.Error("记录 IDP 审计日志失败", "error", err)
		return fmt.Errorf("记录 IDP 审计日志失败: %w", err)
	}
	
	return nil
}

// LogoutUser 用户登出
func (s *idpService) LogoutUser(ctx context.Context, userID string) error {
	// 删除用户的所有 IDP 会话
	if err := s.db.Where("user_id = ?", userID).Delete(&IDPSession{}).Error; err != nil {
		return fmt.Errorf("删除 IDP 会话失败: %w", err)
	}

	s.logger.Info("用户 IDP 登出成功", "user_id", userID)
	return nil
}

// GetIDPStats 获取 IDP 统计信息
func (s *idpService) GetIDPStats(ctx context.Context) (*IDPStats, error) {
	stats := &IDPStats{
		ProviderStats: make(map[string]int),
	}

	// 统计提供商数量
	if err := s.db.Model(&IDPProvider{}).Count(&stats.TotalProviders).Error; err != nil {
		return nil, fmt.Errorf("统计提供商数量失败: %w", err)
	}

	// 统计活跃提供商数量
	if err := s.db.Model(&IDPProvider{}).Where("enabled = ?", true).Count(&stats.ActiveProviders).Error; err != nil {
		return nil, fmt.Errorf("统计活跃提供商数量失败: %w", err)
	}

	// 统计账号数量
	if err := s.db.Model(&IDPAccount{}).Count(&stats.TotalAccounts).Error; err != nil {
		return nil, fmt.Errorf("统计账号数量失败: %w", err)
	}

	// 统计活跃账号数量
	if err := s.db.Model(&IDPAccount{}).Where("status = ?", "active").Count(&stats.ActiveAccounts).Error; err != nil {
		return nil, fmt.Errorf("统计活跃账号数量失败: %w", err)
	}

	return stats, nil
}

// GetIDPAuditLogs 获取 IDP 审计日志
func (s *idpService) GetIDPAuditLogs(ctx context.Context) ([]*IDPAuditLog, error) {
	var logs []*IDPAuditLog
	if err := s.db.Order("timestamp DESC").Limit(100).Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取 IDP 审计日志失败: %w", err)
	}

	return logs, nil
}

// SyncAllUsersFromIDP 同步所有用户
func (s *idpService) SyncAllUsersFromIDP(ctx context.Context, providerID string) (*IDPSyncResult, error) {
	result := &IDPSyncResult{
		Success: true,
	}

	// 获取提供商的所有账号
	var accounts []*IDPAccount
	if err := s.db.Where("idp_provider_id = ?", providerID).Find(&accounts).Error; err != nil {
		return nil, fmt.Errorf("获取账号列表失败: %w", err)
	}

	// 同步每个账号
	for _, account := range accounts {
		if err := s.syncUserFromIDPInternal(ctx, account.ID); err != nil {
			result.FailedUsers++
			result.Errors = append(result.Errors, fmt.Sprintf("同步账号 %s 失败: %v", account.ID, err))
		} else {
			result.UpdatedUsers++
		}
	}

	if result.FailedUsers > 0 {
		result.Success = false
	}

	return result, nil
}

// CreateIDPSession 创建 IDP 会话
func (s *idpService) CreateIDPSession(ctx context.Context, req *CreateIDPSessionRequest) (*IDPSession, error) {
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	session := &IDPSession{
		ID:            generateID(),
		UserID:        req.UserID,
		IDPProviderID: req.IDPProviderID,
		IDPSessionID:  req.IDPSessionID,
		AccessToken:   req.AccessToken,
		RefreshToken:  req.RefreshToken,
		TokenType:     req.TokenType,
		ExpiresAt:     req.ExpiresAt,
		Scope:         req.Scope,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.db.Create(session).Error; err != nil {
		return nil, fmt.Errorf("创建 IDP 会话失败: %w", err)
	}

	return session, nil
}

// GetIDPSession 获取 IDP 会话
func (s *idpService) GetIDPSession(ctx context.Context, sessionID string) (*IDPSession, error) {
	var session IDPSession
	if err := s.db.First(&session, "id = ?", sessionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 会话不存在")
		}
		return nil, fmt.Errorf("获取 IDP 会话失败: %w", err)
	}

	return &session, nil
}

// RefreshIDPSession 刷新 IDP 会话
func (s *idpService) RefreshIDPSession(ctx context.Context, sessionID string) (*IDPSession, error) {
	session, err := s.GetIDPSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// 这里应该调用 IDP 的刷新令牌接口
	// 简化实现，直接更新过期时间
	newExpiresAt := time.Now().Add(time.Hour)
	session.ExpiresAt = &newExpiresAt
	session.UpdatedAt = time.Now()

	if err := s.db.Save(session).Error; err != nil {
		return nil, fmt.Errorf("刷新 IDP 会话失败: %w", err)
	}

	return session, nil
}

// RevokeIDPSession 撤销 IDP 会话
func (s *idpService) RevokeIDPSession(ctx context.Context, sessionID string) error {
	if err := s.db.Delete(&IDPSession{}, "id = ?", sessionID).Error; err != nil {
		return fmt.Errorf("撤销 IDP 会话失败: %w", err)
	}

	s.logger.Info("撤销 IDP 会话成功", "session_id", sessionID)
	return nil
}

// FindUserByIDPAccount 根据 IDP 账号查找用户
func (s *idpService) FindUserByIDPAccount(ctx context.Context, providerID string, idpUserID string) (*User, error) {
	var account IDPAccount
	if err := s.db.Where("idp_provider_id = ? AND idp_user_id = ?", providerID, idpUserID).First(&account).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 账号不存在")
		}
		return nil, fmt.Errorf("查找 IDP 账号失败: %w", err)
	}

	var user User
	if err := s.db.First(&user, "id = ?", account.UserID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查找用户失败: %w", err)
	}

	return &user, nil
}

// GetUserIDPAccounts 获取用户的 IDP 账号列表
func (s *idpService) GetUserIDPAccounts(ctx context.Context, userID string) ([]*IDPAccount, error) {
	var accounts []*IDPAccount
	if err := s.db.Where("user_id = ?", userID).Find(&accounts).Error; err != nil {
		return nil, fmt.Errorf("获取用户 IDP 账号失败: %w", err)
	}

	return accounts, nil
}

// UnlinkIDPAccount 取消关联 IDP 账号
func (s *idpService) UnlinkIDPAccount(ctx context.Context, userID string, accountID string) error {
	// 检查账号是否属于当前用户
	var account IDPAccount
	if err := s.db.Where("id = ? AND user_id = ?", accountID, userID).First(&account).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("IDP 账号不存在")
		}
		return fmt.Errorf("查找 IDP 账号失败: %w", err)
	}

	// 删除账号
	if err := s.db.Delete(&account).Error; err != nil {
		return fmt.Errorf("删除 IDP 账号失败: %w", err)
	}

	// 删除相关的会话
	if err := s.db.Where("user_id = ? AND idp_provider_id = ?", userID, account.IDPProviderID).Delete(&IDPSession{}).Error; err != nil {
		s.logger.Warn("删除 IDP 会话失败", "error", err, "user_id", userID, "provider_id", account.IDPProviderID)
	}

	s.logger.Info("取消关联 IDP 账号成功", "user_id", userID, "account_id", accountID)
	return nil
}

// InitiateIDPAuth 发起 IDP 认证
func (s *idpService) InitiateIDPAuth(ctx context.Context, providerID string, redirectURL string) (*IDPAuthInitResponse, error) {
	// 获取 IDP 提供商
	provider, err := s.GetIDPProvider(ctx, providerID)
	if err != nil {
		return nil, err
	}

	if !provider.Enabled {
		return nil, fmt.Errorf("IDP 提供商已禁用")
	}

	// 生成状态参数和随机数
	state := generateID()
	nonce := generateID()

	// 构造认证 URL
	authURL := fmt.Sprintf("%s?client_id=%s&redirect_uri=%s&state=%s&nonce=%s&response_type=code&scope=openid email profile",
		provider.Config.AuthURL, provider.Config.ClientID, redirectURL, state, nonce)

	response := &IDPAuthInitResponse{
		AuthURL:   authURL,
		State:     state,
		Nonce:     nonce,
		ExpiresAt: time.Now().Add(time.Minute * 10).Unix(),
	}

	return response, nil
}

// HandleIDPCallback 处理 IDP 回调
func (s *idpService) HandleIDPCallback(ctx context.Context, providerID string, code string, state string) (*IDPAuthResult, error) {
	// 获取 IDP 提供商
	_, err := s.GetIDPProvider(ctx, providerID)
	if err != nil {
		return nil, err
	}

	// 这里应该调用 IDP 的令牌交换接口
	// 简化实现，直接返回模拟结果
	result := &IDPAuthResult{
		Success:   true,
		IsNewUser: false,
		IsLinked:  true,
	}

	return result, nil
}

// ValidateIDPToken 验证 IDP 令牌
func (s *idpService) ValidateIDPToken(ctx context.Context, providerID string, token string) (*IDPUserInfo, error) {
	// 这里应该调用 IDP 的令牌验证接口
	// 简化实现，直接返回模拟结果
	userInfo := &IDPUserInfo{
		ID:       "mock_user_id",
		Username: "mock_user",
		Email:    "<EMAIL>",
	}

	return userInfo, nil
}

// SyncUserFromIDP 同步单个用户
func (s *idpService) SyncUserFromIDP(ctx context.Context, accountID string) error {
	return s.syncUserFromIDPInternal(ctx, accountID)
}

// syncUserFromIDPInternal 内部同步用户方法
func (s *idpService) syncUserFromIDPInternal(ctx context.Context, accountID string) error {
	// 获取 IDP 账号信息
	var account IDPAccount
	if err := s.db.First(&account, "id = ?", accountID).Error; err != nil {
		return fmt.Errorf("获取 IDP 账号失败: %w", err)
	}

	// 这里应该调用 IDP API 获取最新的用户信息
	// 简化实现，直接更新最后同步时间
	now := time.Now()
	account.LastSyncAt = &now
	account.UpdatedAt = now

	if err := s.db.Save(&account).Error; err != nil {
		return fmt.Errorf("更新 IDP 账号失败: %w", err)
	}

	s.logger.Info("同步用户成功", "account_id", accountID)
	return nil
}

// generateID 生成唯一 ID
func generateID() string {
	// 这里应该使用 UUID 生成器，简化示例
	return fmt.Sprintf("id_%d", time.Now().UnixNano())
}
