package auth

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// IDPAuthMiddleware IDP 认证中间件
type IDPAuthMiddleware struct {
	db         *gorm.DB
	jwtService JWTService
	idpService IDPService
	logger     Logger
	config     IDPAuthConfig
}

// IDPAuthConfig IDP 认证配置
type IDPAuthConfig struct {
	Enabled            bool     `json:"enabled"`              // 是否启用 IDP 认证
	IDPFirst           bool     `json:"idp_first"`            // IDP 优先模式（默认 true）
	AllowLocalAuth     bool     `json:"allow_local_auth"`     // 是否允许本地认证
	AdminOnlyLocalAuth bool     `json:"admin_only_local_auth"` // 本地认证仅限管理员
	SkipPaths          []string `json:"skip_paths"`           // 跳过认证的路径
	AdminPaths         []string `json:"admin_paths"`          // 管理员专用路径
	TokenHeader        string   `json:"token_header"`         // 令牌头名称
	TokenPrefix        string   `json:"token_prefix"`         // 令牌前缀
	CacheTTL           int      `json:"cache_ttl"`            // 缓存 TTL（秒）
}

// NewIDPAuthMiddleware 创建 IDP 认证中间件
func NewIDPAuthMiddleware(db *gorm.DB, jwtService JWTService, idpService IDPService, logger Logger, config IDPAuthConfig) *IDPAuthMiddleware {
	return &IDPAuthMiddleware{
		db:         db,
		jwtService: jwtService,
		idpService: idpService,
		logger:     logger,
		config:     config,
	}
}

// Handler 返回 Gin 中间件处理函数
func (m *IDPAuthMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否跳过认证
		if m.shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// 获取认证令牌
		token := m.extractToken(c)
		if token == "" {
			m.respondUnauthorized(c, "MISSING_TOKEN", "缺少认证令牌")
			return
		}
		
		// 尝试验证令牌
		if err := m.validateToken(c, token); err != nil {
			m.logger.Warn("令牌验证失败", "error", err.Error(), "path", c.Request.URL.Path)
			m.respondUnauthorized(c, "INVALID_TOKEN", "认证令牌无效")
			return
		}
		
		c.Next()
	}
}

// validateToken 验证令牌（IDP 优先模式）
func (m *IDPAuthMiddleware) validateToken(c *gin.Context, token string) error {
	// 检查是否为管理员路径
	isAdminPath := m.isAdminPath(c.Request.URL.Path)

	// IDP 优先模式
	if m.config.IDPFirst && m.config.Enabled {
		// 首先尝试验证 IDP 令牌
		if err := m.validateIDPToken(c, token); err == nil {
			m.logger.Debug("IDP 认证成功")
			return nil
		}
	}

	// 本地认证验证
	if m.config.AllowLocalAuth {
		// 如果启用了管理员专用本地认证，检查路径和用户类型
		if m.config.AdminOnlyLocalAuth && !isAdminPath {
			// 非管理员路径不允许本地认证
			m.logger.Debug("非管理员路径，跳过本地认证")
		} else {
			if claims, err := m.jwtService.ValidateToken(token); err == nil {
				// 如果是管理员专用模式，验证用户类型
				if m.config.AdminOnlyLocalAuth && isAdminPath {
					// 对于管理员路径，需要验证用户类型
					if err := m.validateAdminUser(claims.UserID); err != nil {
						m.logger.Warn("非管理员用户尝试访问管理员路径", "user_id", claims.UserID)
						return fmt.Errorf("权限不足")
					}
				}

				m.setUserContextFromJWT(c, claims)
				m.logger.Debug("JWT 认证成功", "user_id", claims.UserID)
				return nil
			}
		}
	}

	// 如果 IDP 优先模式未启用，再次尝试 IDP 认证
	if !m.config.IDPFirst && m.config.Enabled {
		if err := m.validateIDPToken(c, token); err == nil {
			m.logger.Debug("IDP 认证成功")
			return nil
		}
	}

	return fmt.Errorf("令牌验证失败")
}

// validateIDPToken 验证 IDP 令牌
func (m *IDPAuthMiddleware) validateIDPToken(c *gin.Context, token string) error {
	// 从数据库查找 IDP 会话
	var session IDPSession
	if err := m.db.Where("access_token = ?", token).First(&session).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("IDP 会话不存在")
		}
		return fmt.Errorf("查询 IDP 会话失败: %w", err)
	}
	
	// 检查令牌是否过期
	if session.ExpiresAt != nil && time.Now().After(*session.ExpiresAt) {
		// 尝试刷新令牌
		if err := m.refreshIDPToken(&session); err != nil {
			return fmt.Errorf("IDP 令牌已过期且刷新失败: %w", err)
		}
	}
	
	// 获取用户信息
	var user User
	if err := m.db.Preload("IDPAccounts").First(&user, "id = ?", session.UserID).Error; err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}
	
	// 设置用户上下文
	m.setUserContextFromIDP(c, &user, &session)
	
	return nil
}

// refreshIDPToken 刷新 IDP 令牌
func (m *IDPAuthMiddleware) refreshIDPToken(session *IDPSession) error {
	if session.RefreshToken == "" {
		return fmt.Errorf("没有刷新令牌")
	}
	
	// 这里应该调用 IDP 的刷新令牌接口
	// 简化实现，实际应该根据不同的 IDP 类型调用相应的刷新接口
	refreshedSession, err := m.idpService.RefreshIDPSession(context.Background(), session.ID)
	if err != nil {
		return fmt.Errorf("刷新 IDP 令牌失败: %w", err)
	}
	
	*session = *refreshedSession
	return nil
}

// setUserContextFromJWT 从 JWT 设置用户上下文
func (m *IDPAuthMiddleware) setUserContextFromJWT(c *gin.Context, claims *Claims) {
	c.Set("user_id", claims.UserID)
	c.Set("tenant_id", claims.TenantID)
	c.Set("username", claims.Username)
	c.Set("email", claims.Email)
	c.Set("roles", claims.Roles)
	c.Set("permissions", claims.Permissions)
	c.Set("session_id", claims.SessionID)
	c.Set("auth_type", "jwt")
}

// setUserContextFromIDP 从 IDP 设置用户上下文
func (m *IDPAuthMiddleware) setUserContextFromIDP(c *gin.Context, user *User, session *IDPSession) {
	// 获取用户角色和权限
	var roles []string
	var permissions []string
	
	for _, userRole := range user.UserRoles {
		roles = append(roles, userRole.Role.Name)
		permissions = append(permissions, userRole.Role.Permissions...)
	}
	
	c.Set("user_id", user.ID)
	c.Set("tenant_id", user.TenantID)
	c.Set("username", user.Username)
	c.Set("email", user.Email)
	c.Set("roles", roles)
	c.Set("permissions", permissions)
	c.Set("session_id", session.ID)
	c.Set("auth_type", "idp")
	c.Set("idp_provider_id", session.IDPProviderID)
}

// extractToken 提取认证令牌
func (m *IDPAuthMiddleware) extractToken(c *gin.Context) string {
	// 从 Authorization 头获取
	authHeader := c.GetHeader(m.config.TokenHeader)
	if authHeader != "" && strings.HasPrefix(authHeader, m.config.TokenPrefix) {
		return strings.TrimPrefix(authHeader, m.config.TokenPrefix)
	}
	
	// 从查询参数获取
	if token := c.Query("access_token"); token != "" {
		return token
	}
	
	// 从 Cookie 获取
	if token, err := c.Cookie("access_token"); err == nil && token != "" {
		return token
	}
	
	return ""
}

// shouldSkipAuth 检查是否应该跳过认证
func (m *IDPAuthMiddleware) shouldSkipAuth(path string) bool {
	for _, skipPath := range m.config.SkipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// isAdminPath 检查是否为管理员路径
func (m *IDPAuthMiddleware) isAdminPath(path string) bool {
	for _, adminPath := range m.config.AdminPaths {
		if strings.HasPrefix(path, adminPath) {
			return true
		}
	}
	return false
}

// validateAdminUser 验证用户是否为管理员
func (m *IDPAuthMiddleware) validateAdminUser(userID string) error {
	var user User
	if err := m.db.First(&user, "id = ?", userID).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	if user.UserType != UserTypeAdmin {
		return fmt.Errorf("用户不是管理员")
	}

	return nil
}

// respondUnauthorized 返回未授权响应
func (m *IDPAuthMiddleware) respondUnauthorized(c *gin.Context, code string, message string) {
	c.JSON(401, gin.H{
		"error": gin.H{
			"code":      code,
			"message":   message,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	})
	c.Abort()
}

// IDPFirstAuthMiddleware IDP 优先认证中间件
// 默认使用 IDP 认证，管理员路径支持本地认证
type IDPFirstAuthMiddleware struct {
	idpMiddleware *IDPAuthMiddleware
	jwtService    JWTService
	db            *gorm.DB
	logger        Logger
	config        IDPFirstAuthConfig
}

// IDPFirstAuthConfig IDP 优先认证配置
type IDPFirstAuthConfig struct {
	AdminPaths         []string `json:"admin_paths"`          // 管理员专用路径
	AllowAdminLocalAuth bool     `json:"allow_admin_local_auth"` // 是否允许管理员本地认证
	SkipPaths          []string `json:"skip_paths"`           // 跳过认证的路径
}

// NewIDPFirstAuthMiddleware 创建 IDP 优先认证中间件
func NewIDPFirstAuthMiddleware(idpMiddleware *IDPAuthMiddleware, jwtService JWTService, db *gorm.DB, logger Logger, config IDPFirstAuthConfig) *IDPFirstAuthMiddleware {
	return &IDPFirstAuthMiddleware{
		idpMiddleware: idpMiddleware,
		jwtService:    jwtService,
		db:            db,
		logger:        logger,
		config:        config,
	}
}

// Handler 返回 IDP 优先认证中间件处理函数
func (m *IDPFirstAuthMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// 检查是否跳过认证
		if m.shouldSkipAuth(path) {
			c.Next()
			return
		}

		// 获取认证令牌
		token := m.extractToken(c)
		if token == "" {
			m.respondUnauthorized(c, "MISSING_TOKEN", "缺少认证令牌")
			return
		}

		// 检查是否为管理员路径
		isAdminPath := m.isAdminPath(path)

		// 验证令牌
		if err := m.validateToken(c, token, isAdminPath); err != nil {
			m.logger.Warn("令牌验证失败", "error", err.Error(), "path", path, "is_admin_path", isAdminPath)
			m.respondUnauthorized(c, "INVALID_TOKEN", "认证令牌无效")
			return
		}

		c.Next()
	}
}

// validateToken 验证令牌（IDP 优先策略）
func (m *IDPFirstAuthMiddleware) validateToken(c *gin.Context, token string, isAdminPath bool) error {
	// 对于管理员路径，优先尝试本地认证
	if isAdminPath && m.config.AllowAdminLocalAuth {
		if claims, err := m.jwtService.ValidateToken(token); err == nil {
			// 验证用户是否为管理员
			if err := m.validateAdminUser(claims.UserID); err == nil {
				m.setUserContextFromJWT(c, claims)
				m.logger.Debug("管理员本地认证成功", "user_id", claims.UserID)
				return nil
			}
		}
	}

	// 尝试 IDP 认证
	if err := m.idpMiddleware.validateIDPToken(c, token); err == nil {
		m.logger.Debug("IDP 认证成功")
		return nil
	}

	// 对于非管理员路径，不允许本地认证
	if !isAdminPath {
		return fmt.Errorf("普通用户必须使用 IDP 认证")
	}

	return fmt.Errorf("认证失败")
}

// shouldSkipAuth 检查是否应该跳过认证
func (m *IDPFirstAuthMiddleware) shouldSkipAuth(path string) bool {
	for _, skipPath := range m.config.SkipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// isAdminPath 检查是否为管理员路径
func (m *IDPFirstAuthMiddleware) isAdminPath(path string) bool {
	for _, adminPath := range m.config.AdminPaths {
		if strings.HasPrefix(path, adminPath) {
			return true
		}
	}
	return false
}

// validateAdminUser 验证用户是否为管理员
func (m *IDPFirstAuthMiddleware) validateAdminUser(userID string) error {
	var user User
	if err := m.db.First(&user, "id = ?", userID).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	if user.UserType != UserTypeAdmin {
		return fmt.Errorf("用户不是管理员")
	}

	return nil
}

// setUserContextFromJWT 从 JWT 设置用户上下文
func (m *IDPFirstAuthMiddleware) setUserContextFromJWT(c *gin.Context, claims *Claims) {
	c.Set("user_id", claims.UserID)
	c.Set("tenant_id", claims.TenantID)
	c.Set("username", claims.Username)
	c.Set("email", claims.Email)
	c.Set("roles", claims.Roles)
	c.Set("permissions", claims.Permissions)
	c.Set("session_id", claims.SessionID)
	c.Set("auth_type", "jwt")
	c.Set("user_type", UserTypeAdmin) // 本地认证的用户都是管理员
}

// extractToken 提取认证令牌
func (m *IDPFirstAuthMiddleware) extractToken(c *gin.Context) string {
	return m.idpMiddleware.extractToken(c)
}

// respondUnauthorized 返回未授权响应
func (m *IDPFirstAuthMiddleware) respondUnauthorized(c *gin.Context, code string, message string) {
	m.idpMiddleware.respondUnauthorized(c, code, message)
}

// JWTAuthMiddleware JWT 认证中间件接口
type JWTAuthMiddleware interface {
	Handler() gin.HandlerFunc
}

// HybridAuthMiddleware 混合认证中间件（向后兼容）
type HybridAuthMiddleware struct {
	jwtMiddleware JWTAuthMiddleware
	idpMiddleware *IDPAuthMiddleware
	logger        Logger
}

// NewHybridAuthMiddleware 创建混合认证中间件
func NewHybridAuthMiddleware(jwtMiddleware JWTAuthMiddleware, idpMiddleware *IDPAuthMiddleware, logger Logger) *HybridAuthMiddleware {
	return &HybridAuthMiddleware{
		jwtMiddleware: jwtMiddleware,
		idpMiddleware: idpMiddleware,
		logger:        logger,
	}
}

// Handler 返回混合认证中间件处理函数
func (m *HybridAuthMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否跳过认证
		if m.shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// 获取认证令牌
		token := m.extractToken(c)
		if token == "" {
			m.respondUnauthorized(c, "MISSING_TOKEN", "缺少认证令牌")
			return
		}
		
		// 尝试 JWT 认证
		if err := m.tryJWTAuth(c, token); err == nil {
			c.Next()
			return
		}
		
		// 尝试 IDP 认证
		if err := m.tryIDPAuth(c, token); err == nil {
			c.Next()
			return
		}
		
		// 所有认证方式都失败
		m.logger.Warn("混合认证失败", "path", c.Request.URL.Path)
		m.respondUnauthorized(c, "INVALID_TOKEN", "认证令牌无效")
	}
}

// tryJWTAuth 尝试 JWT 认证
func (m *HybridAuthMiddleware) tryJWTAuth(c *gin.Context, token string) error {
	// 创建一个新的上下文来测试 JWT 认证
	testCtx := c.Copy()
	testCtx.Request.Header.Set("Authorization", "Bearer "+token)
	
	// 使用 JWT 中间件处理
	m.jwtMiddleware.Handler()(testCtx)
	
	// 检查是否认证成功
	if testCtx.IsAborted() {
		return fmt.Errorf("JWT 认证失败")
	}
	
	// 复制认证信息到原始上下文
	m.copyAuthContext(testCtx, c)
	return nil
}

// tryIDPAuth 尝试 IDP 认证
func (m *HybridAuthMiddleware) tryIDPAuth(c *gin.Context, token string) error {
	// 创建一个新的上下文来测试 IDP 认证
	testCtx := c.Copy()
	testCtx.Request.Header.Set("Authorization", "Bearer "+token)
	
	// 使用 IDP 中间件处理
	m.idpMiddleware.Handler()(testCtx)
	
	// 检查是否认证成功
	if testCtx.IsAborted() {
		return fmt.Errorf("IDP 认证失败")
	}
	
	// 复制认证信息到原始上下文
	m.copyAuthContext(testCtx, c)
	return nil
}

// copyAuthContext 复制认证上下文
func (m *HybridAuthMiddleware) copyAuthContext(from *gin.Context, to *gin.Context) {
	authKeys := []string{
		"user_id", "tenant_id", "username", "email", "roles", 
		"permissions", "session_id", "auth_type", "idp_provider_id",
	}
	
	for _, key := range authKeys {
		if value, exists := from.Get(key); exists {
			to.Set(key, value)
		}
	}
}

// extractToken 提取认证令牌
func (m *HybridAuthMiddleware) extractToken(c *gin.Context) string {
	return m.idpMiddleware.extractToken(c)
}

// shouldSkipAuth 检查是否应该跳过认证
func (m *HybridAuthMiddleware) shouldSkipAuth(path string) bool {
	return m.idpMiddleware.shouldSkipAuth(path)
}

// respondUnauthorized 返回未授权响应
func (m *HybridAuthMiddleware) respondUnauthorized(c *gin.Context, code string, message string) {
	m.idpMiddleware.respondUnauthorized(c, code, message)
}
