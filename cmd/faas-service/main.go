package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"paas-platform/internal/container"
	"paas-platform/internal/database"
	"paas-platform/internal/faas"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"
	"gorm.io/gorm"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title FaaS 服务 API
// @version 1.0
// @description PaaS 平台的 Function as a Service (FaaS) 执行器服务
// @termsOfService https://paas-platform.com/terms

// @contact.name PaaS 平台支持团队
// @contact.url https://paas-platform.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8087
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description 输入 "Bearer {token}" 进行身份验证

var (
	configFile = flag.String("config", "", "配置文件路径")
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	flag.Parse()

	// 打印版本信息
	fmt.Printf("FaaS 服务启动中...\n")
	fmt.Printf("版本: %s\n", version)
	fmt.Printf("构建时间: %s\n", buildTime)
	fmt.Printf("Git 提交: %s\n", gitCommit)

	// 初始化配置
	initConfig()

	// 初始化日志
	logger := logger.NewLogger()

	// 初始化数据库
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 自动迁移数据库表
	if err := migrateFaaSTables(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化容器管理器
	containerManager, err := container.NewDockerManager(logger)
	if err != nil {
		log.Fatalf("初始化容器管理器失败: %v", err)
	}

	// 初始化 FaaS 执行器配置
	executorConfig := &faas.ExecutorConfig{
		MaxConcurrentExecutions: viper.GetInt("executor.max_concurrent_executions"),
		DefaultTimeout:          viper.GetDuration("executor.default_timeout"),
		MaxExecutionTime:        viper.GetDuration("executor.max_execution_time"),
		ContainerPoolSize:       viper.GetInt("executor.container_pool_size"),
		PrewarmContainers:       viper.GetInt("executor.prewarm_containers"),
		CleanupInterval:         viper.GetDuration("executor.cleanup_interval"),
		ResourceLimits: &faas.ResourceLimits{
			CPULimit:    viper.GetString("executor.resource_limits.cpu_limit"),
			MemoryLimit: viper.GetString("executor.resource_limits.memory_limit"),
			DiskLimit:   viper.GetString("executor.resource_limits.disk_limit"),
		},
	}

	// 初始化 FaaS 执行器
	executor := faas.NewFunctionExecutor(containerManager, executorConfig, logger)

	// 初始化 FaaS 处理器
	faasHandler := faas.NewHandler(executor, logger)

	// 初始化 Gin 路由
	router := setupRouter(faasHandler, logger)

	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler:      router,
		ReadTimeout:  viper.GetDuration("server.read_timeout"),
		WriteTimeout: viper.GetDuration("server.write_timeout"),
		IdleTimeout:  viper.GetDuration("server.idle_timeout"),
	}

	// 优雅启动
	go func() {
		logger.Info("FaaS 服务启动", 
			"port", viper.GetInt("server.port"),
			"mode", viper.GetString("server.mode"),
			"version", version)
		
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()

	// 启动后台清理任务
	go startBackgroundTasks(executor, logger)

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭 FaaS 服务...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭 HTTP 服务器
	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("服务关闭失败", "error", err)
	}

	// 关闭容器池和清理资源
	if executor != nil {
		logger.Info("正在清理 FaaS 执行器资源...")
		// 这里应该调用执行器的关闭方法
		// executor.Shutdown(ctx)
		logger.Info("FaaS 执行器资源清理完成")
	}

	logger.Info("FaaS 服务已关闭")
}

// initConfig 初始化配置
func initConfig() {
	// 设置默认值
	setDefaultConfig()

	// 如果指定了配置文件，直接使用
	if *configFile != "" {
		viper.SetConfigFile(*configFile)
	} else {
		// 否则按默认方式查找配置文件
		viper.SetConfigName("faas-service")
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")
	}

	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("FAAS_SERVICE")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Printf("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	} else {
		// 输出使用的配置文件信息
		configPath := viper.ConfigFileUsed()
		if configPath != "" {
			configName := filepath.Base(configPath)
			if strings.Contains(configName, "dev") {
				log.Printf("✅ 使用开发环境配置文件: %s", configPath)
			} else {
				log.Printf("📄 使用配置文件: %s", configPath)
			}
		}
	}
}

// setDefaultConfig 设置默认配置
func setDefaultConfig() {
	// 服务器配置
	viper.SetDefault("server.port", 8087)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.mode", "release")
	viper.SetDefault("server.read_timeout", "60s")
	viper.SetDefault("server.write_timeout", "60s")
	viper.SetDefault("server.idle_timeout", "120s")

	// 数据库配置
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/faas-service.db")
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", 3600)
	viper.SetDefault("database.log_level", "warn")

	// FaaS 执行器配置
	viper.SetDefault("executor.max_concurrent_executions", 50)
	viper.SetDefault("executor.default_timeout", "30s")
	viper.SetDefault("executor.max_execution_time", "300s")
	viper.SetDefault("executor.container_pool_size", 10)
	viper.SetDefault("executor.prewarm_containers", 3)
	viper.SetDefault("executor.cleanup_interval", "300s")
	viper.SetDefault("executor.resource_limits.cpu_limit", "1.0")
	viper.SetDefault("executor.resource_limits.memory_limit", "1Gi")
	viper.SetDefault("executor.resource_limits.disk_limit", "2Gi")

	// 日志配置
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// 监控配置
	viper.SetDefault("monitoring.enabled", true)
	viper.SetDefault("monitoring.metrics_path", "/metrics")
	viper.SetDefault("monitoring.health_check.enabled", true)
	viper.SetDefault("monitoring.health_check.path", "/health")
}

// setupRouter 设置路由
func setupRouter(faasHandler *faas.Handler, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))

	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())

	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "faas-service",
			"version":   version,
			"timestamp": time.Now().Format(time.RFC3339),
			"message":   "FaaS 服务运行正常",
		})
	})

	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	// 版本信息接口
	router.GET("/version", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"version":    version,
			"build_time": buildTime,
			"git_commit": gitCommit,
		})
	})

	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		// 集成 Swagger UI
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

		// 添加 API 文档重定向
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})

		// 添加 API 文档信息接口
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "FaaS 服务 API",
				"version":     version,
				"description": "PaaS 平台的 Function as a Service (FaaS) 执行器服务",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}

	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 添加认证中间件
		v1.Use(middleware.Auth())

		// FaaS 路由组
		faasGroup := v1.Group("/faas")
		{
			// 注册 FaaS 路由
			faasHandler.RegisterRoutes(faasGroup)
		}
	}

	// 监控指标接口
	if viper.GetBool("monitoring.enabled") {
		// 使用简单的指标处理函数
		router.GET(viper.GetString("monitoring.metrics_path"), func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"service": "faas-service",
				"status":  "healthy",
				"metrics": gin.H{
					"active_functions": 0, // TODO: 实现实际指标
					"total_executions": 0,
					"error_rate":       0.0,
				},
			})
		})
	}

	return router
}

// migrateFaaSTables 迁移 FaaS 相关数据库表
func migrateFaaSTables(db *gorm.DB) error {
	// 迁移 FaaS 相关表
	err := db.AutoMigrate(
		&faas.Function{},
		&faas.FunctionExecution{},
		&faas.FunctionVersion{},
		&faas.FunctionMetrics{},
		&faas.FunctionTrigger{},
	)
	if err != nil {
		return fmt.Errorf("FaaS 表迁移失败: %w", err)
	}

	log.Println("FaaS 数据库表迁移完成")
	return nil
}

// startBackgroundTasks 启动后台任务
func startBackgroundTasks(executor *faas.FunctionExecutor, logger logger.Logger) {
	// 定期清理容器池和执行记录
	ticker := time.NewTicker(viper.GetDuration("executor.cleanup_interval"))
	defer ticker.Stop()

	logger.Info("启动 FaaS 后台清理任务", "interval", viper.GetDuration("executor.cleanup_interval"))

	for {
		select {
		case <-ticker.C:
			logger.Debug("执行 FaaS 后台清理任务")

			// 这里可以添加具体的清理逻辑
			// 例如：清理过期的执行记录、释放空闲容器等
			// executor.CleanupExpiredExecutions()
			// executor.CleanupIdleContainers()

			logger.Debug("FaaS 后台清理任务完成")
		}
	}
}
