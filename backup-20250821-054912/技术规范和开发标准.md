# PaaS 平台技术规范和开发标准

## 📋 文档概述

本文档定义了 PaaS 平台项目的技术规范、开发标准和最佳实践，确保代码质量、系统稳定性和团队协作效率。

## 🏗️ 架构规范

### 1. 微服务架构原则

#### 1.1 服务拆分原则
- **单一职责**: 每个服务只负责一个业务领域
- **高内聚低耦合**: 服务内部功能紧密相关，服务间依赖最小
- **数据独立**: 每个服务拥有独立的数据存储
- **接口稳定**: 服务间通过稳定的 API 接口通信

#### 1.2 服务通信规范
```yaml
# 服务间通信协议
protocols:
  - HTTP/REST: 主要通信协议
  - gRPC: 高性能内部通信
  - Message Queue: 异步任务处理
  - WebSocket: 实时数据推送

# API 版本管理
versioning:
  strategy: URL 路径版本控制
  format: "/api/v{major}/..."
  compatibility: 向后兼容至少 2 个版本
```

#### 1.3 数据一致性策略
- **最终一致性**: 分布式事务采用最终一致性
- **补偿机制**: 实现 Saga 模式处理分布式事务
- **幂等性**: 所有写操作必须支持幂等性
- **事件驱动**: 使用事件驱动架构处理跨服务数据同步

### 2. 技术栈标准

#### 2.1 后端技术栈
```yaml
language: Go 1.21+
framework: Gin Web Framework
database:
  primary: PostgreSQL 15+
  cache: Redis 7+
  search: Elasticsearch 8+ (可选)
orm: GORM v2
authentication: JWT
logging: Structured logging (JSON format)
monitoring: Prometheus + Grafana
```

#### 2.2 前端技术栈
```yaml
framework: Vue.js 3.4+
language: TypeScript 5.3+
build_tool: Vite 5.0+
ui_library: Element Plus 2.4+
state_management: Pinia 2.1+
routing: Vue Router 4.2+
http_client: Axios 1.6+
charts: ECharts 5.4+
testing: Vitest + Vue Test Utils
```

#### 2.3 基础设施技术栈
```yaml
containerization: Docker 24+
orchestration: Kubernetes 1.28+
service_mesh: Istio (可选)
ci_cd: GitLab CI/CD 或 GitHub Actions
registry: Docker Registry 或 Harbor
monitoring: Prometheus + Grafana + AlertManager
logging: ELK Stack (Elasticsearch + Logstash + Kibana)
```

## 💻 编码规范

### 1. Go 语言规范

#### 1.1 代码结构
```go
// 包声明和导入
package service

import (
    "context"
    "fmt"
    
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    
    "paas-platform/internal/auth"
    "paas-platform/pkg/logger"
)

// 常量定义
const (
    DefaultTimeout = 30 * time.Second
    MaxRetries     = 3
)

// 类型定义
type Service struct {
    db     *gorm.DB
    logger logger.Logger
}

// 构造函数
func NewService(db *gorm.DB, logger logger.Logger) *Service {
    return &Service{
        db:     db,
        logger: logger,
    }
}

// 方法实现
func (s *Service) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    // 参数验证
    if err := req.Validate(); err != nil {
        return nil, fmt.Errorf("参数验证失败: %w", err)
    }
    
    // 业务逻辑
    user := &User{
        Name:  req.Name,
        Email: req.Email,
    }
    
    // 数据库操作
    if err := s.db.WithContext(ctx).Create(user).Error; err != nil {
        s.logger.Error("创建用户失败", "error", err, "request", req)
        return nil, fmt.Errorf("创建用户失败: %w", err)
    }
    
    s.logger.Info("用户创建成功", "user_id", user.ID)
    return user, nil
}
```

#### 1.2 命名规范
- **包名**: 小写，简短，有意义 (如 `auth`, `config`)
- **函数名**: 驼峰命名，动词开头 (如 `CreateUser`, `GetConfig`)
- **变量名**: 驼峰命名，名词 (如 `userID`, `configValue`)
- **常量名**: 大写，下划线分隔 (如 `MAX_RETRY_COUNT`)
- **接口名**: 以 `er` 结尾 (如 `UserService`, `ConfigManager`)

#### 1.3 错误处理
```go
// 自定义错误类型
type ValidationError struct {
    Field   string
    Message string
}

func (e *ValidationError) Error() string {
    return fmt.Sprintf("字段 %s 验证失败: %s", e.Field, e.Message)
}

// 错误包装
func (s *Service) processUser(ctx context.Context, userID string) error {
    user, err := s.getUser(ctx, userID)
    if err != nil {
        return fmt.Errorf("获取用户失败: %w", err)
    }
    
    if err := s.validateUser(user); err != nil {
        return fmt.Errorf("用户验证失败: %w", err)
    }
    
    return nil
}
```

### 2. TypeScript/Vue.js 规范

#### 2.1 组件结构
```vue
<template>
  <div class="user-management">
    <el-card class="user-card">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleCreate">
            创建用户
          </el-button>
        </div>
      </template>
      
      <el-table :data="users" :loading="loading">
        <el-table-column prop="name" label="用户名" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { User } from '@/types/user'
import { userApi } from '@/api/user'

// 响应式数据
const users = ref<User[]>([])
const loading = ref(false)

// 生命周期
onMounted(() => {
  fetchUsers()
})

// 方法定义
const fetchUsers = async () => {
  try {
    loading.value = true
    const response = await userApi.getUsers()
    users.value = response.data
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  // 创建用户逻辑
}

const handleEdit = (user: User) => {
  // 编辑用户逻辑
}
</script>

<style scoped lang="scss">
.user-management {
  padding: 20px;
  
  .user-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
```

#### 2.2 类型定义
```typescript
// 基础类型定义
export interface User {
  id: string
  name: string
  email: string
  roles: Role[]
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role_ids: string[]
}

export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// API 接口定义
export interface UserApi {
  getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>>
  createUser(data: CreateUserRequest): Promise<ApiResponse<User>>
  updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>>
  deleteUser(id: string): Promise<ApiResponse<void>>
}
```

## 🗄️ 数据库规范

### 1. 表设计规范

#### 1.1 命名规范
- **表名**: 小写，下划线分隔，复数形式 (如 `users`, `user_roles`)
- **字段名**: 小写，下划线分隔 (如 `user_id`, `created_at`)
- **索引名**: `idx_表名_字段名` (如 `idx_users_email`)
- **外键名**: `fk_表名_字段名` (如 `fk_users_tenant_id`)

#### 1.2 字段规范
```sql
-- 标准字段定义
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL COMMENT '用户名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '用户状态',
    tenant_id UUID NOT NULL COMMENT '租户ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    INDEX idx_users_email (email),
    INDEX idx_users_tenant_id (tenant_id),
    INDEX idx_users_status (status),
    INDEX idx_users_deleted_at (deleted_at)
);
```

#### 1.3 数据类型规范
- **主键**: UUID 类型，使用 `gen_random_uuid()`
- **字符串**: VARCHAR，指定合适长度
- **时间**: TIMESTAMP，UTC 时区
- **布尔值**: BOOLEAN 或 TINYINT(1)
- **枚举**: VARCHAR，配合应用层验证
- **JSON**: JSON 类型 (PostgreSQL) 或 TEXT (SQLite)

### 2. 查询优化规范

#### 2.1 索引策略
- **单列索引**: 频繁查询的字段
- **复合索引**: 多字段组合查询
- **唯一索引**: 业务唯一性约束
- **部分索引**: 条件索引优化存储

#### 2.2 查询优化
```go
// 好的查询实践
func (r *UserRepository) GetActiveUsers(ctx context.Context, tenantID string, limit, offset int) ([]User, error) {
    var users []User
    
    // 使用索引，限制返回字段，分页查询
    err := r.db.WithContext(ctx).
        Select("id, name, email, created_at").
        Where("tenant_id = ? AND status = ? AND deleted_at IS NULL", tenantID, "active").
        Order("created_at DESC").
        Limit(limit).
        Offset(offset).
        Find(&users).Error
        
    return users, err
}

// 避免 N+1 查询
func (r *UserRepository) GetUsersWithRoles(ctx context.Context, tenantID string) ([]User, error) {
    var users []User
    
    // 使用 Preload 预加载关联数据
    err := r.db.WithContext(ctx).
        Preload("Roles").
        Where("tenant_id = ? AND deleted_at IS NULL", tenantID).
        Find(&users).Error
        
    return users, err
}
```

## 🧪 测试规范

### 1. 测试策略

#### 1.1 测试金字塔
```
    /\
   /  \     E2E Tests (10%)
  /____\    
 /      \   Integration Tests (20%)
/________\  Unit Tests (70%)
```

#### 1.2 测试类型
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试服务间集成
- **端到端测试**: 测试完整业务流程
- **性能测试**: 测试系统性能和负载

### 2. 单元测试规范

#### 2.1 Go 单元测试
```go
package service

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "gorm.io/gorm"
)

// 测试用例结构
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name    string
        request *CreateUserRequest
        setup   func(*MockDB)
        want    *User
        wantErr bool
    }{
        {
            name: "成功创建用户",
            request: &CreateUserRequest{
                Name:  "测试用户",
                Email: "<EMAIL>",
            },
            setup: func(mockDB *MockDB) {
                mockDB.On("Create", mock.AnythingOfType("*User")).Return(nil)
            },
            want: &User{
                Name:  "测试用户",
                Email: "<EMAIL>",
            },
            wantErr: false,
        },
        {
            name: "邮箱已存在",
            request: &CreateUserRequest{
                Name:  "测试用户",
                Email: "<EMAIL>",
            },
            setup: func(mockDB *MockDB) {
                mockDB.On("Create", mock.AnythingOfType("*User")).
                    Return(gorm.ErrDuplicatedKey)
            },
            want:    nil,
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 设置 mock
            mockDB := &MockDB{}
            tt.setup(mockDB)
            
            // 创建服务实例
            service := NewUserService(mockDB, &MockLogger{})
            
            // 执行测试
            ctx := context.Background()
            got, err := service.CreateUser(ctx, tt.request)
            
            // 验证结果
            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, got)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.want.Name, got.Name)
                assert.Equal(t, tt.want.Email, got.Email)
            }
            
            // 验证 mock 调用
            mockDB.AssertExpectations(t)
        })
    }
}
```

#### 2.2 前端单元测试
```typescript
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserManagement from '@/views/UserManagement.vue'
import { userApi } from '@/api/user'

// Mock API
vi.mock('@/api/user', () => ({
  userApi: {
    getUsers: vi.fn(),
    createUser: vi.fn(),
  }
}))

describe('UserManagement', () => {
  it('应该正确渲染用户列表', async () => {
    // 准备测试数据
    const mockUsers = [
      { id: '1', name: '用户1', email: '<EMAIL>' },
      { id: '2', name: '用户2', email: '<EMAIL>' },
    ]
    
    // 设置 mock 返回值
    vi.mocked(userApi.getUsers).mockResolvedValue({
      code: 200,
      message: 'success',
      data: mockUsers
    })
    
    // 挂载组件
    const wrapper = mount(UserManagement)
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    // 验证渲染结果
    expect(wrapper.find('.user-management').exists()).toBe(true)
    expect(wrapper.findAll('.user-row')).toHaveLength(2)
    expect(wrapper.text()).toContain('用户1')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('应该处理创建用户操作', async () => {
    const wrapper = mount(UserManagement)
    
    // 模拟点击创建按钮
    await wrapper.find('.create-button').trigger('click')
    
    // 验证创建对话框显示
    expect(wrapper.find('.create-dialog').isVisible()).toBe(true)
  })
})
```

## 📝 文档规范

### 1. 代码注释规范

#### 1.1 Go 注释规范
```go
// Package auth 提供用户认证和授权功能
//
// 本包实现了基于 JWT 的用户认证机制，支持多租户和 RBAC 权限模型。
// 主要功能包括：
//   - 用户登录和注销
//   - JWT 令牌生成和验证
//   - 权限检查和角色管理
//
// 使用示例：
//   authService := auth.NewAuthService(db, jwtService, logger)
//   token, err := authService.Login(ctx, "username", "password")
package auth

// AuthService 认证服务接口
//
// AuthService 定义了用户认证相关的核心方法，包括登录、注销、
// 令牌验证等功能。实现类需要确保线程安全。
type AuthService interface {
    // Login 用户登录
    //
    // 验证用户凭据并生成访问令牌。如果认证失败，返回相应的错误信息。
    //
    // 参数：
    //   ctx: 请求上下文，用于超时控制和取消操作
    //   username: 用户名，不能为空
    //   password: 密码，不能为空
    //
    // 返回值：
    //   *LoginResponse: 包含访问令牌和用户信息的响应对象
    //   error: 认证失败时的错误信息
    //
    // 错误类型：
    //   - ErrInvalidCredentials: 用户名或密码错误
    //   - ErrUserDisabled: 用户账户被禁用
    //   - ErrTooManyAttempts: 登录尝试次数过多
    Login(ctx context.Context, username, password string) (*LoginResponse, error)
}
```

#### 1.2 TypeScript 注释规范
```typescript
/**
 * 用户管理 API 接口
 * 
 * 提供用户的 CRUD 操作，包括创建、查询、更新和删除用户。
 * 所有操作都需要适当的权限验证。
 * 
 * @example
 * ```typescript
 * const userApi = new UserApi()
 * const users = await userApi.getUsers({ page: 1, size: 10 })
 * ```
 */
export class UserApi {
  /**
   * 获取用户列表
   * 
   * 支持分页查询和条件筛选，返回符合条件的用户列表。
   * 
   * @param params - 查询参数
   * @param params.page - 页码，从 1 开始
   * @param params.size - 每页大小，默认 20
   * @param params.keyword - 搜索关键词，支持用户名和邮箱模糊匹配
   * @param params.status - 用户状态筛选
   * 
   * @returns Promise<ApiResponse<PaginatedResponse<User>>> 分页用户列表
   * 
   * @throws {ApiError} 当请求失败时抛出 API 错误
   * 
   * @example
   * ```typescript
   * // 获取第一页用户
   * const response = await userApi.getUsers({ page: 1, size: 10 })
   * console.log(response.data.items) // 用户列表
   * 
   * // 搜索用户
   * const searchResult = await userApi.getUsers({ 
   *   keyword: 'admin',
   *   status: 'active'
   * })
   * ```
   */
  async getUsers(params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<User>>> {
    // 实现代码...
  }
}
```

### 2. API 文档规范

#### 2.1 OpenAPI 规范
```yaml
# API 文档示例
paths:
  /api/v1/users:
    get:
      summary: 获取用户列表
      description: |
        获取系统中的用户列表，支持分页查询和条件筛选。
        
        ## 权限要求
        - 需要 `user:read` 权限
        - 只能查看当前租户下的用户
        
        ## 查询说明
        - 支持按用户名、邮箱进行模糊搜索
        - 支持按状态、角色进行精确筛选
        - 默认按创建时间倒序排列
      tags:
        - 用户管理
      parameters:
        - name: page
          in: query
          description: 页码，从 1 开始
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: 每页大小，最大 100
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: 成功返回用户列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'
        '400':
          description: 请求参数错误
        '401':
          description: 未授权访问
        '403':
          description: 权限不足
```

---

*本文档将根据项目发展和技术演进持续更新*
*最后更新时间: 2025-08-15*
