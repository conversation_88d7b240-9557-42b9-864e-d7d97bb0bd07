# PaaS 平台本地开发指南

## 概述

本指南介绍如何在单台 Docker 服务器上进行 PaaS 平台的本地开发，无需使用 Docker 镜像仓库。这种配置专为开发环境优化，简化了部署流程，提高了开发效率。

## 为什么不需要镜像仓库？

在单台服务器开发环境中，Docker 镜像仓库并非必需，原因如下：

### 优点
- **简化配置**: 无需配置和维护镜像仓库服务
- **节省资源**: 减少内存和存储占用
- **加快开发**: 直接本地构建，无需推送/拉取镜像
- **降低复杂度**: 减少网络配置和认证设置

### 适用场景
- 单人开发或小团队开发
- 原型开发和功能验证
- 本地测试和调试
- 学习和实验环境

## 配置文件说明

### 1. docker-compose.local.yml
专为本地开发优化的 Docker Compose 配置文件：
- 移除了镜像仓库服务
- 使用本地构建模式
- 优化了资源配置
- 支持可选的完整环境（包括 Gitea）

### 2. configs/cicd-service.local.yaml
CI/CD 服务的本地开发配置：
- 设置 `registry.mode: "local"`
- 启用 `build.local_only: true`
- 优化构建参数
- 启用开发模式特性

### 3. .env.local
本地环境变量配置：
- 数据库连接信息
- JWT 密钥配置
- 开发模式开关
- 日志级别设置

## 快速开始

### 1. 启动基础开发环境

```bash
# 使用便捷脚本启动
./scripts/start-local-dev.sh start

# 或者后台启动
./scripts/start-local-dev.sh start --detach

# 直接使用 docker-compose
docker-compose -f docker-compose.local.yml up -d
```

### 2. 启动完整环境（包括 Gitea）

```bash
# 启动包含 Gitea 的完整环境
./scripts/start-local-dev.sh start --full

# 使用 docker-compose profile
docker-compose -f docker-compose.local.yml --profile full up -d
```

### 3. 查看服务状态

```bash
# 查看所有服务状态
./scripts/start-local-dev.sh status

# 查看特定服务日志
./scripts/start-local-dev.sh logs api-gateway
```

## 服务访问信息

启动后，您可以通过以下地址访问各个服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| API Gateway | http://localhost:8080 | 主要 API 入口 |
| User Service | http://localhost:8083 | 用户认证服务 |
| App Manager | http://localhost:8081 | 应用管理服务 |
| Config Service | http://localhost:8084 | 配置管理服务 |
| CI/CD Service | http://localhost:8082 | 持续集成服务 |
| PostgreSQL | localhost:5432 | 数据库服务 |
| Redis | localhost:6379 | 缓存服务 |
| Gitea | http://localhost:3000 | 代码仓库（可选） |

## 开发工作流

### 1. 代码修改和测试

```bash
# 修改代码后重新构建特定服务
docker-compose -f docker-compose.local.yml build api-gateway

# 重启特定服务
docker-compose -f docker-compose.local.yml restart api-gateway

# 查看服务日志
docker-compose -f docker-compose.local.yml logs -f api-gateway
```

### 2. 数据库操作

```bash
# 连接到 PostgreSQL
docker-compose -f docker-compose.local.yml exec postgres psql -U paas -d paas_platform

# 执行数据库迁移
docker-compose -f docker-compose.local.yml exec api-gateway /app/migrate
```

### 3. 调试和排错

```bash
# 进入容器进行调试
./scripts/start-local-dev.sh shell api-gateway

# 查看容器资源使用情况
docker stats

# 查看网络连接
docker network ls
docker network inspect paas-network
```

## 本地构建模式

### CI/CD 服务配置

在本地开发模式下，CI/CD 服务配置为：

```yaml
docker:
  registry:
    mode: "local"           # 本地模式
    default_url: ""         # 不使用镜像仓库
  build:
    local_only: true        # 仅本地构建
    use_cache: true         # 使用构建缓存
    parallel_build: true    # 并行构建
```

### 构建流程

1. **代码检出**: 从本地或 Gitea 获取代码
2. **本地构建**: 直接在本地 Docker 环境构建镜像
3. **本地部署**: 在同一网络中部署容器
4. **健康检查**: 验证服务是否正常运行

## 常用命令

### 环境管理

```bash
# 启动环境
./scripts/start-local-dev.sh start

# 停止环境
./scripts/start-local-dev.sh stop

# 重启环境
./scripts/start-local-dev.sh restart

# 清理环境（删除所有容器和数据）
./scripts/start-local-dev.sh clean
```

### 镜像管理

```bash
# 重新构建所有镜像
./scripts/start-local-dev.sh build

# 构建特定服务
docker-compose -f docker-compose.local.yml build user-service

# 查看本地镜像
docker images | grep paas
```

### 日志和监控

```bash
# 查看所有服务日志
./scripts/start-local-dev.sh logs

# 查看特定服务日志
./scripts/start-local-dev.sh logs api-gateway

# 实时监控资源使用
docker stats
```

## 性能优化建议

### 1. 构建优化

- 启用 Docker 构建缓存
- 使用多阶段构建减少镜像大小
- 并行构建多个服务

### 2. 资源配置

```yaml
# 在 docker-compose.local.yml 中调整资源限制
services:
  api-gateway:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

### 3. 开发体验优化

- 使用数据卷挂载源代码实现热重载
- 配置 IDE 的 Docker 集成
- 使用 Docker Compose 的 override 文件

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8080
   
   # 修改 docker-compose.local.yml 中的端口映射
   ```

2. **构建失败**
   ```bash
   # 清理构建缓存
   docker builder prune
   
   # 重新构建
   ./scripts/start-local-dev.sh build
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库容器状态
   docker-compose -f docker-compose.local.yml ps postgres
   
   # 查看数据库日志
   docker-compose -f docker-compose.local.yml logs postgres
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   # 设置环境变量
   export LOG_LEVEL=debug
   
   # 重启服务
   ./scripts/start-local-dev.sh restart
   ```

2. **网络调试**
   ```bash
   # 检查容器网络连接
   docker-compose -f docker-compose.local.yml exec api-gateway ping postgres
   ```

3. **性能分析**
   ```bash
   # 启用 pprof（如果服务支持）
   curl http://localhost:8080/debug/pprof/
   ```

## 迁移到生产环境

当需要部署到生产环境时，可以：

1. **启用镜像仓库**: 使用 `docker-compose.dev.yml` 配置
2. **配置 CI/CD**: 修改构建流程支持镜像推送
3. **环境隔离**: 使用不同的配置文件和环境变量

## 总结

本地开发环境配置通过移除 Docker 镜像仓库，简化了开发流程，提高了开发效率。这种配置特别适合：

- 快速原型开发
- 功能测试和验证
- 学习和实验
- 单人或小团队开发

当项目需要扩展到多环境部署时，可以轻松切换回使用镜像仓库的配置。
