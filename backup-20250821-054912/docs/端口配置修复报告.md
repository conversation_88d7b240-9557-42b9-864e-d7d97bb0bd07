# PaaS平台端口配置不一致问题修复报告

## 📋 问题概述

在PaaS平台开发环境中发现了多个服务的端口配置不一致问题，导致服务启动失败或端口冲突。

## 🔍 发现的问题

### 1. 应用管理服务 (app-manager)
- **问题**: Swagger文档@host配置错误
- **原因**: 代码中@host设置为`localhost:8082`，但实际应该是`localhost:8081`
- **影响**: API文档显示错误的服务地址

### 2. 配置服务 (config-service)
- **问题**: 配置文件结构不一致
- **原因**: 开发配置文件使用`server.port: 8083`，但代码读取的是`port: 8083`
- **影响**: 配置服务启动失败，端口冲突

### 3. 配置文件读取机制
- **问题**: 服务无法正确读取开发环境配置文件
- **原因**: 启动脚本传递`--config`参数，但服务代码未处理该参数
- **影响**: 服务使用默认配置而非开发配置

### 4. 脚本配置错误
- **问题**: `start-config-service.sh`脚本中端口配置错误
- **原因**: 脚本中硬编码了8084端口，但应该是8083
- **影响**: 独立启动配置服务时端口错误

## 🔧 修复方案

### 1. 修复Swagger文档配置
```go
// cmd/app-manager/main.go
// @host localhost:8081  // 修复前: localhost:8082
```

### 2. 修复配置文件结构
```yaml
# configs/config-service.dev.yaml
# 服务端口
port: 8083

# 服务器配置
server:
  mode: debug
  # 移除了重复的port配置
```

### 3. 增强配置文件读取机制
为所有服务添加了命令行参数支持：
```go
// 解析命令行参数
var configFile string
flag.StringVar(&configFile, "config", "", "配置文件路径")
flag.Parse()

// 如果指定了配置文件，直接使用
if configFile != "" {
    viper.SetConfigFile(configFile)
} else {
    // 否则按默认方式查找配置文件
    viper.SetConfigName("service-name")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./configs")
    viper.AddConfigPath(".")
}
```

### 4. 修复脚本配置
```bash
# scripts/start-config-service.sh
# 修复端口检查和环境变量
if lsof -Pi :8083 -sTCP:LISTEN -t >/dev/null; then
    log_error "端口 8083 已被占用"
fi
export PORT=${PORT:-8083}
```

### 5. 更新生成的文档
```go
// docs/docs.go
Host: "localhost:8081",  // 修复前: localhost:8082
```

## ✅ 修复结果验证

### 端口分配确认
```
COMMAND      PID USER   FD   TYPE    DEVICE SIZE/OFF NODE NAME
app-manag 444464 root    6u  IPv6 270811072      0t0  TCP *:8081 (LISTEN)
script-se 444497 root    6u  IPv6 270811084      0t0  TCP *:8084 (LISTEN)
cicd-serv 444522 root    6u  IPv6 270811092      0t0  TCP *:8082 (LISTEN)
config-se 444544 root    6u  IPv6 270834360      0t0  TCP *:8083 (LISTEN)
```

### 配置文件使用确认
- ✅ 应用管理服务: 使用 `configs/app-manager.dev.yaml`, 端口 8081
- ✅ 脚本执行服务: 使用 `configs/script-service.dev.yaml`, 端口 8084
- ✅ CI/CD服务: 使用 `configs/cicd-service.dev.yaml`, 端口 8082
- ✅ 配置服务: 使用 `configs/config-service.dev.yaml`, 端口 8083

### 服务状态确认
```
╔════════════════════════════════════════════════════════════╗
║                        服务状态                            ║
╠════════════════════════════════════════════════════════════╣
║  📱 应用管理服务:    http://localhost:8081                ║
║  📜 脚本执行服务:    http://localhost:8084                ║
║  🚀 CI/CD服务:       http://localhost:8082                ║
║  ⚙️  配置服务:        http://localhost:8083                ║
╠════════════════════════════════════════════════════════════╣
║  📚 API文档:                                               ║
║    - 应用管理: http://localhost:8081/swagger               ║
║    - 脚本执行: http://localhost:8084/swagger               ║
║    - CI/CD:    http://localhost:8082/swagger               ║
║    - 配置服务: http://localhost:8083/swagger               ║
╚════════════════════════════════════════════════════════════╝
```

## 📝 修复的文件清单

1. **cmd/app-manager/main.go** - 修复Swagger @host配置，增加配置文件参数支持
2. **cmd/cicd-service/main.go** - 增加配置文件参数支持
3. **cmd/script-service/main.go** - 增加配置文件参数支持
4. **cmd/config-service/main.go** - 修复默认端口，增加配置文件参数支持
5. **configs/config-service.dev.yaml** - 修复配置文件结构
6. **scripts/start-config-service.sh** - 修复端口配置
7. **docs/docs.go** - 修复生成的Swagger文档host配置

## 🎯 最终端口分配

| 服务名称 | 端口 | 配置文件 | 状态 |
|---------|------|----------|------|
| 应用管理服务 | 8081 | app-manager.dev.yaml | ✅ 正常 |
| CI/CD服务 | 8082 | cicd-service.dev.yaml | ✅ 正常 |
| 配置服务 | 8083 | config-service.dev.yaml | ✅ 正常 |
| 脚本执行服务 | 8084 | script-service.dev.yaml | ✅ 正常 |

## 🔄 后续建议

1. **配置管理标准化**: 建议所有服务统一使用相同的配置文件结构
2. **自动化测试**: 添加端口配置一致性检查的自动化测试
3. **文档同步**: 确保Swagger文档与实际配置保持同步
4. **配置验证**: 在启动脚本中添加配置文件验证逻辑

## ✨ 修复完成

所有端口配置不一致问题已修复，服务现在能够：
- ✅ 正确读取开发环境配置文件
- ✅ 在指定端口启动服务
- ✅ 通过健康检查
- ✅ 显示正确的API文档地址
- ✅ 避免端口冲突

修复后的系统现在具有完全一致的端口配置，所有服务都能正常启动和运行。
