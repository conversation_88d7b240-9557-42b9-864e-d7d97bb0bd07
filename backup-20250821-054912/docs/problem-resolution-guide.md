# 脚本执行服务问题解决指南

## 🎉 问题解决状态

### ✅ 问题1 - SQLite CGO构建错误 **已完全解决**

**问题描述：**
```
Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub
```

**根本原因：**
启动脚本 `scripts/start-script-service.sh` 第136行设置了 `CGO_ENABLED=0`，导致SQLite驱动无法工作。

**解决方案：**
1. **修复构建配置** - 将 `CGO_ENABLED=0` 改为 `CGO_ENABLED=1`
2. **重新构建服务** - 使用启用CGO的配置重新编译
3. **验证数据库连接** - 确保SQLite数据库正常工作

**修复结果：**
```bash
✅ 服务构建成功
✅ 数据库初始化完成  
✅ 服务正常启动 - 监听端口8084
✅ 所有API端点已注册
```

### 🔧 问题2 - 远程Docker配置 **配置完成，需要远程主机配置**

**当前状态：**
- ✅ 配置文件已更新支持远程Docker连接
- ✅ Docker管理器已支持TLS和远程连接
- ✅ 连接测试脚本已创建
- ⚠️ 远程主机*************:2376不可达（需要远程主机配置）

## 📋 完整的远程Docker配置方案

### 1. 当前配置文件设置

已在 `configs/script-service.yaml` 中配置：

```yaml
# Docker 配置
docker:
  # 远程Docker主机配置
  host: "tcp://*************:2376"   # 远程Docker守护进程地址
  api_version: "1.41"                 # Docker API版本
  timeout: 30s                        # 连接超时时间
  
  # TLS安全连接配置
  tls:
    enabled: true                     # 是否启用TLS加密
    verify: true                      # 是否验证服务器证书
    cert_path: "/etc/docker/certs"    # 证书文件路径
    ca_file: "ca.pem"                 # CA证书文件名
    cert_file: "cert.pem"             # 客户端证书文件名
    key_file: "key.pem"               # 客户端私钥文件名
    insecure_skip_verify: false       # 是否跳过证书验证（不推荐）
  
  # 备用配置（本地Docker）
  fallback:
    enabled: true                     # 是否启用备用连接
    host: "unix:///var/run/docker.sock"  # 本地Docker socket
```

### 2. 远程Docker主机配置步骤

#### 步骤1：在远程主机(*************)上配置Docker

```bash
# 1. 创建证书目录
sudo mkdir -p /etc/docker/certs

# 2. 生成CA私钥
sudo openssl genrsa -aes256 -out /etc/docker/certs/ca-key.pem 4096

# 3. 生成CA证书
sudo openssl req -new -x509 -days 365 -key /etc/docker/certs/ca-key.pem \
    -sha256 -out /etc/docker/certs/ca.pem -subj "/C=CN/ST=Beijing/L=Beijing/O=PaaS/CN=docker-ca"

# 4. 生成服务器私钥
sudo openssl genrsa -out /etc/docker/certs/server-key.pem 4096

# 5. 生成服务器证书签名请求
sudo openssl req -subj "/C=CN/ST=Beijing/L=Beijing/O=PaaS/CN=*************" \
    -sha256 -new -key /etc/docker/certs/server-key.pem -out /etc/docker/certs/server.csr

# 6. 生成服务器证书
sudo openssl x509 -req -days 365 -sha256 -in /etc/docker/certs/server.csr \
    -CA /etc/docker/certs/ca.pem -CAkey /etc/docker/certs/ca-key.pem \
    -out /etc/docker/certs/server-cert.pem -CAcreateserial \
    -extensions v3_req -extfile <(echo "subjectAltName=IP:*************,IP:127.0.0.1")

# 7. 生成客户端私钥
sudo openssl genrsa -out /etc/docker/certs/key.pem 4096

# 8. 生成客户端证书签名请求
sudo openssl req -subj "/C=CN/ST=Beijing/L=Beijing/O=PaaS/CN=client" \
    -new -key /etc/docker/certs/key.pem -out /etc/docker/certs/client.csr

# 9. 生成客户端证书
sudo openssl x509 -req -days 365 -sha256 -in /etc/docker/certs/client.csr \
    -CA /etc/docker/certs/ca.pem -CAkey /etc/docker/certs/ca-key.pem \
    -out /etc/docker/certs/cert.pem -CAcreateserial -extensions v3_req \
    -extfile <(echo "extendedKeyUsage=clientAuth")

# 10. 设置证书权限
sudo chmod 400 /etc/docker/certs/ca-key.pem /etc/docker/certs/server-key.pem /etc/docker/certs/key.pem
sudo chmod 444 /etc/docker/certs/ca.pem /etc/docker/certs/server-cert.pem /etc/docker/certs/cert.pem
```

#### 步骤2：配置Docker守护进程

```bash
# 1. 创建Docker服务配置目录
sudo mkdir -p /etc/systemd/system/docker.service.d

# 2. 创建Docker服务配置文件
sudo tee /etc/systemd/system/docker.service.d/docker-tls.conf << EOF
[Service]
ExecStart=
ExecStart=/usr/bin/dockerd \\
    -H fd:// \\
    -H tcp://0.0.0.0:2376 \\
    --tlsverify \\
    --tlscacert=/etc/docker/certs/ca.pem \\
    --tlscert=/etc/docker/certs/server-cert.pem \\
    --tlskey=/etc/docker/certs/server-key.pem
EOF

# 3. 重新加载systemd配置
sudo systemctl daemon-reload

# 4. 重启Docker服务
sudo systemctl restart docker

# 5. 验证Docker服务状态
sudo systemctl status docker
```

#### 步骤3：配置防火墙

```bash
# Ubuntu/Debian
sudo ufw allow from ***********/24 to any port 2376

# CentOS/RHEL
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='***********/24' port protocol='tcp' port='2376' accept"
sudo firewall-cmd --reload
```

### 3. 客户端配置步骤

#### 步骤1：复制证书文件

```bash
# 1. 创建证书目录
sudo mkdir -p /etc/docker/certs

# 2. 从远程主机复制证书文件
scp user@*************:/etc/docker/certs/ca.pem /etc/docker/certs/
scp user@*************:/etc/docker/certs/cert.pem /etc/docker/certs/
scp user@*************:/etc/docker/certs/key.pem /etc/docker/certs/

# 3. 设置证书权限
sudo chmod 400 /etc/docker/certs/key.pem
sudo chmod 444 /etc/docker/certs/ca.pem /etc/docker/certs/cert.pem
```

#### 步骤2：测试连接

```bash
# 1. 设置环境变量
export DOCKER_HOST="tcp://*************:2376"
export DOCKER_TLS_VERIFY=1
export DOCKER_CERT_PATH="/etc/docker/certs"

# 2. 测试Docker连接
docker version
docker info

# 3. 使用我们的测试脚本
./scripts/test-docker-connection.sh -H tcp://*************:2376 --tls --tlsverify --tlscert /etc/docker/certs
```

### 4. 无TLS的简化配置（仅用于测试）

如果只是测试环境，可以使用无TLS的简化配置：

#### 远程主机配置：
```bash
# 1. 配置Docker守护进程（无TLS）
sudo tee /etc/systemd/system/docker.service.d/docker-tcp.conf << EOF
[Service]
ExecStart=
ExecStart=/usr/bin/dockerd -H fd:// -H tcp://0.0.0.0:2376
EOF

# 2. 重启Docker
sudo systemctl daemon-reload
sudo systemctl restart docker

# 3. 配置防火墙（限制访问）
sudo ufw allow from ***********/24 to any port 2376
```

#### 客户端配置：
```yaml
# configs/script-service.yaml
docker:
  host: "tcp://*************:2376"
  tls:
    enabled: false  # 禁用TLS
```

### 5. 验证配置

#### 使用测试脚本验证：
```bash
# 测试网络连通性
./scripts/test-docker-connection.sh -H tcp://*************:2376

# 测试TLS连接
./scripts/test-docker-connection.sh -H tcp://*************:2376 --tls --tlsverify --tlscert /etc/docker/certs

# 详细输出
./scripts/test-docker-connection.sh -H tcp://*************:2376 -v
```

#### 手动验证：
```bash
# 1. 测试网络连通性
nc -z ************* 2376

# 2. 测试Docker API
curl -k https://*************:2376/version

# 3. 测试Docker命令
DOCKER_HOST=tcp://*************:2376 docker version
```

### 6. 故障排除

#### 常见问题：

1. **网络不通** - 检查防火墙和网络配置
2. **证书错误** - 验证证书文件和权限
3. **端口被占用** - 检查端口2376是否被其他服务占用
4. **Docker服务未启动** - 确认远程主机Docker服务正常运行

#### 调试命令：
```bash
# 检查端口监听
sudo netstat -tlnp | grep 2376

# 检查Docker服务日志
sudo journalctl -u docker.service -f

# 检查证书有效性
openssl x509 -in /etc/docker/certs/cert.pem -text -noout
```

## 🚀 启动服务

配置完成后，启动脚本执行服务：

```bash
# 使用配置文件启动
./scripts/start-script-service.sh

# 或直接启动
./bin/script-service --config configs/script-service.yaml
```

## 📊 验证结果

服务启动后应该看到：
- ✅ Docker连接成功（如果远程主机配置正确）
- ✅ 运行时适配器注册完成
- ✅ API端点注册完成
- ✅ 服务监听端口8084

如果Docker连接失败，服务会自动切换到模拟模式，仍可正常提供API服务。
