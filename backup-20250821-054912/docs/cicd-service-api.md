# CI/CD 服务 API 文档

## 概述

CI/CD 服务提供完整的持续集成和持续部署功能，支持流水线管理、构建执行、部署管理等核心功能。

## 基础信息

- **服务名称**: cicd-service
- **默认端口**: 8082
- **API 版本**: v1
- **基础路径**: `/api/v1`

## 认证

所有 API 请求都需要在请求头中包含有效的 JWT Token：

```
Authorization: Bearer <your-jwt-token>
```

## 流水线管理

### 创建流水线

创建新的 CI/CD 流水线配置。

**请求**
```
POST /api/v1/pipelines
Content-Type: application/json

{
  "name": "示例应用流水线",
  "description": "用于构建和部署示例应用的流水线",
  "app_id": "app-123",
  "config": {
    "apiVersion": "v1",
    "kind": "Pipeline",
    "metadata": {
      "name": "example-app-pipeline",
      "description": "示例应用的 CI/CD 流水线"
    },
    "spec": {
      "triggers": [
        {
          "type": "push",
          "branches": ["main", "develop"]
        }
      ],
      "stages": [
        {
          "name": "构建",
          "steps": [
            {
              "name": "检出代码",
              "type": "git_checkout",
              "config": {
                "repository": "https://github.com/example/app.git"
              }
            },
            {
              "name": "安装依赖",
              "type": "shell",
              "config": {
                "commands": ["npm install"]
              }
            },
            {
              "name": "构建应用",
              "type": "shell",
              "config": {
                "commands": ["npm run build"]
              }
            },
            {
              "name": "构建镜像",
              "type": "docker_build",
              "config": {
                "dockerfile": "Dockerfile",
                "tags": ["example-app:${BUILD_NUMBER}"]
              }
            }
          ]
        },
        {
          "name": "部署",
          "steps": [
            {
              "name": "推送镜像",
              "type": "docker_push",
              "config": {
                "image": "example-app:${BUILD_NUMBER}",
                "registry": "localhost:5000"
              }
            },
            {
              "name": "部署到开发环境",
              "type": "deploy",
              "config": {
                "environment": "development",
                "strategy": "rolling"
              }
            }
          ]
        }
      ]
    }
  }
}
```

**响应**
```json
{
  "id": "pipeline-abc123",
  "name": "示例应用流水线",
  "description": "用于构建和部署示例应用的流水线",
  "app_id": "app-123",
  "status": "active",
  "config": { ... },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 获取流水线详情

根据流水线 ID 获取详细信息。

**请求**
```
GET /api/v1/pipelines/{id}
```

**响应**
```json
{
  "id": "pipeline-abc123",
  "name": "示例应用流水线",
  "description": "用于构建和部署示例应用的流水线",
  "app_id": "app-123",
  "status": "active",
  "config": { ... },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 获取流水线列表

获取指定应用的流水线列表。

**请求**
```
GET /api/v1/pipelines?app_id=app-123&page=1&page_size=20
```

**查询参数**
- `app_id` (必需): 应用 ID
- `status`: 流水线状态过滤 (active, inactive, deleted)
- `page`: 页码，默认为 1
- `page_size`: 每页大小，默认为 20

**响应**
```json
{
  "pipelines": [
    {
      "id": "pipeline-abc123",
      "name": "示例应用流水线",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 1
}
```

### 更新流水线

更新流水线配置信息。

**请求**
```
PUT /api/v1/pipelines/{id}
Content-Type: application/json

{
  "description": "更新后的流水线描述",
  "config": { ... },
  "status": "active"
}
```

### 删除流水线

删除指定的流水线（软删除）。

**请求**
```
DELETE /api/v1/pipelines/{id}
```

**响应**
```json
{
  "message": "流水线删除成功"
}
```

## 构建管理

### 创建构建任务

手动触发构建任务。

**请求**
```
POST /api/v1/builds
Content-Type: application/json

{
  "pipeline_id": "pipeline-abc123",
  "branch": "main",
  "commit_hash": "abc123def456",
  "commit_msg": "修复登录问题",
  "author": "developer",
  "trigger_type": "manual",
  "priority": 1
}
```

**响应**
```json
{
  "id": "build-xyz789",
  "pipeline_id": "pipeline-abc123",
  "number": 15,
  "status": "pending",
  "branch": "main",
  "commit_hash": "abc123def456",
  "commit_msg": "修复登录问题",
  "author": "developer",
  "trigger_type": "manual",
  "created_at": "2024-01-15T11:00:00Z"
}
```

### 获取构建详情

获取构建任务的详细信息，包括阶段和步骤。

**请求**
```
GET /api/v1/builds/{id}
```

**响应**
```json
{
  "id": "build-xyz789",
  "pipeline_id": "pipeline-abc123",
  "number": 15,
  "status": "running",
  "branch": "main",
  "commit_hash": "abc123def456",
  "start_time": "2024-01-15T11:01:00Z",
  "stages": [
    {
      "id": "stage-123",
      "name": "构建",
      "status": "running",
      "order": 1,
      "steps": [
        {
          "id": "step-456",
          "name": "检出代码",
          "type": "git_checkout",
          "status": "success",
          "order": 1,
          "start_time": "2024-01-15T11:01:00Z",
          "end_time": "2024-01-15T11:01:30Z",
          "duration": 30
        }
      ]
    }
  ]
}
```

### 获取构建列表

获取构建任务列表。

**请求**
```
GET /api/v1/builds?pipeline_id=pipeline-abc123&status=success&page=1&page_size=20
```

**查询参数**
- `pipeline_id`: 流水线 ID 过滤
- `status`: 构建状态过滤 (pending, running, success, failed, canceled)
- `branch`: 分支过滤
- `author`: 作者过滤
- `page`: 页码，默认为 1
- `page_size`: 每页大小，默认为 20

### 取消构建

取消正在运行的构建任务。

**请求**
```
POST /api/v1/builds/{id}/cancel
```

**响应**
```json
{
  "message": "构建任务已取消"
}
```

### 重试构建

重新执行失败的构建任务。

**请求**
```
POST /api/v1/builds/{id}/retry
```

**响应**
```json
{
  "id": "build-new123",
  "pipeline_id": "pipeline-abc123",
  "number": 16,
  "status": "pending",
  "message": "构建任务已重新创建"
}
```

### 获取构建日志

获取构建任务的详细日志。

**请求**
```
GET /api/v1/builds/{id}/logs?level=INFO&stage=构建&limit=100
```

**查询参数**
- `level`: 日志级别过滤 (DEBUG, INFO, WARN, ERROR)
- `stage`: 阶段过滤
- `step`: 步骤过滤
- `start_time`: 开始时间过滤
- `end_time`: 结束时间过滤
- `limit`: 限制数量，默认为 100
- `offset`: 偏移量，默认为 0

**响应**
```json
[
  {
    "id": "log-123",
    "build_id": "build-xyz789",
    "timestamp": "2024-01-15T11:01:00Z",
    "level": "INFO",
    "stage": "构建",
    "step": "检出代码",
    "message": "开始检出代码...",
    "source": "system"
  }
]
```

### 实时日志流

通过 WebSocket 获取实时构建日志。

**WebSocket 连接**
```
ws://localhost:8082/api/v1/builds/{id}/logs/stream
```

**消息格式**
```json
{
  "type": "log",
  "build_id": "build-xyz789",
  "entry": {
    "timestamp": "2024-01-15T11:01:00Z",
    "level": "INFO",
    "stage": "构建",
    "step": "检出代码",
    "message": "检出代码完成"
  }
}
```

### 获取构建产物

获取构建任务生成的产物列表。

**请求**
```
GET /api/v1/builds/{id}/artifacts
```

**响应**
```json
[
  {
    "name": "app.tar.gz",
    "type": "archive",
    "size": 1048576,
    "url": "/api/v1/artifacts/download/abc123",
    "created_at": "2024-01-15T11:05:00Z"
  }
]
```

## 部署管理

### 创建部署

创建新的部署任务。

**请求**
```
POST /api/v1/deployments
Content-Type: application/json

{
  "app_id": "app-123",
  "build_id": "build-xyz789",
  "environment": "production",
  "version": "v1.2.3",
  "image_tag": "example-app:15",
  "strategy": "rolling",
  "config": {
    "replicas": 3,
    "resources": {
      "cpu": "500m",
      "memory": "512Mi"
    }
  }
}
```

**响应**
```json
{
  "id": "deploy-abc123",
  "app_id": "app-123",
  "build_id": "build-xyz789",
  "environment": "production",
  "version": "v1.2.3",
  "status": "pending",
  "start_time": "2024-01-15T12:00:00Z"
}
```

### 获取部署详情

获取部署任务的详细信息。

**请求**
```
GET /api/v1/deployments/{id}
```

### 获取部署列表

获取部署任务列表。

**请求**
```
GET /api/v1/deployments?app_id=app-123&environment=production&page=1&page_size=20
```

### 回滚部署

回滚到上一个成功的部署版本。

**请求**
```
POST /api/v1/deployments/{id}/rollback
```

**响应**
```json
{
  "message": "部署回滚成功"
}
```

## 环境管理

### 创建环境

创建新的部署环境。

**请求**
```
POST /api/v1/environments
Content-Type: application/json

{
  "name": "production",
  "description": "生产环境",
  "type": "production",
  "cluster": "prod-cluster",
  "namespace": "default",
  "config": {
    "auto_deploy": false,
    "approval_required": true
  }
}
```

### 获取环境列表

获取租户的环境列表。

**请求**
```
GET /api/v1/environments
```

## 构建节点管理

### 注册构建节点

注册新的构建节点。

**请求**
```
POST /api/v1/nodes
Content-Type: application/json

{
  "name": "build-node-01",
  "address": "*************:8080",
  "capacity": 5,
  "labels": {
    "os": "linux",
    "arch": "amd64"
  }
}
```

### 获取构建节点列表

获取所有构建节点列表。

**请求**
```
GET /api/v1/nodes
```

### 更新节点状态

更新构建节点的状态。

**请求**
```
PUT /api/v1/nodes/{id}/status
Content-Type: application/json

{
  "status": "online"
}
```

## 统计信息

### 获取构建统计

获取构建任务的统计信息。

**请求**
```
GET /api/v1/stats/builds
```

**响应**
```json
{
  "total_builds": 100,
  "success_builds": 85,
  "failed_builds": 15,
  "success_rate": 85.0,
  "avg_duration": 300.5,
  "today_builds": 10
}
```

### 获取节点统计

获取构建节点的统计信息。

**请求**
```
GET /api/v1/stats/nodes
```

**响应**
```json
{
  "total_nodes": 5,
  "online_nodes": 4,
  "offline_nodes": 1,
  "busy_nodes": 2,
  "total_capacity": 25,
  "used_capacity": 8
}
```

## Webhook 集成

### Gitea Webhook

接收 Gitea 的 Webhook 事件。

**请求**
```
POST /api/v1/webhooks/gitea
Content-Type: application/json
X-Gitea-Signature: sha256=<signature>

{
  "ref": "refs/heads/main",
  "before": "abc123",
  "after": "def456",
  "repository": {
    "full_name": "example/app"
  },
  "commits": [...]
}
```

### GitHub Webhook

接收 GitHub 的 Webhook 事件。

**请求**
```
POST /api/v1/webhooks/github
Content-Type: application/json
X-Hub-Signature-256: sha256=<signature>

{
  "ref": "refs/heads/main",
  "repository": {
    "full_name": "example/app"
  }
}
```

### GitLab Webhook

接收 GitLab 的 Webhook 事件。

**请求**
```
POST /api/v1/webhooks/gitlab
Content-Type: application/json
X-Gitlab-Token: <token>

{
  "object_kind": "push",
  "ref": "refs/heads/main",
  "project": {
    "path_with_namespace": "example/app"
  }
}
```

## 错误码

| 错误码 | 描述 |
|--------|------|
| INVALID_REQUEST | 请求参数格式错误 |
| PIPELINE_NOT_FOUND | 流水线不存在 |
| BUILD_NOT_FOUND | 构建任务不存在 |
| DEPLOYMENT_NOT_FOUND | 部署任务不存在 |
| ENVIRONMENT_NOT_FOUND | 环境不存在 |
| NODE_NOT_FOUND | 构建节点不存在 |
| UNAUTHORIZED | 未授权访问 |
| FORBIDDEN | 权限不足 |
| INTERNAL_ERROR | 服务器内部错误 |

## 状态码

### 构建状态
- `pending`: 等待中
- `running`: 运行中
- `success`: 成功
- `failed`: 失败
- `canceled`: 已取消
- `timeout`: 超时

### 部署状态
- `pending`: 等待中
- `running`: 部署中
- `success`: 成功
- `failed`: 失败
- `rolled_back`: 已回滚

### 节点状态
- `online`: 在线
- `offline`: 离线
- `busy`: 忙碌
- `maintenance`: 维护中

## 使用示例

### 完整的 CI/CD 流程示例

以下是一个完整的 CI/CD 流程示例，展示如何从创建流水线到部署应用的整个过程。

#### 1. 创建流水线

```bash
curl -X POST http://localhost:8082/api/v1/pipelines \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{
    "name": "Node.js 应用流水线",
    "description": "用于构建和部署 Node.js 应用",
    "app_id": "nodejs-app-001",
    "config": {
      "apiVersion": "v1",
      "kind": "Pipeline",
      "metadata": {
        "name": "nodejs-app-pipeline"
      },
      "spec": {
        "triggers": [
          {
            "type": "push",
            "branches": ["main", "develop"]
          }
        ],
        "stages": [
          {
            "name": "构建",
            "steps": [
              {
                "name": "检出代码",
                "type": "git_checkout"
              },
              {
                "name": "安装依赖",
                "type": "shell",
                "config": {
                  "commands": ["npm ci"]
                }
              },
              {
                "name": "运行测试",
                "type": "shell",
                "config": {
                  "commands": ["npm test"]
                }
              },
              {
                "name": "构建应用",
                "type": "shell",
                "config": {
                  "commands": ["npm run build"]
                }
              },
              {
                "name": "构建 Docker 镜像",
                "type": "docker_build",
                "config": {
                  "dockerfile": "Dockerfile",
                  "tags": ["nodejs-app:${BUILD_NUMBER}", "nodejs-app:latest"]
                }
              }
            ]
          },
          {
            "name": "部署",
            "steps": [
              {
                "name": "推送镜像",
                "type": "docker_push",
                "config": {
                  "image": "nodejs-app:${BUILD_NUMBER}"
                }
              },
              {
                "name": "部署到开发环境",
                "type": "deploy",
                "config": {
                  "environment": "development",
                  "strategy": "rolling"
                }
              }
            ]
          }
        ]
      }
    }
  }'
```

#### 2. 手动触发构建

```bash
curl -X POST http://localhost:8082/api/v1/builds \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{
    "pipeline_id": "pipeline-abc123",
    "branch": "main",
    "commit_hash": "a1b2c3d4e5f6",
    "commit_msg": "添加新功能",
    "author": "developer",
    "trigger_type": "manual",
    "priority": 1
  }'
```

#### 3. 监控构建进度

```bash
# 获取构建详情
curl -X GET http://localhost:8082/api/v1/builds/build-xyz789 \
  -H "Authorization: Bearer <your-token>"

# 获取实时日志（WebSocket）
wscat -c ws://localhost:8082/api/v1/builds/build-xyz789/logs/stream
```

#### 4. 部署到生产环境

```bash
curl -X POST http://localhost:8082/api/v1/deployments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{
    "app_id": "nodejs-app-001",
    "build_id": "build-xyz789",
    "environment": "production",
    "version": "v1.0.0",
    "image_tag": "nodejs-app:15",
    "strategy": "blue_green",
    "config": {
      "replicas": 3,
      "resources": {
        "cpu": "1000m",
        "memory": "1Gi"
      }
    }
  }'
```

### 配置 Webhook 自动触发

#### Gitea Webhook 配置

1. 在 Gitea 仓库设置中添加 Webhook
2. URL: `http://your-cicd-service:8082/api/v1/webhooks/gitea`
3. 密钥: 配置文件中的 `webhook.secret`
4. 事件: 选择 "Push events"

#### GitHub Webhook 配置

1. 在 GitHub 仓库设置中添加 Webhook
2. Payload URL: `http://your-cicd-service:8082/api/v1/webhooks/github`
3. Content type: `application/json`
4. Secret: 配置文件中的 `webhook.secret`
5. 事件: 选择 "Just the push event"

### 流水线配置最佳实践

#### 1. 阶段设计

```yaml
stages:
  - name: "代码检查"
    steps:
      - name: "代码格式检查"
        type: "shell"
        config:
          commands: ["npm run lint"]
      - name: "安全扫描"
        type: "shell"
        config:
          commands: ["npm audit"]

  - name: "测试"
    steps:
      - name: "单元测试"
        type: "shell"
        config:
          commands: ["npm run test:unit"]
      - name: "集成测试"
        type: "shell"
        config:
          commands: ["npm run test:integration"]

  - name: "构建"
    steps:
      - name: "构建应用"
        type: "shell"
        config:
          commands: ["npm run build"]
      - name: "构建镜像"
        type: "docker_build"

  - name: "部署"
    steps:
      - name: "部署到测试环境"
        type: "deploy"
        config:
          environment: "staging"
      - name: "自动化测试"
        type: "shell"
        config:
          commands: ["npm run test:e2e"]
      - name: "部署到生产环境"
        type: "deploy"
        config:
          environment: "production"
          approval_required: true
```

#### 2. 环境变量使用

流水线支持以下内置环境变量：

- `${BUILD_ID}`: 构建 ID
- `${BUILD_NUMBER}`: 构建编号
- `${PIPELINE_ID}`: 流水线 ID
- `${BRANCH}`: 分支名称
- `${COMMIT_HASH}`: 提交哈希
- `${COMMIT_MSG}`: 提交消息
- `${AUTHOR}`: 提交作者

#### 3. 缓存配置

```yaml
steps:
  - name: "恢复依赖缓存"
    type: "cache"
    config:
      action: "restore"
      key: "deps-${BRANCH}-${COMMIT_HASH}"
      paths: ["node_modules"]

  - name: "安装依赖"
    type: "shell"
    config:
      commands: ["npm ci"]

  - name: "保存依赖缓存"
    type: "cache"
    config:
      action: "save"
      key: "deps-${BRANCH}-${COMMIT_HASH}"
      paths: ["node_modules"]
```

## 故障排除

### 常见问题

#### 1. 构建失败

**问题**: 构建任务状态为 `failed`

**解决方案**:
1. 查看构建日志: `GET /api/v1/builds/{id}/logs`
2. 检查流水线配置是否正确
3. 验证构建环境和依赖是否满足要求
4. 检查构建节点状态和资源使用情况

#### 2. Webhook 未触发

**问题**: 代码推送后没有自动触发构建

**解决方案**:
1. 检查 Webhook 配置是否正确
2. 验证 Webhook 密钥是否匹配
3. 查看服务日志确认是否收到 Webhook 请求
4. 检查流水线的触发条件配置

#### 3. 部署失败

**问题**: 部署任务状态为 `failed`

**解决方案**:
1. 检查目标环境的连接性和权限
2. 验证镜像是否存在且可访问
3. 检查部署配置和资源限制
4. 查看部署日志获取详细错误信息

#### 4. 节点离线

**问题**: 构建节点状态为 `offline`

**解决方案**:
1. 检查节点服务是否正常运行
2. 验证网络连接是否正常
3. 检查节点资源使用情况
4. 重启节点服务或重新注册节点

### 性能优化

#### 1. 构建优化

- 使用构建缓存减少重复工作
- 并行执行独立的构建步骤
- 优化 Docker 镜像构建层次
- 使用多阶段构建减少镜像大小

#### 2. 资源管理

- 合理配置构建节点容量
- 监控节点资源使用情况
- 设置构建超时时间
- 定期清理构建工作空间

#### 3. 网络优化

- 使用本地镜像仓库
- 配置依赖包缓存代理
- 优化网络带宽和延迟
- 使用 CDN 加速资源下载
