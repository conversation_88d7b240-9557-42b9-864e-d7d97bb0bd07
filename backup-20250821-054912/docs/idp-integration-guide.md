# PaaS 平台 ID Provider (IDP) 优先认证集成指南

## 📋 概述

本指南详细介绍如何在 PaaS 平台中集成外部 Identity Provider (IDP)，实现 IDP 优先的认证模式。该方案将 IDP 认证设为主要认证方式，普通用户仅支持 IDP 认证，系统管理员保留本地认证能力。

## 🎯 功能特性

### ✅ 已实现功能

- **IDP 优先认证**：普通用户仅支持 IDP 认证，提供企业级身份管理
- **管理员本地认证**：系统管理员保留本地认证能力，确保系统可管理性
- **多种 IDP 类型**：支持 OIDC、OAuth2、SAML、LDAP 等主流协议
- **账号关联机制**：支持本地账号与 IDP 账号的灵活关联
- **自动用户创建**：从 IDP 自动创建本地用户，无需手动注册
- **用户信息同步**：支持从 IDP 同步用户信息到本地
- **双重登录界面**：企业用户 IDP 登录 + 管理员专用登录
- **审计日志**：完整的 IDP 认证和操作审计记录
- **优先级管理**：支持多个 IDP 提供商的优先级配置
- **安全会话管理**：IDP 会话的安全存储和管理

### 🔧 配置灵活性

- **租户级配置**：每个租户可独立配置 IDP 提供商
- **认证策略配置**：可配置 IDP 优先或混合认证模式
- **管理员权限控制**：精确控制管理员本地认证权限
- **属性映射**：灵活的用户属性映射配置
- **权限继承**：IDP 用户可继承本地角色和权限

## 🏗️ 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "前端应用"
        A[登录页面] --> B[IDP 选择]
        B --> C[回调处理]
    end
    
    subgraph "API 网关"
        D[混合认证中间件]
        E[JWT 中间件]
        F[IDP 中间件]
        D --> E
        D --> F
    end
    
    subgraph "用户服务"
        G[认证处理器]
        H[IDP 处理器]
        I[用户管理]
        J[IDP 服务]
    end
    
    subgraph "数据存储"
        K[(用户数据库)]
        L[(IDP 配置)]
        M[(会话存储)]
        N[(审计日志)]
    end
    
    subgraph "外部 IDP"
        O[Azure AD]
        P[Google]
        Q[GitHub]
        R[企业 SAML]
    end
    
    A --> D
    C --> H
    G --> I
    H --> J
    J --> L
    J --> M
    J --> N
    I --> K
    H --> O
    H --> P
    H --> Q
    H --> R
```

### 认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant S as 用户服务
    participant I as IDP提供商
    
    U->>F: 选择IDP登录
    F->>S: 发起IDP认证
    S->>I: 重定向到IDP
    I->>U: IDP登录页面
    U->>I: 输入凭据
    I->>S: 回调授权码
    S->>I: 交换访问令牌
    I->>S: 返回用户信息
    S->>S: 创建/关联本地用户
    S->>F: 返回JWT令牌
    F->>G: 使用JWT访问API
    G->>G: 验证JWT令牌
    G->>S: 转发请求
```

## 🚀 快速开始

### 1. 数据库迁移

首先执行数据库迁移脚本：

```bash
# 备份现有数据库
mysqldump -u username -p paas_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 执行迁移脚本
mysql -u username -p paas_db < scripts/migrate-idp-tables.sql
```

### 2. 配置文件更新

更新用户服务配置文件 `configs/user-service.yaml`：

```yaml
security:
  # 启用 IDP 集成
  idp:
    enabled: true
    allow_local_auth: true
    auto_create_user: true
    auto_link_user: false
    
    # 认证配置
    auth:
      token_header: "Authorization"
      token_prefix: "Bearer "
      cache_ttl: 300
      skip_paths:
        - "/health"
        - "/ready"
        - "/api/v1/auth/login"
        - "/api/v1/idp/login/*"
        - "/api/v1/idp/callback/*"
```

### 3. IDP 提供商配置

复制并配置 IDP 提供商：

```bash
cp configs/idp-providers.example.yaml configs/idp-providers.yaml
```

编辑 `configs/idp-providers.yaml`，配置您的 IDP 提供商：

```yaml
idp_providers:
  - name: "azure-ad"
    type: "oidc"
    enabled: true
    tenant_id: "default"
    config:
      client_id: "your-azure-client-id"
      client_secret: "your-azure-client-secret"
      issuer: "https://login.microsoftonline.com/{tenant-id}/v2.0"
      # ... 其他配置
```

### 4. 启动服务

```bash
# 启动用户服务
./bin/user-service --config=configs/user-service.yaml

# 启动 API 网关
./bin/api-gateway --config=configs/api-gateway.yaml
```

## 🔧 详细配置

### IDP 提供商类型

#### 1. OpenID Connect (OIDC)

```yaml
- name: "azure-ad"
  type: "oidc"
  config:
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    issuer: "https://login.microsoftonline.com/{tenant}/v2.0"
    scopes: ["openid", "profile", "email"]
```

#### 2. OAuth 2.0

```yaml
- name: "github"
  type: "oauth2"
  config:
    client_id: "your-github-client-id"
    client_secret: "your-github-client-secret"
    auth_url: "https://github.com/login/oauth/authorize"
    token_url: "https://github.com/login/oauth/access_token"
    user_info_url: "https://api.github.com/user"
```

#### 3. SAML 2.0

```yaml
- name: "enterprise-saml"
  type: "saml"
  config:
    entity_id: "https://your-domain.com/saml/metadata"
    sso_url: "https://your-saml-idp.com/sso"
    certificate: |
      -----BEGIN CERTIFICATE-----
      YOUR_CERTIFICATE_HERE
      -----END CERTIFICATE-----
```

### 属性映射配置

```yaml
mapping:
  user_id: "sub"           # IDP 用户唯一标识
  username: "preferred_username"
  email: "email"
  first_name: "given_name"
  last_name: "family_name"
  avatar: "picture"
  phone: "phone_number"
  roles: "roles"           # 角色信息
  groups: "groups"         # 组信息
```

## 🔌 API 接口

### 认证相关 API

#### 发起 IDP 登录

```http
POST /api/v1/idp/login/{provider}
Content-Type: application/json

{
  "provider_name": "azure-ad",
  "tenant_id": "default",
  "redirect_url": "https://your-app.com/dashboard"
}
```

#### 处理 IDP 回调

```http
POST /api/v1/idp/callback/{provider}
Content-Type: application/json

{
  "provider_name": "azure-ad",
  "code": "authorization_code",
  "state": "state_parameter",
  "tenant_id": "default"
}
```

### 账号管理 API

#### 关联 IDP 账号

```http
POST /api/v1/idp/link
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "user_id": "local-user-id",
  "idp_provider_id": "provider-id",
  "idp_user_id": "idp-user-id"
}
```

#### 获取用户 IDP 账号

```http
GET /api/v1/idp/accounts
Authorization: Bearer {jwt_token}
```

### 管理员 API

#### 创建 IDP 提供商

```http
POST /api/v1/admin/idp/providers
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "custom-oidc",
  "type": "oidc",
  "enabled": true,
  "tenant_id": "default",
  "config": { ... },
  "mapping": { ... }
}
```

## 🧪 测试

### 运行单元测试

```bash
# 运行 IDP 服务测试
go test ./internal/auth -v -run TestIDP

# 运行所有认证相关测试
go test ./internal/auth -v
```

### 运行集成测试

```bash
# 运行 IDP 集成测试
go test ./tests/integration -v -run TestIDP

# 运行完整集成测试
go test ./tests/integration -v
```

### 前端测试

```bash
cd web
npm test -- --grep "IDP"
```

## 🔒 安全考虑

### 1. 令牌安全

- **加密存储**：IDP 访问令牌和刷新令牌使用 AES 加密存储
- **令牌轮换**：定期刷新访问令牌，避免长期有效令牌
- **安全传输**：所有 IDP 通信必须使用 HTTPS

### 2. 会话管理

- **会话超时**：配置合理的会话超时时间
- **并发限制**：限制单用户的并发会话数量
- **会话撤销**：支持管理员强制撤销用户会话

### 3. 审计日志

- **完整记录**：记录所有 IDP 认证和操作事件
- **敏感信息保护**：避免在日志中记录敏感信息
- **日志轮转**：定期归档和清理审计日志

## 🚨 故障排除

### 常见问题

#### 1. IDP 认证失败

**症状**：用户无法通过 IDP 登录

**排查步骤**：
1. 检查 IDP 提供商配置是否正确
2. 验证客户端 ID 和密钥
3. 确认回调 URL 配置
4. 查看审计日志中的错误信息

#### 2. 用户信息同步失败

**症状**：IDP 用户信息未正确同步到本地

**排查步骤**：
1. 检查属性映射配置
2. 验证 IDP 返回的用户信息格式
3. 确认同步权限设置
4. 查看同步日志

#### 3. 令牌验证失败

**症状**：API 请求返回 401 未授权

**排查步骤**：
1. 检查令牌是否过期
2. 验证令牌格式和签名
3. 确认中间件配置
4. 检查缓存设置

### 日志分析

启用详细日志记录：

```yaml
log:
  level: "debug"
  format: "json"
```

关键日志事件：
- `idp_auth_init`: IDP 认证初始化
- `idp_auth_callback`: IDP 认证回调
- `idp_user_sync`: 用户信息同步
- `idp_account_link`: 账号关联操作

## 📈 监控和运维

### 关键指标

- **认证成功率**：IDP 认证成功的比例
- **响应时间**：IDP 认证的平均响应时间
- **用户活跃度**：通过 IDP 登录的用户数量
- **错误率**：IDP 相关错误的发生频率

### 告警配置

建议配置以下告警：
- IDP 认证失败率超过 5%
- IDP 响应时间超过 10 秒
- IDP 服务不可用
- 异常的登录尝试

## 🔄 升级和维护

### 版本兼容性

- **向后兼容**：新版本保持与现有配置的兼容性
- **平滑升级**：支持零停机时间的滚动升级
- **配置迁移**：提供配置文件的自动迁移工具

### 定期维护

- **证书更新**：定期更新 SAML 证书和 OIDC 密钥
- **配置审查**：定期审查 IDP 提供商配置
- **性能优化**：监控和优化认证性能
- **安全更新**：及时应用安全补丁

## 📚 参考资料

- [OpenID Connect 规范](https://openid.net/connect/)
- [OAuth 2.0 规范](https://tools.ietf.org/html/rfc6749)
- [SAML 2.0 规范](https://docs.oasis-open.org/security/saml/v2.0/)
- [Azure AD 集成指南](https://docs.microsoft.com/en-us/azure/active-directory/)
- [Google OAuth 2.0 指南](https://developers.google.com/identity/protocols/oauth2)

## 🚀 生产环境部署

### 环境变量配置

```bash
# IDP 功能开关
export PAAS_IDP_ENABLED=true
export PAAS_IDP_ALLOW_LOCAL_AUTH=true

# Azure AD 配置
export AZURE_CLIENT_ID="your-azure-client-id"
export AZURE_CLIENT_SECRET="your-azure-client-secret"
export AZURE_TENANT_ID="your-azure-tenant-id"

# Google OAuth 配置
export GOOGLE_CLIENT_ID="your-google-client-id"
export GOOGLE_CLIENT_SECRET="your-google-client-secret"

# 数据库配置
export DB_HOST="your-db-host"
export DB_USER="your-db-user"
export DB_PASSWORD="your-db-password"
export DB_NAME="paas_db"

# Redis 配置（用于会话缓存）
export REDIS_HOST="your-redis-host"
export REDIS_PASSWORD="your-redis-password"
```

### Docker 部署

```dockerfile
# Dockerfile.idp-enabled
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o user-service cmd/user-service/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/user-service .
COPY --from=builder /app/configs ./configs
CMD ["./user-service", "--config=configs/user-service.yaml"]
```

### Kubernetes 部署

```yaml
# k8s/idp-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: idp-config
data:
  user-service.yaml: |
    security:
      idp:
        enabled: true
        allow_local_auth: true
        # ... 其他配置

---
apiVersion: v1
kind: Secret
metadata:
  name: idp-secrets
type: Opaque
data:
  azure-client-secret: <base64-encoded-secret>
  google-client-secret: <base64-encoded-secret>

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service-idp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service-idp
  template:
    metadata:
      labels:
        app: user-service-idp
    spec:
      containers:
      - name: user-service
        image: paas/user-service:idp-v1.0.0
        ports:
        - containerPort: 8083
        env:
        - name: PAAS_IDP_ENABLED
          value: "true"
        volumeMounts:
        - name: config
          mountPath: /root/configs
        - name: secrets
          mountPath: /root/secrets
      volumes:
      - name: config
        configMap:
          name: idp-config
      - name: secrets
        secret:
          secretName: idp-secrets
```

---

**注意**：本文档会随着功能的更新而持续维护。如有问题或建议，请联系开发团队。
