# 用户认证层规范文档

## 1. 认证架构设计

### 1.1 设计原则

- **多租户支持**: 完整的租户隔离和管理
- **RBAC 权限模型**: 基于角色的访问控制
- **JWT 令牌认证**: 无状态的令牌认证机制
- **密码安全**: 强密码策略和安全存储
- **会话管理**: 完整的会话生命周期管理
- **审计日志**: 完整的认证和授权审计

### 1.2 架构图

```
┌─────────────────────────────────────────────────────────┐
│                    前端应用                              │
├─────────────────────────────────────────────────────────┤
│                  API Gateway                            │
│              (认证中间件)                                │
├─────────────────────────────────────────────────────────┤
│                  用户服务                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  用户管理   │ │  角色管理   │ │     权限管理        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                  认证服务                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  JWT 管理   │ │  会话管理   │ │     密码管理        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                  数据存储层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   用户表    │ │   角色表    │ │     权限表          │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 2. 多租户模型

### 2.1 租户隔离策略

#### 数据隔离
- 每个租户的数据完全隔离
- 通过 tenant_id 字段实现行级安全
- 支持租户级别的数据备份和恢复

#### 用户隔离
- 用户属于特定租户
- 用户名在租户内唯一
- 跨租户用户访问需要特殊权限

#### 资源隔离
- 应用、流水线等资源按租户隔离
- 资源配额按租户管理
- 租户级别的监控和统计

### 2.2 租户管理

```yaml
# 租户配置示例
tenant:
  id: "tenant-123"
  name: "示例企业"
  description: "示例企业的 PaaS 租户"
  status: "active"                    # active, suspended, deleted
  
  # 资源配额
  quotas:
    max_applications: 100             # 最大应用数
    max_users: 50                     # 最大用户数
    max_storage: "100Gi"              # 最大存储空间
    max_cpu: "50"                     # 最大 CPU 核数
    max_memory: "100Gi"               # 最大内存
    
  # 功能开关
  features:
    ci_cd_enabled: true               # 是否启用 CI/CD
    monitoring_enabled: true          # 是否启用监控
    backup_enabled: true              # 是否启用备份
    
  # 安全设置
  security:
    password_policy:
      min_length: 8                   # 最小密码长度
      require_uppercase: true         # 需要大写字母
      require_lowercase: true         # 需要小写字母
      require_numbers: true           # 需要数字
      require_symbols: true           # 需要特殊字符
      max_age_days: 90                # 密码最大有效期
      
    session_policy:
      max_idle_time: "30m"            # 最大空闲时间
      max_session_time: "8h"          # 最大会话时间
      concurrent_sessions: 3          # 最大并发会话数
      
    ip_whitelist:                     # IP 白名单
      - "***********/24"
      - "10.0.0.0/8"
```

## 3. RBAC 权限模型

### 3.1 权限模型设计

```
用户 (User) ←→ 用户角色 (UserRole) ←→ 角色 (Role) ←→ 角色权限 (RolePermission) ←→ 权限 (Permission)
```

### 3.2 权限定义

#### 资源类型
- `app`: 应用管理
- `pipeline`: 流水线管理
- `build`: 构建管理
- `deployment`: 部署管理
- `user`: 用户管理
- `role`: 角色管理
- `config`: 配置管理
- `monitor`: 监控管理
- `system`: 系统管理

#### 操作类型
- `create`: 创建
- `read`: 读取
- `update`: 更新
- `delete`: 删除
- `execute`: 执行
- `manage`: 管理

#### 权限格式
```
<resource>:<action>[:<scope>]
```

#### 权限示例
```json
{
  "permissions": [
    "app:create",                     // 创建应用
    "app:read",                       // 读取应用
    "app:update:own",                 // 更新自己的应用
    "app:delete:own",                 // 删除自己的应用
    "pipeline:execute",               // 执行流水线
    "build:read",                     // 读取构建信息
    "deployment:create",              // 创建部署
    "user:read:tenant",               // 读取租户内用户
    "monitor:read",                   // 读取监控信息
    "system:manage"                   // 系统管理
  ]
}
```

### 3.3 预定义角色

#### 超级管理员 (Super Admin)
```json
{
  "name": "super_admin",
  "description": "超级管理员，拥有所有权限",
  "permissions": ["*:*"],
  "scope": "global"
}
```

#### 租户管理员 (Tenant Admin)
```json
{
  "name": "tenant_admin",
  "description": "租户管理员，管理租户内所有资源",
  "permissions": [
    "app:*", "pipeline:*", "build:*", "deployment:*",
    "user:*:tenant", "role:*:tenant", "config:*:tenant",
    "monitor:read", "system:read"
  ],
  "scope": "tenant"
}
```

#### 开发者 (Developer)
```json
{
  "name": "developer",
  "description": "开发者，可以管理自己的应用和流水线",
  "permissions": [
    "app:create", "app:read", "app:update:own", "app:delete:own",
    "pipeline:create", "pipeline:read", "pipeline:update:own",
    "build:read", "build:create", "build:execute:own",
    "deployment:create:own", "deployment:read",
    "monitor:read:own", "config:read"
  ],
  "scope": "user"
}
```

#### 运维人员 (DevOps)
```json
{
  "name": "devops",
  "description": "运维人员，负责部署和监控",
  "permissions": [
    "app:read", "pipeline:read", "build:read",
    "deployment:*", "monitor:*", "config:read",
    "system:read"
  ],
  "scope": "tenant"
}
```

#### 查看者 (Viewer)
```json
{
  "name": "viewer",
  "description": "查看者，只能查看信息",
  "permissions": [
    "app:read", "pipeline:read", "build:read",
    "deployment:read", "monitor:read"
  ],
  "scope": "tenant"
}
```

## 4. JWT 令牌认证

### 4.1 JWT 结构

#### Header
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

#### Payload
```json
{
  "sub": "user-123",                  // 用户ID
  "tenant_id": "tenant-456",          // 租户ID
  "username": "zhangsan",             // 用户名
  "email": "<EMAIL>",    // 邮箱
  "roles": ["developer", "viewer"],   // 角色列表
  "permissions": [                    // 权限列表
    "app:create",
    "app:read",
    "pipeline:execute"
  ],
  "iat": 1640995200,                  // 签发时间
  "exp": 1641081600,                  // 过期时间
  "jti": "jwt-789"                    // JWT ID
}
```

### 4.2 令牌类型

#### 访问令牌 (Access Token)
- 用于 API 访问认证
- 短期有效 (默认 1 小时)
- 包含用户身份和权限信息

#### 刷新令牌 (Refresh Token)
- 用于刷新访问令牌
- 长期有效 (默认 7 天)
- 存储在安全的 HttpOnly Cookie 中

### 4.3 令牌管理

#### 令牌生成
```go
type TokenService interface {
    GenerateTokenPair(user *User) (*TokenPair, error)
    RefreshToken(refreshToken string) (*TokenPair, error)
    ValidateToken(token string) (*Claims, error)
    RevokeToken(tokenID string) error
}

type TokenPair struct {
    AccessToken  string    `json:"access_token"`
    RefreshToken string    `json:"refresh_token"`
    TokenType    string    `json:"token_type"`
    ExpiresIn    int       `json:"expires_in"`
}
```

#### 令牌撤销
- 支持单个令牌撤销
- 支持用户所有令牌撤销
- 支持租户所有令牌撤销
- 黑名单机制防止已撤销令牌使用

## 5. 密码安全

### 5.1 密码策略

#### 强密码要求
- 最小长度 8 位
- 必须包含大写字母
- 必须包含小写字母
- 必须包含数字
- 必须包含特殊字符
- 不能包含用户名
- 不能是常见弱密码

#### 密码历史
- 记录最近 12 次密码
- 新密码不能与历史密码相同
- 密码有效期 90 天

### 5.2 密码存储

#### 哈希算法
- 使用 bcrypt 算法
- 成本因子 12 (可配置)
- 自动加盐

```go
// 密码哈希
func HashPassword(password string) (string, error) {
    cost := 12
    hash, err := bcrypt.GenerateFromPassword([]byte(password), cost)
    if err != nil {
        return "", err
    }
    return string(hash), nil
}

// 密码验证
func VerifyPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### 5.3 密码重置

#### 重置流程
1. 用户请求密码重置
2. 系统生成重置令牌
3. 发送重置邮件
4. 用户点击链接重置密码
5. 令牌验证和密码更新

#### 安全措施
- 重置令牌有效期 30 分钟
- 令牌只能使用一次
- 重置后撤销所有会话
- 记录重置操作日志

## 6. 会话管理

### 6.1 会话存储

#### Redis 会话存储
```go
type SessionStore interface {
    Create(session *Session) error
    Get(sessionID string) (*Session, error)
    Update(session *Session) error
    Delete(sessionID string) error
    DeleteByUser(userID string) error
    Cleanup() error
}

type Session struct {
    ID           string            `json:"id"`
    UserID       string            `json:"user_id"`
    TenantID     string            `json:"tenant_id"`
    IPAddress    string            `json:"ip_address"`
    UserAgent    string            `json:"user_agent"`
    CreatedAt    time.Time         `json:"created_at"`
    LastActivity time.Time         `json:"last_activity"`
    ExpiresAt    time.Time         `json:"expires_at"`
    Data         map[string]interface{} `json:"data"`
}
```

### 6.2 会话策略

#### 会话超时
- 绝对超时: 8 小时
- 空闲超时: 30 分钟
- 可配置的超时策略

#### 并发会话
- 限制用户最大并发会话数
- 新会话可以踢出旧会话
- 会话冲突检测

### 6.3 会话安全

#### 会话固定攻击防护
- 登录后重新生成会话ID
- 权限变更后重新生成会话ID

#### 会话劫持防护
- IP 地址验证
- User-Agent 验证
- 会话令牌定期轮换

## 7. 审计日志

### 7.1 审计事件

#### 认证事件
- 用户登录成功/失败
- 用户登出
- 密码修改
- 密码重置
- 令牌刷新
- 会话超时

#### 授权事件
- 权限检查成功/失败
- 角色变更
- 权限变更
- 资源访问

#### 管理事件
- 用户创建/修改/删除
- 角色创建/修改/删除
- 租户创建/修改/删除

### 7.2 审计日志格式

```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "event_type": "authentication",
  "event_name": "user_login",
  "result": "success",
  "user_id": "user-123",
  "tenant_id": "tenant-456",
  "username": "zhangsan",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "resource": "auth/login",
  "details": {
    "login_method": "password",
    "session_id": "session-789"
  },
  "risk_score": 0
}
```

### 7.3 审计分析

#### 异常检测
- 异常登录时间
- 异常登录地点
- 多次失败登录
- 权限滥用检测

#### 合规报告
- 用户访问报告
- 权限变更报告
- 安全事件报告
- 合规性检查报告

## 8. 单点登录 (SSO)

### 8.1 SAML 2.0 支持

#### SAML 配置
```yaml
saml:
  enabled: true
  entity_id: "https://paas.example.com"
  sso_url: "https://idp.example.com/sso"
  slo_url: "https://idp.example.com/slo"
  certificate: "/path/to/cert.pem"
  private_key: "/path/to/key.pem"
  
  # 属性映射
  attribute_mapping:
    user_id: "NameID"
    username: "username"
    email: "email"
    first_name: "firstName"
    last_name: "lastName"
    roles: "roles"
```

### 8.2 OAuth 2.0 / OpenID Connect

#### OAuth 配置
```yaml
oauth:
  providers:
    - name: "google"
      client_id: "google-client-id"
      client_secret: "google-client-secret"
      auth_url: "https://accounts.google.com/o/oauth2/auth"
      token_url: "https://oauth2.googleapis.com/token"
      user_info_url: "https://www.googleapis.com/oauth2/v2/userinfo"
      scopes: ["openid", "email", "profile"]
      
    - name: "github"
      client_id: "github-client-id"
      client_secret: "github-client-secret"
      auth_url: "https://github.com/login/oauth/authorize"
      token_url: "https://github.com/login/oauth/access_token"
      user_info_url: "https://api.github.com/user"
      scopes: ["user:email"]
```

## 9. API 安全

### 9.1 认证中间件

```go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取 Authorization 头
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(401, gin.H{"error": "缺少认证令牌"})
            c.Abort()
            return
        }
        
        // 验证 Bearer 令牌
        token := strings.TrimPrefix(authHeader, "Bearer ")
        claims, err := ValidateJWT(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "无效的认证令牌"})
            c.Abort()
            return
        }
        
        // 设置用户上下文
        c.Set("user_id", claims.UserID)
        c.Set("tenant_id", claims.TenantID)
        c.Set("username", claims.Username)
        c.Set("roles", claims.Roles)
        c.Set("permissions", claims.Permissions)
        
        c.Next()
    }
}
```

### 9.2 权限检查中间件

```go
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        permissions, exists := c.Get("permissions")
        if !exists {
            c.JSON(403, gin.H{"error": "权限信息缺失"})
            c.Abort()
            return
        }
        
        if !HasPermission(permissions.([]string), permission) {
            c.JSON(403, gin.H{"error": "权限不足"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// 使用示例
router.POST("/apps", AuthMiddleware(), RequirePermission("app:create"), CreateApp)
router.GET("/apps", AuthMiddleware(), RequirePermission("app:read"), ListApps)
```

### 9.3 API 限流

```go
func RateLimitMiddleware(limit int, window time.Duration) gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Every(window/time.Duration(limit)), limit)
    
    return func(c *gin.Context) {
        if !limiter.Allow() {
            c.JSON(429, gin.H{"error": "请求过于频繁"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

## 10. 安全最佳实践

### 10.1 传输安全
- 强制使用 HTTPS
- TLS 1.2+ 版本
- 安全的密码套件
- HSTS 头部设置

### 10.2 输入验证
- 严格的输入验证
- SQL 注入防护
- XSS 攻击防护
- CSRF 令牌验证

### 10.3 错误处理
- 不泄露敏感信息
- 统一的错误响应格式
- 详细的内部日志记录
- 用户友好的错误消息

### 10.4 安全头部
```go
func SecurityHeadersMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        c.Header("Content-Security-Policy", "default-src 'self'")
        c.Next()
    }
}
```
