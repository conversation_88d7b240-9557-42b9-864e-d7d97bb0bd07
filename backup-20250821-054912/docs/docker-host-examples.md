# Docker Host 连接配置示例

## 快速开始

### 1. SSH 连接（推荐）

最简单和安全的方式：

```bash
# 切换到 SSH 连接
./scripts/switch-docker-host.sh ssh root@*************

# 测试连接（不保存配置）
./scripts/switch-docker-host.sh -t ssh root@*************
```

**环境变量配置：**
```bash
DOCKER_HOST=ssh://root@*************
```

### 2. TLS 连接（生产环境）

安全的 TCP 连接：

```bash
# 首先准备证书文件到 ./docker-certs/ 目录
# 然后切换到 TLS 连接
./scripts/switch-docker-host.sh tls ************* 2376

# 使用自定义证书路径
./scripts/switch-docker-host.sh -c /path/to/certs tls *************
```

**环境变量配置：**
```bash
DOCKER_HOST=tcp://*************:2376
DOCKER_TLS_VERIFY=1
DOCKER_CERT_PATH=./docker-certs
```

### 3. TCP 连接（仅开发环境）

⚠️ 不安全，仅用于隔离的开发环境：

```bash
# 切换到 TCP 连接
./scripts/switch-docker-host.sh tcp ************* 2375
```

**环境变量配置：**
```bash
DOCKER_HOST=tcp://*************:2375
DOCKER_TLS_VERIFY=0
```

### 4. 切换回本地 Docker

```bash
# 切换回本地 Docker
./scripts/switch-docker-host.sh local
```

**环境变量配置：**
```bash
DOCKER_HOST=unix:///var/run/docker.sock
```

## 常见场景配置

### 场景 1：开发环境连接测试服务器

```bash
# 使用 SSH 连接到开发服务器
./scripts/switch-docker-host.<NAME_EMAIL>

# 验证连接
docker ps
docker images
```

### 场景 2：生产环境部署

```bash
# 准备 TLS 证书
mkdir -p ./docker-certs
scp prod-server:/etc/docker/certs/{ca.pem,cert.pem,key.pem} ./docker-certs/

# 连接到生产服务器
./scripts/switch-docker-host.sh tls prod-server.company.com 2376

# 部署应用
docker-compose up -d
```

### 场景 3：多环境切换

```bash
# 创建环境切换别名
alias docker-local='./scripts/switch-docker-host.sh local'
alias docker-dev='./scripts/switch-docker-host.sh ssh developer@dev-server'
alias docker-prod='./scripts/switch-docker-host.sh tls prod-server 2376'

# 快速切换
docker-dev    # 切换到开发环境
docker-prod   # 切换到生产环境
docker-local  # 切换回本地
```

## 远程主机配置示例

### SSH 连接配置

**远程主机配置：**
```bash
# 确保 Docker 服务运行
sudo systemctl enable docker
sudo systemctl start docker

# 将用户添加到 docker 组（避免使用 root）
sudo usermod -aG docker $USER

# 配置 SSH 密钥认证
# 在本地生成密钥对
ssh-keygen -t rsa -b 4096 -f ~/.ssh/docker_host_key

# 复制公钥到远程主机
ssh-copy-id -i ~/.ssh/docker_host_key.pub user@remote-host
```

**本地配置：**
```bash
# 使用指定的密钥文件
./scripts/switch-docker-host.sh -k ~/.ssh/docker_host_key ssh user@remote-host
```

### TLS 连接配置

**远程主机配置：**

1. 生成证书（参考完整指南）
2. 配置 Docker 守护进程：

```json
# /etc/docker/daemon.json
{
  "hosts": [
    "unix:///var/run/docker.sock",
    "tcp://0.0.0.0:2376"
  ],
  "tls": true,
  "tlscert": "/etc/docker/certs/server-cert.pem",
  "tlskey": "/etc/docker/certs/server-key.pem",
  "tlsverify": true,
  "tlscacert": "/etc/docker/certs/ca.pem"
}
```

3. 重启 Docker：
```bash
sudo systemctl restart docker
```

**本地配置：**
```bash
# 复制证书文件
mkdir -p ./docker-certs
scp remote-host:/etc/docker/certs/{ca.pem,cert.pem,key.pem} ./docker-certs/

# 连接
./scripts/switch-docker-host.sh tls remote-host 2376
```

## 故障排除

### 连接测试命令

```bash
# 测试 SSH 连接
ssh -o ConnectTimeout=10 user@remote-host "docker version"

# 测试 TCP 连接
telnet remote-host 2375

# 测试 TLS 连接
openssl s_client -connect remote-host:2376 -cert ./docker-certs/cert.pem -key ./docker-certs/key.pem -CAfile ./docker-certs/ca.pem
```

### 常见错误解决

1. **Permission denied (publickey)**
   ```bash
   # 检查 SSH 密钥权限
   chmod 600 ~/.ssh/id_rsa
   
   # 测试 SSH 连接
   ssh -v user@remote-host
   ```

2. **Connection refused**
   ```bash
   # 检查远程主机 Docker 服务
   ssh user@remote-host "sudo systemctl status docker"
   
   # 检查端口是否开放
   nmap -p 2376 remote-host
   ```

3. **TLS certificate error**
   ```bash
   # 验证证书
   openssl x509 -in ./docker-certs/cert.pem -text -noout
   
   # 检查证书有效期
   openssl x509 -in ./docker-certs/cert.pem -noout -dates
   ```

## 环境变量文件示例

### 开发环境 (.env.development)

```bash
# Docker 配置 - SSH 连接
DOCKER_HOST=ssh://developer@dev-server
DOCKER_TLS_VERIFY=0
DOCKER_TIMEOUT=30s

# 或者 TCP 连接（不安全）
# DOCKER_HOST=tcp://*************:2375
# DOCKER_TLS_VERIFY=0
```

### 生产环境 (.env.production)

```bash
# Docker 配置 - TLS 连接
DOCKER_HOST=tcp://prod-docker-host:2376
DOCKER_TLS_VERIFY=1
DOCKER_CERT_PATH=/etc/docker/certs
DOCKER_TIMEOUT=60s
```

## 自动化脚本

### 环境检测脚本

```bash
#!/bin/bash
# check-docker-env.sh

echo "当前 Docker 环境信息："
echo "DOCKER_HOST: ${DOCKER_HOST:-未设置}"
echo "DOCKER_TLS_VERIFY: ${DOCKER_TLS_VERIFY:-未设置}"
echo "DOCKER_CERT_PATH: ${DOCKER_CERT_PATH:-未设置}"

echo -e "\nDocker 版本信息："
docker version --format "客户端: {{.Client.Version}}"
docker version --format "服务端: {{.Server.Version}}"

echo -e "\nDocker 系统信息："
docker system info --format "容器数量: {{.Containers}}"
docker system info --format "镜像数量: {{.Images}}"
```

### 批量环境切换

```bash
#!/bin/bash
# batch-switch.sh

environments=(
    "local:local"
    "dev:ssh://developer@dev-server"
    "staging:ssh://deployer@staging-server"
    "prod:tcp://prod-server:2376"
)

echo "可用环境："
for i in "${!environments[@]}"; do
    env_name=$(echo "${environments[$i]}" | cut -d':' -f1)
    echo "$((i+1)). $env_name"
done

read -p "选择环境 (1-${#environments[@]}): " choice
selected="${environments[$((choice-1))]}"
env_name=$(echo "$selected" | cut -d':' -f1)
docker_host=$(echo "$selected" | cut -d':' -f2-)

echo "切换到 $env_name 环境..."
if [[ "$env_name" == "local" ]]; then
    ./scripts/switch-docker-host.sh local
elif [[ "$docker_host" == ssh://* ]]; then
    target=$(echo "$docker_host" | sed 's|ssh://||')
    ./scripts/switch-docker-host.sh ssh "$target"
elif [[ "$docker_host" == tcp://* ]]; then
    host_port=$(echo "$docker_host" | sed 's|tcp://||')
    ./scripts/switch-docker-host.sh tls "${host_port%:*}" "${host_port#*:}"
fi
```

## 最佳实践

1. **使用 SSH 连接**：最安全和简单的方式
2. **环境隔离**：不同环境使用不同的配置文件
3. **证书管理**：定期更新 TLS 证书
4. **权限控制**：使用最小权限原则
5. **监控日志**：监控 Docker API 访问日志
6. **备份配置**：定期备份证书和配置文件
