# 服务自动注册机制

## 📋 概述

本文档介绍了 PaaS 平台中新实现的服务自动注册机制。该系统实现了应用实例部署后自动注册到 API 网关的完整流程，包括服务发现、健康检查、负载均衡等功能。

## 🏗️ 系统架构

```
应用部署 → 自动注册 → API 网关 → 负载均衡器 → 服务实例
    ↓           ↓          ↓           ↓
[应用管理器] [注册客户端] [服务注册中心] [健康检查器]
```

### 核心组件

1. **RegistryClient** (`pkg/registry/client.go`)
   - 服务注册客户端
   - 支持注册、注销、健康状态更新
   - 自动重试和错误处理

2. **AppRegistryManager** (`pkg/registry/app_registry.go`)
   - 应用注册管理器
   - 管理应用实例生命周期
   - 自动健康检查和故障转移

3. **LoadBalancer Service** (`cmd/loadbalancer-service/main.go`)
   - 独立的负载均衡服务
   - 提供服务注册中心功能
   - 支持多种负载均衡算法

## ✅ 功能特性

### 1. 自动服务注册
- ✅ **应用部署后自动注册**：实例启动后自动注册到 API 网关
- ✅ **服务元数据管理**：包含应用信息、版本、环境等
- ✅ **动态服务发现**：支持服务实例的动态上下线
- ✅ **租户隔离**：基于租户的服务命名和隔离

### 2. 健康检查机制
- ✅ **自动健康检查**：定期检查服务实例健康状态
- ✅ **故障检测**：自动检测不健康的实例
- ✅ **自动恢复**：实例恢复后自动重新加入负载均衡
- ✅ **故障转移**：不健康实例自动从负载均衡中移除

### 3. 负载均衡支持
- ✅ **多种算法**：轮询、加权轮询、最少连接、IP哈希、随机
- ✅ **权重配置**：支持实例权重配置
- ✅ **会话保持**：支持基于IP的会话保持
- ✅ **动态配置**：支持运行时修改负载均衡配置

### 4. 服务生命周期管理
- ✅ **自动注册**：应用部署完成后自动注册
- ✅ **自动注销**：应用停止时自动注销
- ✅ **状态同步**：实时同步服务实例状态
- ✅ **批量管理**：支持批量注册和注销操作

## 🔧 配置说明

### 1. 应用服务配置

应用服务会自动集成注册管理器：

```go
// 在 internal/app/service.go 中
func NewAppService(db *gorm.DB, logger Logger) Service {
    // 创建注册管理器
    registryConfig := registry.DefaultAppRegistryConfig()
    registryConfig.RegistryURL = "http://localhost:8080" // API 网关地址
    
    registryManager := registry.NewAppRegistryManager(registryConfig, loggerAdapter)
    
    return &AppService{
        db:              db,
        logger:          logger,
        registryManager: registryManager,
    }
}
```

### 2. 负载均衡器配置 (`configs/loadbalancer.yaml`)

```yaml
# 服务器配置
server:
  port: 8086
  host: "0.0.0.0"

# 负载均衡配置
load_balancer:
  default_algorithm: "round_robin"
  algorithms:
    round_robin:
      enabled: true
    weighted_round_robin:
      enabled: true
    least_connections:
      enabled: true

# 健康检查配置
health_check:
  global:
    enabled: true
    default_interval: "30s"
    default_timeout: "5s"
    default_path: "/health"
```

### 3. API 网关路由配置

API 网关自动代理负载均衡器请求：

```go
// 在 cmd/api-gateway/main.go 中
loadbalancer := router.Group("/loadbalancer")
{
    loadbalancer.Any("/*path", func(c *gin.Context) {
        targetPath := "/api/v1/loadbalancer" + c.Param("path")
        targetURL := "http://localhost:8086" + targetPath
        proxyToURL(c, targetURL, logger)
    })
}
```

## 🚀 使用示例

### 1. 应用自动注册流程

```go
// 应用部署完成后自动执行
func (s *AppService) deployInstance(ctx context.Context, instance *AppInstance, app *Application) {
    // ... 部署逻辑 ...
    
    // 自动注册到服务注册中心
    if err := s.registerInstanceToGateway(ctx, instance, app, instanceHost, instancePort); err != nil {
        s.logger.Error("自动注册服务失败", "error", err)
    }
}

func (s *AppService) registerInstanceToGateway(ctx context.Context, instance *AppInstance, app *Application, host string, port int) error {
    registration := &registry.AppRegistration{
        AppID:       app.ID,
        AppName:     app.Name,
        InstanceID:  instance.ID,
        ServiceName: s.buildServiceName(app), // tenant-id-app-name
        Host:        host,
        Port:        port,
        Protocol:    "http",
        Version:     instance.Version,
        Environment: s.getEnvironment(app),
        TenantID:    app.TenantID,
        Metadata: map[string]string{
            "language":  app.Language,
            "framework": app.Framework,
        },
    }
    
    return s.registryManager.RegisterApp(ctx, registration)
}
```

### 2. 手动服务注册

```bash
# 注册服务实例
curl -X POST http://localhost:8086/api/v1/loadbalancer/services/register \
  -H "Content-Type: application/json" \
  -d '{
    "service_name": "my-app",
    "instance_id": "my-app-instance-1",
    "host": "127.0.0.1",
    "port": 3000,
    "protocol": "http",
    "weight": 100,
    "metadata": {
      "app_id": "app-123",
      "version": "1.0.0",
      "environment": "production"
    },
    "health_check_url": "http://127.0.0.1:3000/health"
  }'
```

### 3. 查询服务实例

```bash
# 获取所有服务
curl http://localhost:8086/api/v1/loadbalancer/services

# 获取特定服务的实例
curl http://localhost:8086/api/v1/loadbalancer/services/my-app/instances

# 检查服务健康状态
curl http://localhost:8086/api/v1/loadbalancer/health/services/my-app
```

### 4. 注销服务实例

```bash
# 注销服务实例
curl -X DELETE http://localhost:8086/api/v1/loadbalancer/services/my-app/instances/my-app-instance-1

# 更新健康状态
curl -X PUT http://localhost:8086/api/v1/loadbalancer/services/my-app/instances/my-app-instance-1/health \
  -H "Content-Type: application/json" \
  -d '{"status": "unhealthy"}'
```

## 📊 API 端点

### 服务注册和发现
- `POST /api/v1/loadbalancer/services/register` - 注册服务
- `DELETE /api/v1/loadbalancer/services/{service}/instances/{instance}` - 注销服务
- `PUT /api/v1/loadbalancer/services/{service}/instances/{instance}/health` - 更新健康状态
- `GET /api/v1/loadbalancer/services` - 获取所有服务
- `GET /api/v1/loadbalancer/services/{service}/instances` - 获取服务实例

### 健康检查
- `GET /api/v1/loadbalancer/health/services` - 获取健康统计
- `GET /api/v1/loadbalancer/health/services/{service}` - 检查服务健康
- `POST /api/v1/loadbalancer/health/services/{service}/config` - 设置健康检查配置

### 负载均衡
- `GET /api/v1/loadbalancer/stats` - 获取负载均衡统计
- `GET /api/v1/loadbalancer/algorithms` - 获取支持的算法
- `POST /api/v1/loadbalancer/services/{service}/config` - 设置负载均衡配置

### 代理请求
- `ANY /api/v1/loadbalancer/proxy/{service}/*` - 代理请求到服务实例

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/registry -v
```

### 集成测试
```bash
# 启动负载均衡服务
go run cmd/loadbalancer-service/main.go

# 启动 API 网关
go run cmd/api-gateway/main.go

# 启动应用管理服务
go run cmd/app-manager/main.go

# 测试应用部署和自动注册
curl -X POST http://localhost:8080/api/v1/apps \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "test-app",
    "language": "nodejs",
    "version": "1.0.0"
  }'
```

## 🔍 监控和调试

### 服务状态监控
```bash
# 查看所有注册的服务
curl http://localhost:8086/api/v1/loadbalancer/services

# 查看负载均衡统计
curl http://localhost:8086/api/v1/loadbalancer/stats

# 查看健康检查状态
curl http://localhost:8086/api/v1/loadbalancer/health/services
```

### 日志监控
```bash
# 查看应用管理服务日志
tail -f logs/app-manager.log

# 查看负载均衡服务日志
tail -f logs/loadbalancer.log

# 查看 API 网关日志
tail -f logs/api-gateway.log
```

## 🛠️ 故障排除

### 常见问题

1. **服务注册失败**
   - 检查负载均衡服务是否运行 (端口 8086)
   - 检查网络连接和防火墙设置
   - 查看应用管理服务日志

2. **健康检查失败**
   - 确认应用实例提供 `/health` 端点
   - 检查健康检查配置
   - 查看负载均衡服务日志

3. **负载均衡不工作**
   - 检查服务实例是否健康
   - 确认负载均衡算法配置
   - 查看代理请求日志

## 📈 性能优化

1. **连接池配置**：调整 HTTP 客户端连接池大小
2. **健康检查频率**：根据需要调整检查间隔
3. **缓存策略**：启用服务发现结果缓存
4. **批量操作**：使用批量注册/注销减少网络开销

## 🔒 安全考虑

1. **认证授权**：API 端点支持认证和授权
2. **网络隔离**：服务间通信使用内网
3. **访问控制**：基于 IP 白名单的访问控制
4. **数据加密**：敏感数据传输加密

---

**注意**：服务自动注册机制是 PaaS 平台微服务架构的核心组件，确保了应用实例的自动化管理和高可用性。
