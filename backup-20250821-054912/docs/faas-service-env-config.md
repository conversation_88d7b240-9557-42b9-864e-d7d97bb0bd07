# FaaS 服务环境变量配置说明

## 概述

本文档详细说明了 FaaS (Function as a Service) 服务的环境变量配置，包括开发环境和生产环境的配置差异。

## 基础服务配置

### 端口配置
```bash
# FaaS 服务端口
FAAS_SERVICE_PORT=8087
```

### 数据库配置
```bash
# 开发环境 - 使用 SQLite
FAAS_SERVICE_DB_DSN=./data/faas-service.db

# 生产环境 - 使用 PostgreSQL
FAAS_SERVICE_DB_DSN=host=localhost user=paas_user password=paas_password dbname=paas_faas port=5432 sslmode=disable TimeZone=Asia/Shanghai
```

### 日志配置
```bash
# 日志级别 - 开发环境使用 debug，生产环境使用 info
FAAS_SERVICE_LOG_LEVEL=debug

# 日志文件路径
FAAS_SERVICE_LOG_FILE=./logs/faas-service.log
```

### Redis 配置
```bash
# Redis 数据库编号
FAAS_SERVICE_REDIS_DB=4
```

### 性能分析工具
```bash
# pprof 性能分析端口
FAAS_SERVICE_PPROF_PORT=6065
```

## FaaS 执行器配置

### 并发和超时配置
```bash
# 最大并发执行数 - 开发环境较小，生产环境可以更大
FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS=50

# 默认超时时间
FAAS_SERVICE_DEFAULT_TIMEOUT=30s

# 最大执行时间
FAAS_SERVICE_MAX_EXECUTION_TIME=5m
```

### 容器池配置
```bash
# 容器池大小 - 开发环境较小
FAAS_SERVICE_CONTAINER_POOL_SIZE=5

# 预热容器数量
FAAS_SERVICE_PREWARM_CONTAINERS=2

# 清理间隔
FAAS_SERVICE_CLEANUP_INTERVAL=5m
```

## 资源限制配置

### CPU 和内存限制
```bash
# CPU 限制 - 开发环境较小
FAAS_SERVICE_CPU_LIMIT=0.5

# 内存限制
FAAS_SERVICE_MEMORY_LIMIT=512m

# 磁盘限制
FAAS_SERVICE_DISK_LIMIT=1g
```

## 存储配置

### 工作空间配置
```bash
# 工作空间基础路径
FAAS_SERVICE_WORKSPACE_BASE_PATH=./data/faas/workspaces

# 工作空间最大大小 - 开发环境较小
FAAS_SERVICE_WORKSPACE_MAX_SIZE=5Gi

# 工作空间保留天数 - 开发环境较短
FAAS_SERVICE_WORKSPACE_RETENTION_DAYS=3
```

### 产物存储配置
```bash
# 产物存储基础路径
FAAS_SERVICE_ARTIFACTS_BASE_PATH=./data/faas/artifacts

# 产物存储最大大小 - 开发环境较小
FAAS_SERVICE_ARTIFACTS_MAX_SIZE=20Gi

# 产物保留天数 - 开发环境较短
FAAS_SERVICE_ARTIFACTS_RETENTION_DAYS=7
```

## 通知配置

### 通知开关和配置
```bash
# 通知功能开关 - 开发环境通常禁用
FAAS_SERVICE_NOTIFICATION_ENABLED=false

# Webhook URL - 生产环境配置实际的通知地址
FAAS_SERVICE_WEBHOOK_URL=

# 通知超时时间
FAAS_SERVICE_NOTIFICATION_TIMEOUT=30s
```

## 环境差异说明

### 开发环境特点
- 使用 SQLite 数据库，简化部署
- 较小的资源限制和并发数
- 启用详细的调试日志
- 较短的数据保留时间
- 禁用通知功能

### 生产环境特点
- 使用 PostgreSQL 数据库，支持高并发
- 更大的资源限制和并发数
- 使用 info 级别日志
- 更长的数据保留时间
- 启用通知功能

## 配置文件优先级

FaaS 服务的配置加载优先级如下：
1. 命令行参数 `--config` 指定的配置文件
2. 环境变量（前缀：`FAAS_SERVICE_`）
3. 默认配置文件：`configs/faas-service.yaml` 或 `configs/faas-service.dev.yaml`
4. 代码中的默认值

## 使用示例

### 开发环境启动
```bash
# 使用 .env.development 文件
cp .env.development .env
./scripts/start-faas-service.sh --dev
```

### 生产环境启动
```bash
# 设置生产环境变量
export FAAS_SERVICE_PORT=8087
export FAAS_SERVICE_DB_DSN="host=prod-db user=faas_user password=secure_password dbname=paas_faas port=5432 sslmode=require"
./scripts/start-faas-service.sh --config=configs/faas-service.yaml
```

## 注意事项

1. **安全性**：生产环境的数据库密码和敏感配置不应写入配置文件，应通过环境变量或密钥管理系统提供
2. **资源监控**：生产环境应监控 FaaS 服务的资源使用情况，及时调整限制参数
3. **日志管理**：生产环境应配置日志轮转和归档策略
4. **备份策略**：重要的函数代码和执行结果应定期备份
5. **版本兼容性**：升级服务时注意配置参数的兼容性变化

## 相关文档

- [FaaS 服务部署指南](./faas-deployment-guide.md)
- [FaaS 函数开发指南](./faas-function-development.md)
- [FaaS 服务监控指南](./faas-monitoring-guide.md)
