# 请求/响应转换系统

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的请求/响应转换系统。该系统提供了强大的数据转换能力，包括请求参数转换、响应数据过滤、字段脱敏、数据格式化等功能，确保 API 的安全性和一致性。

## 🏗️ 系统架构

```
客户端请求 → 请求转换器 → 后端服务 → 响应转换器 → 客户端响应
     ↓           ↓           ↓           ↓
[转换规则] [参数处理] [业务逻辑] [数据脱敏]
```

### 核心组件

1. **RequestTransformer** (`pkg/transformer/transformer.go`)
   - 请求参数转换
   - 请求头处理
   - 请求体格式化

2. **ResponseTransformer** (`pkg/transformer/response_transformer.go`)
   - 响应数据过滤
   - 敏感数据脱敏
   - 响应格式包装

3. **RuleManager** (`pkg/transformer/rule_manager.go`)
   - 转换规则管理
   - 规则验证和加载
   - 动态规则更新

4. **TransformMiddleware** (`pkg/middleware/transform.go`)
   - Gin 中间件集成
   - 请求/响应拦截
   - 转换流程控制

## ✅ 功能特性

### 1. 请求转换功能
- ✅ **请求头转换**：添加、删除、重命名、替换请求头
- ✅ **查询参数转换**：参数类型转换、默认值设置、参数重命名
- ✅ **路径参数处理**：路径参数提取和转换
- ✅ **请求体转换**：JSON 字段操作、数据类型转换
- ✅ **条件转换**：基于条件的动态转换规则

### 2. 响应转换功能
- ✅ **响应头处理**：响应头添加、删除、修改
- ✅ **数据脱敏**：手机号、邮箱、身份证、信用卡等敏感数据脱敏
- ✅ **字段过滤**：移除敏感字段、内部字段
- ✅ **响应包装**：统一响应格式包装
- ✅ **状态码转换**：HTTP 状态码映射

### 3. 规则管理
- ✅ **规则配置**：JSON 格式的转换规则配置
- ✅ **规则验证**：转换规则的语法和逻辑验证
- ✅ **动态加载**：从文件系统动态加载转换规则
- ✅ **规则统计**：转换规则的使用统计和监控

### 4. 安全特性
- ✅ **敏感数据保护**：多种脱敏策略
- ✅ **字段白名单**：只允许特定字段通过
- ✅ **数据验证**：转换前后的数据完整性验证
- ✅ **审计日志**：转换操作的详细日志记录

## 🔧 配置说明

### 1. 转换中间件配置

```go
// 在 cmd/api-gateway/main.go 中
func setupTransformMiddleware(logger logger.Logger) *middleware.TransformMiddleware {
    // 创建转换器
    requestTransformer := transformer.NewRequestTransformer(logger)
    responseTransformer := transformer.NewResponseTransformer(logger)
    
    // 创建规则管理器
    ruleManager := transformer.NewRuleManager(logger, "configs/transform-rules", false)
    
    // 加载转换规则
    ruleManager.LoadRulesFromDirectory()
    
    // 创建中间件
    transformMiddleware := middleware.NewTransformMiddleware(
        requestTransformer,
        responseTransformer,
        logger,
        middleware.DefaultTransformConfig(),
    )
    
    return transformMiddleware
}
```

### 2. 转换规则配置

转换规则使用 JSON 格式配置，存放在 `configs/transform-rules/` 目录下：

```json
{
  "path": "/api/v1/users",
  "method": "GET",
  "enabled": true,
  "description": "用户API转换规则",
  "request_rules": {
    "headers": [
      {
        "action": "add",
        "name": "X-Request-ID",
        "value": "{{uuid}}"
      }
    ],
    "query_params": [
      {
        "action": "convert",
        "name": "page",
        "data_type": "int"
      }
    ]
  },
  "response_rules": {
    "body": {
      "transform": "json_transform",
      "rules": [
        {
          "action": "mask",
          "path": "data.*.phone",
          "mask_type": "phone"
        }
      ]
    }
  }
}
```

## 🚀 使用示例

### 1. 请求头转换

```json
{
  "request_rules": {
    "headers": [
      {
        "action": "add",
        "name": "X-API-Version",
        "value": "v1"
      },
      {
        "action": "remove",
        "name": "X-Internal-Token"
      },
      {
        "action": "rename",
        "name": "X-User-ID",
        "new_name": "X-Operator-ID"
      },
      {
        "action": "replace",
        "name": "Authorization",
        "pattern": "Bearer (.+)",
        "replacement": "Token $1"
      }
    ]
  }
}
```

### 2. 查询参数转换

```json
{
  "request_rules": {
    "query_params": [
      {
        "action": "add",
        "name": "include_deleted",
        "value": false
      },
      {
        "action": "convert",
        "name": "page",
        "data_type": "int"
      },
      {
        "action": "rename",
        "name": "limit",
        "new_name": "size"
      }
    ]
  }
}
```

### 3. 请求体转换

```json
{
  "request_rules": {
    "body": {
      "content_type": "application/json",
      "transform": "json_transform",
      "rules": [
        {
          "action": "add",
          "path": "created_at",
          "value": "{{timestamp}}"
        },
        {
          "action": "remove",
          "path": "internal_notes"
        },
        {
          "action": "convert",
          "path": "age",
          "data_type": "int"
        }
      ]
    }
  }
}
```

### 4. 响应数据脱敏

```json
{
  "response_rules": {
    "body": {
      "transform": "json_transform",
      "rules": [
        {
          "action": "mask",
          "path": "data.*.phone",
          "mask_type": "phone"
        },
        {
          "action": "mask",
          "path": "data.*.email",
          "mask_type": "email"
        },
        {
          "action": "mask",
          "path": "data.*.id_card",
          "mask_type": "id_card"
        },
        {
          "action": "remove",
          "path": "data.*.password_hash"
        }
      ]
    }
  }
}
```

### 5. 响应包装

```json
{
  "response_rules": {
    "body": {
      "wrapper": {
        "enabled": true,
        "code_field": "code",
        "message_field": "message",
        "data_field": "data",
        "timestamp_field": "timestamp",
        "success_code": 0,
        "error_code": -1
      }
    }
  }
}
```

## 📊 支持的转换操作

### 请求转换操作

| 操作类型 | 适用范围 | 描述 | 示例 |
|---------|---------|------|------|
| `add` | 请求头、参数、字段 | 添加新的数据 | 添加默认参数 |
| `remove` | 请求头、参数、字段 | 删除指定数据 | 移除敏感参数 |
| `rename` | 请求头、参数、字段 | 重命名数据键 | 参数名标准化 |
| `replace` | 请求头、参数、字段 | 替换数据值 | 值格式转换 |
| `convert` | 参数、字段 | 数据类型转换 | 字符串转数字 |

### 响应转换操作

| 操作类型 | 适用范围 | 描述 | 示例 |
|---------|---------|------|------|
| `mask` | 响应字段 | 敏感数据脱敏 | 手机号脱敏 |
| `remove` | 响应头、字段 | 删除敏感数据 | 移除内部字段 |
| `add` | 响应头、字段 | 添加元数据 | 添加时间戳 |
| `rename` | 响应头、字段 | 字段重命名 | API 兼容性 |
| `replace` | 响应头、字段 | 值替换 | 状态码映射 |

### 脱敏类型

| 脱敏类型 | 描述 | 示例 |
|---------|------|------|
| `phone` | 手机号脱敏 | `138****5678` |
| `email` | 邮箱脱敏 | `u***<EMAIL>` |
| `id_card` | 身份证脱敏 | `110***********1234` |
| `credit_card` | 信用卡脱敏 | `1234 **** **** 5678` |
| `custom` | 自定义脱敏 | 基于正则表达式 |

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/transformer -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试请求转换
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token123" \
  -d '{"name": "test", "age": "25"}'

# 测试响应脱敏
curl http://localhost:8080/api/v1/users
```

## 🔍 监控和调试

### 转换统计
```bash
# 查看转换中间件状态
curl http://localhost:8080/api/v1/transform/stats

# 查看转换规则统计
curl http://localhost:8080/api/v1/transform/rules/stats
```

### 日志监控
```bash
# 查看转换日志
tail -f logs/api-gateway.log | grep "转换"

# 查看转换详情
tail -f logs/api-gateway.log | grep "transform"
```

## 🛠️ 故障排除

### 常见问题

1. **转换规则不生效**
   - 检查规则文件格式是否正确
   - 确认规则路径和方法匹配
   - 查看规则是否启用 (`enabled: true`)

2. **数据类型转换失败**
   - 检查源数据格式
   - 确认目标数据类型支持
   - 查看转换错误日志

3. **脱敏效果不符合预期**
   - 确认脱敏类型配置
   - 检查字段路径是否正确
   - 验证数据格式匹配

### 调试技巧

1. **启用详细日志**：
   ```yaml
   # 在配置文件中
   log:
     level: debug
   ```

2. **测试转换规则**：
   ```bash
   # 使用测试工具验证规则
   go test ./pkg/transformer -run TestSpecificRule -v
   ```

3. **规则验证**：
   ```go
   // 验证规则语法
   ruleManager := transformer.NewRuleManager(logger, rulesDir, false)
   err := ruleManager.LoadRulesFromDirectory()
   ```

## 📈 性能优化

1. **规则缓存**：转换规则会被缓存在内存中
2. **条件评估**：只有满足条件的规则才会执行
3. **批量处理**：多个转换操作会批量执行
4. **响应缓冲**：可选的响应缓冲以提高性能

## 🔒 安全考虑

1. **数据脱敏**：确保敏感数据不会泄露
2. **规则验证**：防止恶意转换规则
3. **访问控制**：转换规则的访问权限控制
4. **审计日志**：记录所有转换操作

## 🚀 扩展功能

### 计划中的功能
- [ ] **模板引擎**：支持更复杂的数据转换模板
- [ ] **条件表达式**：更强大的条件评估引擎
- [ ] **插件系统**：支持自定义转换插件
- [ ] **性能监控**：转换操作的性能指标
- [ ] **规则热更新**：无需重启的规则动态更新

---

**注意**：请求/响应转换系统是 API 网关数据处理的核心组件，确保了数据的安全性、一致性和合规性。在生产环境中使用时，请仔细测试转换规则，确保不会影响业务逻辑。
