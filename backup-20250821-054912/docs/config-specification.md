# 配置管理层规范文档

## 1. 配置管理架构设计

### 1.1 设计原则

- **集中化管理**: 所有配置统一存储和管理
- **环境隔离**: 不同环境的配置完全隔离
- **版本控制**: 配置变更的完整版本历史
- **动态更新**: 支持配置的热更新和实时生效
- **安全存储**: 敏感配置的加密存储
- **审计追踪**: 完整的配置变更审计日志

### 1.2 配置层次结构

```
全局配置 (Global)
├── 平台配置 (Platform)
│   ├── 系统配置 (System)
│   ├── 服务配置 (Service)
│   └── 功能开关 (Feature Flags)
├── 租户配置 (Tenant)
│   ├── 租户设置 (Settings)
│   ├── 资源配额 (Quotas)
│   └── 安全策略 (Security)
└── 应用配置 (Application)
    ├── 环境变量 (Environment Variables)
    ├── 配置文件 (Config Files)
    └── 密钥管理 (Secrets)
```

### 1.3 配置优先级

```
应用配置 > 租户配置 > 平台配置 > 全局配置 > 默认配置
```

## 2. 配置存储模型

### 2.1 配置实体设计

```go
// Config 配置实体
type Config struct {
    ID          string            `json:"id"`
    Key         string            `json:"key"`           // 配置键
    Value       interface{}       `json:"value"`         // 配置值
    Type        ConfigType        `json:"type"`          // 配置类型
    Scope       ConfigScope       `json:"scope"`         // 配置作用域
    ScopeID     string            `json:"scope_id"`      // 作用域ID
    Environment string            `json:"environment"`   // 环境
    Encrypted   bool              `json:"encrypted"`     // 是否加密
    Description string            `json:"description"`   // 描述
    Tags        []string          `json:"tags"`          // 标签
    Metadata    ConfigMetadata    `json:"metadata"`      // 元数据
    Version     int               `json:"version"`       // 版本号
    CreatedAt   time.Time         `json:"created_at"`
    UpdatedAt   time.Time         `json:"updated_at"`
    CreatedBy   string            `json:"created_by"`
    UpdatedBy   string            `json:"updated_by"`
}

// ConfigType 配置类型
type ConfigType string

const (
    ConfigTypeString  ConfigType = "string"
    ConfigTypeNumber  ConfigType = "number"
    ConfigTypeBool    ConfigType = "bool"
    ConfigTypeJSON    ConfigType = "json"
    ConfigTypeYAML    ConfigType = "yaml"
    ConfigTypeSecret  ConfigType = "secret"
    ConfigTypeFile    ConfigType = "file"
)

// ConfigScope 配置作用域
type ConfigScope string

const (
    ConfigScopeGlobal      ConfigScope = "global"
    ConfigScopePlatform    ConfigScope = "platform"
    ConfigScopeTenant      ConfigScope = "tenant"
    ConfigScopeApplication ConfigScope = "application"
    ConfigScopeService     ConfigScope = "service"
)
```

### 2.2 配置版本管理

```go
// ConfigVersion 配置版本
type ConfigVersion struct {
    ID          string      `json:"id"`
    ConfigID    string      `json:"config_id"`
    Version     int         `json:"version"`
    Value       interface{} `json:"value"`
    ChangeType  ChangeType  `json:"change_type"`  // create, update, delete
    ChangedBy   string      `json:"changed_by"`
    ChangeReason string     `json:"change_reason"`
    CreatedAt   time.Time   `json:"created_at"`
}

// ChangeType 变更类型
type ChangeType string

const (
    ChangeTypeCreate ChangeType = "create"
    ChangeTypeUpdate ChangeType = "update"
    ChangeTypeDelete ChangeType = "delete"
)
```

## 3. 配置分类管理

### 3.1 系统配置

#### 平台基础配置
```yaml
platform:
  name: "PaaS 平台"
  version: "1.0.0"
  description: "企业级 PaaS 平台"
  
  # 服务发现配置
  service_discovery:
    type: "consul"              # consul, etcd, kubernetes
    endpoints: ["localhost:8500"]
    
  # 负载均衡配置
  load_balancer:
    type: "nginx"               # nginx, haproxy, envoy
    algorithm: "round_robin"    # round_robin, least_conn, ip_hash
    
  # 存储配置
  storage:
    default_class: "standard"
    classes:
      standard:
        type: "local"
        path: "/data/storage"
      fast:
        type: "ssd"
        path: "/data/fast-storage"
```

#### 服务配置模板
```yaml
services:
  app-manager:
    port: 8081
    replicas: 2
    resources:
      requests:
        cpu: "100m"
        memory: "128Mi"
      limits:
        cpu: "500m"
        memory: "512Mi"
    health_check:
      path: "/health"
      interval: "30s"
      timeout: "5s"
      
  cicd-service:
    port: 8082
    replicas: 1
    build_timeout: "30m"
    max_concurrent_builds: 10
```

### 3.2 应用配置

#### 环境变量管理
```yaml
# 应用环境变量配置
app_config:
  app_id: "app-123"
  environment: "production"
  
  # 普通环境变量
  env_vars:
    NODE_ENV: "production"
    LOG_LEVEL: "info"
    PORT: "3000"
    
  # 密钥环境变量 (加密存储)
  secrets:
    DATABASE_URL: "******************************/db"
    API_KEY: "secret-api-key-123"
    JWT_SECRET: "jwt-secret-key"
    
  # 配置文件
  config_files:
    - name: "app.json"
      content: |
        {
          "database": {
            "host": "localhost",
            "port": 5432
          }
        }
      mount_path: "/app/config/app.json"
      
    - name: "nginx.conf"
      content: |
        server {
          listen 80;
          location / {
            proxy_pass http://app:3000;
          }
        }
      mount_path: "/etc/nginx/nginx.conf"
```

### 3.3 功能开关

```yaml
# 功能开关配置
feature_flags:
  # 全局功能开关
  global:
    new_ui_enabled: true
    beta_features_enabled: false
    maintenance_mode: false
    
  # 租户级功能开关
  tenant:
    tenant-123:
      advanced_monitoring: true
      custom_domains: true
      api_rate_limit: 1000
      
  # 应用级功能开关
  application:
    app-456:
      auto_scaling: true
      blue_green_deployment: false
      canary_deployment: true
```

## 4. 配置服务接口

### 4.1 配置 CRUD 操作

```go
type ConfigService interface {
    // 基础 CRUD
    CreateConfig(ctx context.Context, req *CreateConfigRequest) (*Config, error)
    GetConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Config, error)
    UpdateConfig(ctx context.Context, configID string, req *UpdateConfigRequest) (*Config, error)
    DeleteConfig(ctx context.Context, configID string) error
    
    // 批量操作
    BatchGetConfigs(ctx context.Context, keys []string, scope ConfigScope, scopeID string, env string) (map[string]*Config, error)
    BatchUpdateConfigs(ctx context.Context, configs []*UpdateConfigRequest) error
    
    // 配置查询
    ListConfigs(ctx context.Context, filter *ConfigFilter) ([]*Config, int64, error)
    SearchConfigs(ctx context.Context, query string, scope ConfigScope, scopeID string) ([]*Config, error)
    
    // 配置渲染
    RenderConfig(ctx context.Context, template string, variables map[string]interface{}) (string, error)
    GetEffectiveConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (interface{}, error)
    
    // 版本管理
    GetConfigVersions(ctx context.Context, configID string) ([]*ConfigVersion, error)
    RollbackConfig(ctx context.Context, configID string, version int) error
    
    // 配置导入导出
    ExportConfigs(ctx context.Context, scope ConfigScope, scopeID string, format string) ([]byte, error)
    ImportConfigs(ctx context.Context, data []byte, format string, scope ConfigScope, scopeID string) error
    
    // 配置验证
    ValidateConfig(ctx context.Context, config *Config) error
    ValidateConfigValue(ctx context.Context, configType ConfigType, value interface{}) error
}
```

### 4.2 配置监听和通知

```go
type ConfigWatcher interface {
    // 监听配置变更
    Watch(ctx context.Context, key string, scope ConfigScope, scopeID string) (<-chan *ConfigChangeEvent, error)
    WatchPrefix(ctx context.Context, prefix string, scope ConfigScope, scopeID string) (<-chan *ConfigChangeEvent, error)
    
    // 停止监听
    Unwatch(key string) error
}

type ConfigChangeEvent struct {
    Type      ChangeType  `json:"type"`       // create, update, delete
    Config    *Config     `json:"config"`
    OldValue  interface{} `json:"old_value"`
    NewValue  interface{} `json:"new_value"`
    Timestamp time.Time   `json:"timestamp"`
}
```

## 5. 密钥管理

### 5.1 密钥存储

#### 加密策略
- 使用 AES-256-GCM 加密算法
- 密钥派生使用 PBKDF2
- 每个密钥使用唯一的盐值
- 支持密钥轮换

#### 密钥分级
```yaml
# 密钥分级管理
secrets:
  # L1: 系统级密钥 (平台核心密钥)
  system:
    encryption_key: "系统主加密密钥"
    jwt_secret: "JWT 签名密钥"
    database_key: "数据库加密密钥"
    
  # L2: 租户级密钥 (租户专用密钥)
  tenant:
    tenant-123:
      api_key: "租户 API 密钥"
      webhook_secret: "Webhook 密钥"
      
  # L3: 应用级密钥 (应用专用密钥)
  application:
    app-456:
      database_url: "应用数据库连接字符串"
      redis_url: "Redis 连接字符串"
      third_party_api_key: "第三方服务 API 密钥"
```

### 5.2 密钥轮换

```go
type SecretRotationService interface {
    // 密钥轮换
    RotateSecret(ctx context.Context, secretID string) error
    ScheduleRotation(ctx context.Context, secretID string, schedule string) error
    
    // 轮换策略
    SetRotationPolicy(ctx context.Context, secretID string, policy *RotationPolicy) error
    GetRotationPolicy(ctx context.Context, secretID string) (*RotationPolicy, error)
}

type RotationPolicy struct {
    Enabled         bool          `json:"enabled"`
    Interval        time.Duration `json:"interval"`        // 轮换间隔
    MaxAge          time.Duration `json:"max_age"`         // 最大年龄
    NotifyBefore    time.Duration `json:"notify_before"`   // 提前通知时间
    AutoRotate      bool          `json:"auto_rotate"`     // 是否自动轮换
    BackupVersions  int           `json:"backup_versions"` // 备份版本数
}
```

## 6. 配置模板系统

### 6.1 模板引擎

#### 模板语法
```yaml
# 配置模板示例
database:
  host: "{{ .Database.Host | default "localhost" }}"
  port: {{ .Database.Port | default 5432 }}
  name: "{{ .Database.Name }}"
  username: "{{ .Database.Username }}"
  password: "{{ secret "database.password" }}"
  
redis:
  addr: "{{ .Redis.Host }}:{{ .Redis.Port | default 6379 }}"
  password: "{{ secret "redis.password" | default "" }}"
  
app:
  name: "{{ .App.Name }}"
  version: "{{ .App.Version }}"
  environment: "{{ .Environment }}"
  debug: {{ .Debug | default false }}
  
  # 条件配置
  {{- if eq .Environment "production" }}
  log_level: "error"
  {{- else }}
  log_level: "debug"
  {{- end }}
  
  # 循环配置
  services:
  {{- range .Services }}
  - name: "{{ .Name }}"
    url: "{{ .URL }}"
    timeout: "{{ .Timeout | default "30s" }}"
  {{- end }}
```

#### 模板函数
```go
// 内置模板函数
var TemplateFuncs = template.FuncMap{
    "default":    defaultFunc,     // 默认值
    "secret":     secretFunc,      // 获取密钥
    "env":        envFunc,         // 获取环境变量
    "base64":     base64Func,      // Base64 编码
    "sha256":     sha256Func,      // SHA256 哈希
    "random":     randomFunc,      // 生成随机字符串
    "now":        nowFunc,         // 当前时间
    "toJson":     toJsonFunc,      // 转换为 JSON
    "fromJson":   fromJsonFunc,    // 从 JSON 解析
    "toYaml":     toYamlFunc,      // 转换为 YAML
    "fromYaml":   fromYamlFunc,    // 从 YAML 解析
}
```

### 6.2 配置继承

```yaml
# 基础配置模板
base_config: &base
  database:
    driver: "postgres"
    pool_size: 10
    timeout: "30s"
    
  redis:
    pool_size: 10
    timeout: "5s"
    
  logging:
    format: "json"
    level: "info"

# 开发环境配置 (继承基础配置)
development:
  <<: *base
  database:
    <<: *base.database
    host: "localhost"
    name: "paas_dev"
  logging:
    level: "debug"

# 生产环境配置
production:
  <<: *base
  database:
    <<: *base.database
    host: "prod-db.example.com"
    name: "paas_prod"
    ssl_mode: "require"
  logging:
    level: "warn"
```

## 7. 动态配置更新

### 7.1 配置热更新

#### 更新机制
1. 配置变更检测
2. 配置验证
3. 配置推送
4. 服务重载
5. 状态确认

#### 更新策略
```yaml
update_strategy:
  type: "rolling"               # rolling, immediate, scheduled
  
  # 滚动更新配置
  rolling:
    batch_size: 1               # 每批更新实例数
    max_unavailable: 0          # 最大不可用实例数
    timeout: "5m"               # 更新超时时间
    
  # 立即更新配置
  immediate:
    force: false                # 是否强制更新
    
  # 定时更新配置
  scheduled:
    time: "02:00"               # 更新时间
    timezone: "Asia/Shanghai"   # 时区
```

### 7.2 配置推送机制

#### WebSocket 推送
```go
type ConfigPushService interface {
    // 推送配置更新
    PushUpdate(ctx context.Context, update *ConfigUpdate) error
    
    // 订阅配置更新
    Subscribe(ctx context.Context, filter *ConfigFilter) (<-chan *ConfigUpdate, error)
    
    // 取消订阅
    Unsubscribe(subscriptionID string) error
}

type ConfigUpdate struct {
    Type        UpdateType  `json:"type"`         // full, partial, delete
    Scope       ConfigScope `json:"scope"`
    ScopeID     string      `json:"scope_id"`
    Environment string      `json:"environment"`
    Configs     []*Config   `json:"configs"`
    Timestamp   time.Time   `json:"timestamp"`
}
```

#### HTTP 长轮询
```go
// 长轮询接口
func (h *Handler) WatchConfigs(c *gin.Context) {
    timeout := 30 * time.Second
    ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
    defer cancel()
    
    // 获取当前配置版本
    currentVersion := c.Query("version")
    
    // 等待配置更新
    updates, err := h.service.WaitForUpdates(ctx, currentVersion)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, updates)
}
```

## 8. 配置验证和约束

### 8.1 配置模式验证

#### JSON Schema 验证
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "database": {
      "type": "object",
      "properties": {
        "host": {
          "type": "string",
          "format": "hostname"
        },
        "port": {
          "type": "integer",
          "minimum": 1,
          "maximum": 65535
        },
        "name": {
          "type": "string",
          "minLength": 1,
          "maxLength": 63
        }
      },
      "required": ["host", "port", "name"]
    }
  },
  "required": ["database"]
}
```

#### 自定义验证器
```go
type ConfigValidator interface {
    Validate(config *Config) error
}

// 数据库连接验证器
type DatabaseConfigValidator struct{}

func (v *DatabaseConfigValidator) Validate(config *Config) error {
    if config.Key == "database.url" {
        // 验证数据库连接字符串格式
        if !isValidDatabaseURL(config.Value.(string)) {
            return fmt.Errorf("无效的数据库连接字符串")
        }
        
        // 测试数据库连接
        if err := testDatabaseConnection(config.Value.(string)); err != nil {
            return fmt.Errorf("数据库连接测试失败: %w", err)
        }
    }
    return nil
}
```

### 8.2 配置约束

#### 值约束
```yaml
constraints:
  # 数值约束
  server.port:
    type: "integer"
    min: 1024
    max: 65535
    
  # 字符串约束
  app.name:
    type: "string"
    min_length: 3
    max_length: 50
    pattern: "^[a-zA-Z0-9-]+$"
    
  # 枚举约束
  log.level:
    type: "string"
    enum: ["debug", "info", "warn", "error"]
    
  # 依赖约束
  ssl.enabled:
    type: "boolean"
    when:
      ssl.cert_file:
        required: true
      ssl.key_file:
        required: true
```

## 9. 配置安全

### 9.1 访问控制

#### 权限模型
```yaml
config_permissions:
  # 全局配置权限
  global:
    read: ["system:admin"]
    write: ["system:admin"]
    
  # 租户配置权限
  tenant:
    read: ["tenant:admin", "tenant:user"]
    write: ["tenant:admin"]
    
  # 应用配置权限
  application:
    read: ["app:owner", "app:developer", "app:viewer"]
    write: ["app:owner", "app:developer"]
    
  # 密钥权限
  secrets:
    read: ["secret:reader"]
    write: ["secret:writer"]
    manage: ["secret:admin"]
```

### 9.2 敏感配置保护

#### 配置脱敏
```go
type ConfigMasker interface {
    Mask(config *Config) *Config
}

// 密码脱敏
func MaskPassword(value string) string {
    if len(value) <= 4 {
        return "****"
    }
    return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]
}

// URL 脱敏
func MaskURL(url string) string {
    // 隐藏 URL 中的密码部分
    re := regexp.MustCompile(`://([^:]+):([^@]+)@`)
    return re.ReplaceAllString(url, "://$1:****@")
}
```

## 10. 配置分发和同步

### 10.1 配置分发

#### 推送模式
```go
type ConfigDistributor interface {
    // 推送配置到服务实例
    PushToInstance(ctx context.Context, instanceID string, configs []*Config) error
    
    // 推送配置到服务集群
    PushToCluster(ctx context.Context, clusterID string, configs []*Config) error
    
    // 批量推送
    BatchPush(ctx context.Context, targets []string, configs []*Config) error
}
```

#### 拉取模式
```go
type ConfigPuller interface {
    // 拉取最新配置
    PullLatest(ctx context.Context, scope ConfigScope, scopeID string, version int64) ([]*Config, error)
    
    // 增量拉取
    PullIncremental(ctx context.Context, scope ConfigScope, scopeID string, since time.Time) ([]*Config, error)
}
```

### 10.2 配置同步

#### 多区域同步
```yaml
sync_strategy:
  type: "master_slave"          # master_slave, peer_to_peer
  
  # 主从同步
  master_slave:
    master_region: "us-east-1"
    slave_regions: ["us-west-1", "eu-west-1"]
    sync_interval: "5m"
    conflict_resolution: "master_wins"
    
  # 对等同步
  peer_to_peer:
    regions: ["us-east-1", "us-west-1", "eu-west-1"]
    consensus_algorithm: "raft"
    conflict_resolution: "timestamp"
```

## 11. 配置监控和告警

### 11.1 配置指标

#### 使用统计
- 配置读取次数
- 配置更新频率
- 配置错误率
- 配置响应时间

#### 健康检查
- 配置服务可用性
- 配置数据一致性
- 配置同步状态
- 密钥过期检查

### 11.2 配置告警

#### 告警规则
```yaml
alerts:
  # 配置更新告警
  config_updated:
    condition: "config.scope == 'production'"
    channels: ["slack", "email"]
    message: "生产环境配置已更新: {{ .Config.Key }}"
    
  # 密钥过期告警
  secret_expiring:
    condition: "secret.expires_in < 7d"
    channels: ["email"]
    message: "密钥即将过期: {{ .Secret.Name }}"
    
  # 配置错误告警
  config_error:
    condition: "config.validation_failed"
    channels: ["slack", "pagerduty"]
    message: "配置验证失败: {{ .Error.Message }}"
```

## 12. 配置 API 设计

### 12.1 RESTful API

```
GET    /api/v1/configs                    # 获取配置列表
POST   /api/v1/configs                    # 创建配置
GET    /api/v1/configs/{id}               # 获取配置详情
PUT    /api/v1/configs/{id}               # 更新配置
DELETE /api/v1/configs/{id}               # 删除配置

GET    /api/v1/configs/{id}/versions      # 获取配置版本历史
POST   /api/v1/configs/{id}/rollback      # 回滚配置

POST   /api/v1/configs/batch              # 批量操作配置
GET    /api/v1/configs/search             # 搜索配置

POST   /api/v1/configs/export             # 导出配置
POST   /api/v1/configs/import             # 导入配置

GET    /api/v1/configs/watch              # 监听配置变更 (WebSocket)
```

### 12.2 GraphQL API

```graphql
type Query {
  # 获取配置
  config(key: String!, scope: ConfigScope!, scopeId: String!, environment: String): Config
  configs(filter: ConfigFilter!): ConfigConnection!
  
  # 获取有效配置 (合并继承)
  effectiveConfig(key: String!, scope: ConfigScope!, scopeId: String!, environment: String): JSON
  
  # 配置版本
  configVersions(configId: String!): [ConfigVersion!]!
}

type Mutation {
  # 配置管理
  createConfig(input: CreateConfigInput!): Config!
  updateConfig(id: String!, input: UpdateConfigInput!): Config!
  deleteConfig(id: String!): Boolean!
  
  # 批量操作
  batchUpdateConfigs(inputs: [UpdateConfigInput!]!): [Config!]!
  
  # 版本管理
  rollbackConfig(id: String!, version: Int!): Config!
}

type Subscription {
  # 配置变更订阅
  configChanged(filter: ConfigFilter!): ConfigChangeEvent!
}
```
