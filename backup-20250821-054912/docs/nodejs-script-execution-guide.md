# Node.js脚本执行指南

## 📋 概述

本指南详细说明如何在PaaS平台上执行Node.js脚本，包括配置、API调用、依赖管理等完整流程。

## 🚀 快速开始

### 1. 基本Node.js脚本执行

**API请求示例**:
```http
POST /api/v1/scripts/execute
Content-Type: application/json

{
    "app_id": "my-nodejs-app",
    "script_path": "index.js",
    "runtime_type": "nodejs",
    "parameters": {
        "input_file": "data.json",
        "output_format": "csv"
    },
    "environment": {
        "NODE_ENV": "production",
        "DEBUG": "app:*"
    },
    "timeout": 300,
    "priority": 5
}
```

**响应示例**:
```json
{
    "task_id": "task-nodejs-123",
    "status": "pending",
    "message": "Node.js脚本任务已提交",
    "created_at": "2024-01-01T10:00:00Z"
}
```

### 2. 带依赖管理的脚本执行

**使用NPM**:
```json
{
    "app_id": "my-nodejs-app",
    "script_path": "process-data.js",
    "runtime_type": "nodejs",
    "runtime_config": {
        "version": "18.17.0",
        "image_tag": "node:18.17.0-alpine",
        "dependencies": {
            "type": "npm",
            "file": "package.json",
            "options": {
                "production": true,
                "silent": true
            }
        },
        "resources": {
            "cpu_limit": "1",
            "memory_limit": "1Gi"
        },
        "options": {
            "max_memory": 1024,
            "experimental": false
        }
    },
    "parameters": {
        "data_source": "database",
        "batch_size": 1000
    }
}
```

**使用Yarn**:
```json
{
    "runtime_config": {
        "dependencies": {
            "type": "yarn",
            "file": "package.json",
            "options": {
                "production": true,
                "frozen": true
            }
        }
    }
}
```

### 3. 直接指定依赖包

```json
{
    "runtime_config": {
        "dependencies": {
            "type": "npm",
            "packages": [
                "lodash@4.17.21",
                "axios@1.4.0",
                "moment@2.29.4"
            ],
            "registry": "https://registry.npmjs.org/"
        }
    }
}
```

## 🔧 运行时配置详解

### 1. 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `version` | string | "18.17.0" | Node.js版本 |
| `image_tag` | string | "node:18.17.0-alpine" | Docker镜像标签 |

### 2. 依赖配置

**NPM配置**:
```json
{
    "dependencies": {
        "type": "npm",
        "file": "package.json",
        "registry": "https://registry.npmjs.org/",
        "options": {
            "production": true,    // 只安装生产依赖
            "silent": true,        // 静默安装
            "no_cache": true       // 不使用缓存
        }
    }
}
```

**Yarn配置**:
```json
{
    "dependencies": {
        "type": "yarn",
        "file": "package.json",
        "registry": "https://registry.yarnpkg.com/",
        "options": {
            "production": true,    // 只安装生产依赖
            "frozen": true,        // 使用锁定文件
            "silent": true         // 静默安装
        }
    }
}
```

### 3. 资源限制

```json
{
    "resources": {
        "cpu_limit": "500m",      // CPU限制
        "memory_limit": "512Mi",  // 内存限制
        "disk_limit": "1Gi"       // 磁盘限制
    }
}
```

### 4. Node.js选项

```json
{
    "options": {
        "max_memory": 512,        // 最大内存(MB)
        "experimental": false,    // 启用实验性功能
        "debug": false,          // 调试模式
        "profile": false,        // 性能分析
        "esm": false            // ES模块支持
    }
}
```

## 📝 脚本示例

### 1. 基础数据处理脚本

**index.js**:
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);
const inputFile = getArgValue('--input_file') || 'input.json';
const outputFormat = getArgValue('--output_format') || 'json';

// 获取环境变量参数
const dataSource = process.env.PARAM_DATA_SOURCE || 'file';
const batchSize = parseInt(process.env.PARAM_BATCH_SIZE) || 100;

function getArgValue(argName) {
    const index = args.indexOf(argName);
    return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
}

async function processData() {
    try {
        console.log(`开始处理数据: ${inputFile}`);
        console.log(`数据源: ${dataSource}, 批次大小: ${batchSize}`);
        
        // 读取输入文件
        const inputPath = path.join('/workspace', inputFile);
        const data = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
        
        // 处理数据
        const processedData = data.map(item => ({
            ...item,
            processed_at: new Date().toISOString(),
            batch_size: batchSize
        }));
        
        // 输出结果
        const outputPath = path.join('/output', `result.${outputFormat}`);
        if (outputFormat === 'csv') {
            const csv = convertToCSV(processedData);
            fs.writeFileSync(outputPath, csv);
        } else {
            fs.writeFileSync(outputPath, JSON.stringify(processedData, null, 2));
        }
        
        console.log(`数据处理完成，输出文件: ${outputPath}`);
        console.log(`处理记录数: ${processedData.length}`);
        
    } catch (error) {
        console.error('数据处理失败:', error.message);
        process.exit(1);
    }
}

function convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];
    
    for (const row of data) {
        const values = headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value}"` : value;
        });
        csvRows.push(values.join(','));
    }
    
    return csvRows.join('\n');
}

// 执行主函数
processData();
```

### 2. 带依赖的Web爬虫脚本

**package.json**:
```json
{
    "name": "web-scraper",
    "version": "1.0.0",
    "description": "Web scraping script",
    "main": "scraper.js",
    "dependencies": {
        "axios": "^1.4.0",
        "cheerio": "^1.0.0-rc.12",
        "csv-writer": "^1.6.0"
    },
    "engines": {
        "node": ">=16.0.0"
    }
}
```

**scraper.js**:
```javascript
const axios = require('axios');
const cheerio = require('cheerio');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');

// 获取参数
const targetUrl = process.env.PARAM_TARGET_URL || 'https://example.com';
const selector = process.env.PARAM_SELECTOR || 'h1';
const maxPages = parseInt(process.env.PARAM_MAX_PAGES) || 10;

async function scrapeWebsite() {
    try {
        console.log(`开始爬取网站: ${targetUrl}`);
        
        const results = [];
        
        for (let page = 1; page <= maxPages; page++) {
            console.log(`正在爬取第 ${page} 页...`);
            
            const response = await axios.get(`${targetUrl}?page=${page}`, {
                timeout: 10000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (compatible; PaaS-Scraper/1.0)'
                }
            });
            
            const $ = cheerio.load(response.data);
            
            $(selector).each((index, element) => {
                results.push({
                    page: page,
                    index: index,
                    text: $(element).text().trim(),
                    scraped_at: new Date().toISOString()
                });
            });
            
            // 避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 保存结果
        const csvWriter = createCsvWriter({
            path: '/output/scraped_data.csv',
            header: [
                {id: 'page', title: 'Page'},
                {id: 'index', title: 'Index'},
                {id: 'text', title: 'Text'},
                {id: 'scraped_at', title: 'Scraped At'}
            ]
        });
        
        await csvWriter.writeRecords(results);
        
        console.log(`爬取完成，共获取 ${results.length} 条数据`);
        
    } catch (error) {
        console.error('爬取失败:', error.message);
        process.exit(1);
    }
}

scrapeWebsite();
```

## 🔍 任务监控

### 1. 查看任务状态

```http
GET /api/v1/scripts/tasks/task-nodejs-123
```

**响应**:
```json
{
    "id": "task-nodejs-123",
    "status": "running",
    "progress": 65,
    "start_time": "2024-01-01T10:00:00Z",
    "estimated_completion": "2024-01-01T10:05:00Z",
    "message": "正在执行Node.js脚本"
}
```

### 2. 获取执行日志

```http
GET /api/v1/scripts/tasks/task-nodejs-123/logs
```

### 3. 获取执行结果

```http
GET /api/v1/scripts/tasks/task-nodejs-123/result
```

**响应**:
```json
{
    "task_id": "task-nodejs-123",
    "status": "completed",
    "exit_code": 0,
    "output": "数据处理完成，输出文件: /output/result.json\n处理记录数: 1000",
    "duration": 45000,
    "artifacts": [
        {
            "name": "result.json",
            "url": "/api/v1/scripts/tasks/task-nodejs-123/artifacts/result.json",
            "size": 2048
        }
    ]
}
```

## ⚠️ 注意事项

### 1. 版本兼容性
- 确保package.json中的engines字段与运行时版本匹配
- 使用LTS版本的Node.js以获得最佳稳定性

### 2. 内存管理
- Node.js默认内存限制较小，大数据处理时需要调整max_memory选项
- 使用流处理大文件，避免一次性加载到内存

### 3. 错误处理
- 始终使用try-catch包装异步操作
- 适当设置process.exit()退出码

### 4. 安全考虑
- 避免在脚本中硬编码敏感信息
- 使用环境变量传递配置参数
- 验证输入参数，防止注入攻击

## 🔧 故障排除

### 1. 常见错误

**模块未找到**:
```
Error: Cannot find module 'xxx'
```
解决方案：检查package.json依赖配置，确保模块已正确安装

**内存不足**:
```
FATAL ERROR: Ineffective mark-compacts near heap limit
```
解决方案：增加max_memory配置或优化代码内存使用

**权限错误**:
```
Error: EACCES: permission denied
```
解决方案：检查文件路径权限，使用/workspace和/output目录

### 2. 调试技巧

**启用调试模式**:
```json
{
    "runtime_config": {
        "options": {
            "debug": true
        }
    },
    "environment": {
        "DEBUG": "*"
    }
}
```

**性能分析**:
```json
{
    "runtime_config": {
        "options": {
            "profile": true
        }
    }
}
```

## 📚 最佳实践

1. **模块化设计**: 将复杂逻辑拆分为独立模块
2. **错误处理**: 实现完整的错误处理和日志记录
3. **参数验证**: 验证输入参数的有效性
4. **资源清理**: 及时释放文件句柄和网络连接
5. **进度报告**: 对于长时间运行的任务，定期输出进度信息
