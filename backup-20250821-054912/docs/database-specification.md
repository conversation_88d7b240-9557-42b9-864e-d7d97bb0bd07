# 数据存储层规范文档

## 1. 数据存储抽象层设计

### 1.1 设计原则

- **数据库无关性**: 支持多种数据库，通过适配器模式实现
- **连接池管理**: 高效的数据库连接池管理
- **事务支持**: 完整的事务管理机制
- **迁移管理**: 自动化数据库迁移和版本管理
- **性能优化**: 查询优化、索引管理、缓存策略

### 1.2 架构设计

```
┌─────────────────────────────────────────────────────────┐
│                    应用层                                │
├─────────────────────────────────────────────────────────┤
│                  数据访问层 (DAO)                        │
├─────────────────────────────────────────────────────────┤
│                  ORM 抽象层 (GORM)                       │
├─────────────────────────────────────────────────────────┤
│                  数据库适配器层                          │
├─────────────────┬─────────────────┬─────────────────────┤
│   SQLite 适配器  │ PostgreSQL 适配器│    MySQL 适配器     │
├─────────────────┼─────────────────┼─────────────────────┤
│     SQLite      │   PostgreSQL    │       MySQL         │
└─────────────────┴─────────────────┴─────────────────────┘
```

## 2. 数据库连接池管理

### 2.1 连接池配置

```yaml
database:
  # 基础配置
  driver: postgres              # 数据库驱动
  dsn: "host=localhost..."      # 数据源名称
  
  # 连接池配置
  max_open_conns: 100           # 最大打开连接数
  max_idle_conns: 10            # 最大空闲连接数
  conn_max_lifetime: 3600       # 连接最大生存时间(秒)
  conn_max_idle_time: 1800      # 连接最大空闲时间(秒)
  
  # 性能配置
  slow_query_threshold: 1000    # 慢查询阈值(毫秒)
  log_level: warn               # 日志级别
  
  # 重试配置
  max_retries: 3                # 最大重试次数
  retry_interval: 1s            # 重试间隔
```

### 2.2 连接池监控

```go
// 连接池统计信息
type PoolStats struct {
    MaxOpenConnections int           // 最大打开连接数
    OpenConnections    int           // 当前打开连接数
    InUse              int           // 正在使用的连接数
    Idle               int           // 空闲连接数
    WaitCount          int64         // 等待连接的总次数
    WaitDuration       time.Duration // 等待连接的总时间
    MaxIdleClosed      int64         // 因空闲而关闭的连接数
    MaxLifetimeClosed  int64         // 因生存时间到期而关闭的连接数
}
```

## 3. ORM/查询构建器抽象接口

### 3.1 Repository 模式

```go
// BaseRepository 基础仓库接口
type BaseRepository[T any] interface {
    // 基础 CRUD 操作
    Create(ctx context.Context, entity *T) error
    GetByID(ctx context.Context, id string) (*T, error)
    Update(ctx context.Context, entity *T) error
    Delete(ctx context.Context, id string) error
    
    // 查询操作
    List(ctx context.Context, filter Filter) ([]*T, error)
    Count(ctx context.Context, filter Filter) (int64, error)
    Exists(ctx context.Context, filter Filter) (bool, error)
    
    // 批量操作
    BatchCreate(ctx context.Context, entities []*T) error
    BatchUpdate(ctx context.Context, entities []*T) error
    BatchDelete(ctx context.Context, ids []string) error
    
    // 事务操作
    WithTx(tx *gorm.DB) BaseRepository[T]
}

// Filter 查询过滤器
type Filter interface {
    Apply(db *gorm.DB) *gorm.DB
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
    db     *gorm.DB
    model  interface{}
    wheres []WhereClause
    orders []OrderClause
    limit  int
    offset int
}

// WhereClause WHERE 条件
type WhereClause struct {
    Column   string
    Operator string
    Value    interface{}
}

// OrderClause 排序条件
type OrderClause struct {
    Column string
    Desc   bool
}
```

### 3.2 查询构建器使用示例

```go
// 复杂查询示例
apps, err := NewQueryBuilder(db, &app.Application{}).
    Where("tenant_id", "=", tenantID).
    Where("status", "IN", []string{"running", "stopped"}).
    Where("created_at", ">=", time.Now().AddDate(0, -1, 0)).
    OrderBy("created_at", true).
    Limit(20).
    Offset(0).
    Find()

// 聚合查询示例
stats, err := NewQueryBuilder(db, &app.Application{}).
    Where("tenant_id", "=", tenantID).
    GroupBy("status").
    Select("status, COUNT(*) as count").
    Find()
```

## 4. 数据迁移和版本管理

### 4.1 迁移文件格式

```go
// Migration 迁移接口
type Migration interface {
    Version() string                    // 迁移版本
    Description() string                // 迁移描述
    Up(db *gorm.DB) error              // 升级操作
    Down(db *gorm.DB) error            // 降级操作
}

// 迁移文件示例: migrations/001_create_applications_table.go
type CreateApplicationsTable struct{}

func (m *CreateApplicationsTable) Version() string {
    return "001"
}

func (m *CreateApplicationsTable) Description() string {
    return "创建应用表"
}

func (m *CreateApplicationsTable) Up(db *gorm.DB) error {
    return db.AutoMigrate(&app.Application{})
}

func (m *CreateApplicationsTable) Down(db *gorm.DB) error {
    return db.Migrator().DropTable(&app.Application{})
}
```

### 4.2 迁移管理器

```go
// MigrationManager 迁移管理器
type MigrationManager struct {
    db         *gorm.DB
    migrations []Migration
}

// 执行迁移
func (m *MigrationManager) Migrate() error {
    // 创建迁移记录表
    if err := m.createMigrationTable(); err != nil {
        return err
    }
    
    // 执行未应用的迁移
    for _, migration := range m.migrations {
        if applied, err := m.isMigrationApplied(migration.Version()); err != nil {
            return err
        } else if !applied {
            if err := m.applyMigration(migration); err != nil {
                return err
            }
        }
    }
    
    return nil
}
```

## 5. 多数据库支持

### 5.1 数据库适配器

```go
// DatabaseAdapter 数据库适配器接口
type DatabaseAdapter interface {
    Connect(dsn string) (*gorm.DB, error)
    GetDialector(dsn string) gorm.Dialector
    GetDefaultPort() int
    GetDriverName() string
    SupportFeatures() []string
    
    // 数据库特定操作
    CreateDatabase(name string) error
    DropDatabase(name string) error
    ListDatabases() ([]string, error)
    
    // 备份恢复
    Backup(dbName, backupPath string) error
    Restore(dbName, backupPath string) error
}

// SQLiteAdapter SQLite 适配器
type SQLiteAdapter struct{}

func (a *SQLiteAdapter) Connect(dsn string) (*gorm.DB, error) {
    return gorm.Open(sqlite.Open(dsn), &gorm.Config{})
}

func (a *SQLiteAdapter) GetDialector(dsn string) gorm.Dialector {
    return sqlite.Open(dsn)
}

func (a *SQLiteAdapter) GetDefaultPort() int {
    return 0 // SQLite 不使用端口
}

func (a *SQLiteAdapter) GetDriverName() string {
    return "sqlite"
}

func (a *SQLiteAdapter) SupportFeatures() []string {
    return []string{"transactions", "foreign_keys", "json"}
}

// PostgreSQLAdapter PostgreSQL 适配器
type PostgreSQLAdapter struct{}

func (a *PostgreSQLAdapter) Connect(dsn string) (*gorm.DB, error) {
    return gorm.Open(postgres.Open(dsn), &gorm.Config{})
}

func (a *PostgreSQLAdapter) GetDialector(dsn string) gorm.Dialector {
    return postgres.Open(dsn)
}

func (a *PostgreSQLAdapter) GetDefaultPort() int {
    return 5432
}

func (a *PostgreSQLAdapter) GetDriverName() string {
    return "postgres"
}

func (a *PostgreSQLAdapter) SupportFeatures() []string {
    return []string{"transactions", "foreign_keys", "json", "arrays", "full_text_search"}
}
```

### 5.2 数据库工厂

```go
// DatabaseFactory 数据库工厂
type DatabaseFactory struct {
    adapters map[string]DatabaseAdapter
}

// NewDatabaseFactory 创建数据库工厂
func NewDatabaseFactory() *DatabaseFactory {
    factory := &DatabaseFactory{
        adapters: make(map[string]DatabaseAdapter),
    }
    
    // 注册适配器
    factory.RegisterAdapter("sqlite", &SQLiteAdapter{})
    factory.RegisterAdapter("postgres", &PostgreSQLAdapter{})
    
    return factory
}

// RegisterAdapter 注册数据库适配器
func (f *DatabaseFactory) RegisterAdapter(driver string, adapter DatabaseAdapter) {
    f.adapters[driver] = adapter
}

// CreateConnection 创建数据库连接
func (f *DatabaseFactory) CreateConnection(driver, dsn string) (*gorm.DB, error) {
    adapter, exists := f.adapters[driver]
    if !exists {
        return nil, fmt.Errorf("不支持的数据库驱动: %s", driver)
    }
    
    return adapter.Connect(dsn)
}
```

## 6. 数据备份和恢复

### 6.1 备份策略

#### 全量备份
```yaml
backup:
  type: full                    # 备份类型: full, incremental
  schedule: "0 2 * * *"         # 备份计划 (每天凌晨2点)
  retention: 30                 # 保留天数
  compression: gzip             # 压缩格式
  encryption: aes256            # 加密算法
  storage:
    type: local                 # 存储类型: local, s3, oss
    path: "/backup"             # 存储路径
```

#### 增量备份
```yaml
backup:
  type: incremental
  schedule: "0 */6 * * *"       # 每6小时一次
  base_backup: "2024-01-01"     # 基础备份日期
  retention: 7                  # 保留天数
```

### 6.2 备份实现

```go
// BackupService 备份服务
type BackupService struct {
    db      *gorm.DB
    adapter DatabaseAdapter
    config  BackupConfig
}

// BackupConfig 备份配置
type BackupConfig struct {
    Type        string `json:"type"`         // full, incremental
    Schedule    string `json:"schedule"`     // cron 表达式
    Retention   int    `json:"retention"`    // 保留天数
    Compression string `json:"compression"`  // 压缩格式
    Encryption  string `json:"encryption"`   // 加密算法
    Storage     StorageConfig `json:"storage"`
}

// StorageConfig 存储配置
type StorageConfig struct {
    Type      string            `json:"type"`       // local, s3, oss
    Path      string            `json:"path"`       // 存储路径
    Bucket    string            `json:"bucket"`     // 存储桶
    Region    string            `json:"region"`     // 区域
    AccessKey string            `json:"access_key"` // 访问密钥
    SecretKey string            `json:"secret_key"` // 密钥
    Endpoint  string            `json:"endpoint"`   // 端点
}

// CreateBackup 创建备份
func (s *BackupService) CreateBackup(ctx context.Context, name string) (*Backup, error) {
    backup := &Backup{
        ID:        uuid.New().String(),
        Name:      name,
        Type:      s.config.Type,
        Status:    BackupStatusRunning,
        StartTime: time.Now(),
    }
    
    // 执行备份
    if err := s.executeBackup(ctx, backup); err != nil {
        backup.Status = BackupStatusFailed
        backup.ErrorMsg = err.Error()
        return backup, err
    }
    
    backup.Status = BackupStatusSuccess
    backup.EndTime = &time.Now()
    
    return backup, nil
}
```

### 6.3 恢复机制

```go
// RestoreService 恢复服务
type RestoreService struct {
    db      *gorm.DB
    adapter DatabaseAdapter
}

// RestoreFromBackup 从备份恢复
func (s *RestoreService) RestoreFromBackup(ctx context.Context, backupID string) error {
    // 获取备份信息
    backup, err := s.getBackup(backupID)
    if err != nil {
        return err
    }
    
    // 验证备份完整性
    if err := s.validateBackup(backup); err != nil {
        return fmt.Errorf("备份验证失败: %w", err)
    }
    
    // 执行恢复
    if err := s.executeRestore(ctx, backup); err != nil {
        return fmt.Errorf("恢复执行失败: %w", err)
    }
    
    return nil
}
```

## 7. 数据模型设计

### 7.1 基础实体

```go
// BaseModel 基础模型
type BaseModel struct {
    ID        string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
    CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
    DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index"`
}

// TenantModel 租户模型
type TenantModel struct {
    BaseModel
    TenantID string `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
}

// UserModel 用户模型
type UserModel struct {
    TenantModel
    UserID string `json:"user_id" gorm:"type:varchar(36);not null;index"`
}
```

### 7.2 核心业务表

#### 租户表
```sql
CREATE TABLE tenants (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    settings JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_deleted_at ON tenants(deleted_at);
```

#### 用户表
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    tenant_id VARCHAR(36) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    last_login_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

CREATE UNIQUE INDEX idx_users_username_tenant ON users(username, tenant_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_users_email_tenant ON users(email, tenant_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_status ON users(status);
```

#### 角色权限表
```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

CREATE TABLE user_roles (
    user_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

## 8. 数据库性能优化

### 8.1 索引策略

#### 主键索引
- 所有表使用 UUID 作为主键
- 使用 B-tree 索引提高查询性能

#### 外键索引
- 所有外键字段创建索引
- 复合外键使用复合索引

#### 查询索引
- 根据查询模式创建合适的索引
- 使用部分索引优化特定查询

#### 全文搜索索引
```sql
-- PostgreSQL 全文搜索索引
CREATE INDEX idx_applications_search ON applications 
USING gin(to_tsvector('simple', name || ' ' || description));

-- 搜索查询
SELECT * FROM applications 
WHERE to_tsvector('simple', name || ' ' || description) @@ plainto_tsquery('simple', '搜索关键词');
```

### 8.2 查询优化

#### 分页优化
```go
// 使用游标分页替代 OFFSET
type CursorPagination struct {
    Cursor string `json:"cursor"`
    Limit  int    `json:"limit"`
}

// 游标分页查询
func (r *ApplicationRepository) ListWithCursor(ctx context.Context, cursor string, limit int) ([]*Application, string, error) {
    query := r.db.Model(&Application{})
    
    if cursor != "" {
        query = query.Where("id > ?", cursor)
    }
    
    var apps []*Application
    err := query.Order("id ASC").Limit(limit + 1).Find(&apps).Error
    if err != nil {
        return nil, "", err
    }
    
    var nextCursor string
    if len(apps) > limit {
        nextCursor = apps[limit].ID
        apps = apps[:limit]
    }
    
    return apps, nextCursor, nil
}
```

#### 预加载优化
```go
// 避免 N+1 查询问题
apps, err := db.Preload("Instances", func(db *gorm.DB) *gorm.DB {
    return db.Where("status = ?", "running")
}).Preload("Builds", func(db *gorm.DB) *gorm.DB {
    return db.Order("start_time DESC").Limit(5)
}).Find(&apps).Error
```

### 8.3 缓存策略

#### Redis 缓存
```go
// CacheService 缓存服务
type CacheService struct {
    redis  *redis.Client
    prefix string
    ttl    time.Duration
}

// 缓存应用信息
func (s *CacheService) CacheApplication(app *Application) error {
    key := fmt.Sprintf("%s:app:%s", s.prefix, app.ID)
    data, err := json.Marshal(app)
    if err != nil {
        return err
    }
    
    return s.redis.Set(context.Background(), key, data, s.ttl).Err()
}

// 获取缓存的应用信息
func (s *CacheService) GetCachedApplication(appID string) (*Application, error) {
    key := fmt.Sprintf("%s:app:%s", s.prefix, appID)
    data, err := s.redis.Get(context.Background(), key).Result()
    if err != nil {
        return nil, err
    }
    
    var app Application
    if err := json.Unmarshal([]byte(data), &app); err != nil {
        return nil, err
    }
    
    return &app, nil
}
```

## 9. 数据一致性保证

### 9.1 事务管理

```go
// TransactionManager 事务管理器
type TransactionManager struct {
    db *gorm.DB
}

// WithTransaction 在事务中执行操作
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(*gorm.DB) error) error {
    return tm.db.Transaction(func(tx *gorm.DB) error {
        return fn(tx)
    })
}

// 使用示例
err := tm.WithTransaction(ctx, func(tx *gorm.DB) error {
    // 创建应用
    if err := tx.Create(&app).Error; err != nil {
        return err
    }
    
    // 创建默认流水线
    if err := tx.Create(&pipeline).Error; err != nil {
        return err
    }
    
    // 创建默认环境
    if err := tx.Create(&environment).Error; err != nil {
        return err
    }
    
    return nil
})
```

### 9.2 数据验证

```go
// Validator 数据验证器
type Validator struct {
    validate *validator.Validate
}

// ValidateStruct 验证结构体
func (v *Validator) ValidateStruct(s interface{}) error {
    return v.validate.Struct(s)
}

// 自定义验证规则
func (v *Validator) RegisterValidation(tag string, fn validator.Func) error {
    return v.validate.RegisterValidation(tag, fn)
}
```

## 10. 监控和诊断

### 10.1 数据库监控指标

- 连接池使用率
- 查询响应时间
- 慢查询统计
- 锁等待时间
- 死锁检测

### 10.2 性能诊断

```go
// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
    db     *gorm.DB
    logger Logger
}

// 监控慢查询
func (pm *PerformanceMonitor) MonitorSlowQueries(threshold time.Duration) {
    // 配置 GORM 慢查询日志
    pm.db.Logger = pm.db.Logger.LogMode(logger.Info)
    
    // 自定义慢查询处理
    pm.db.Callback().Query().Before("gorm:query").Register("monitor:before_query", func(db *gorm.DB) {
        db.Set("start_time", time.Now())
    })
    
    pm.db.Callback().Query().After("gorm:query").Register("monitor:after_query", func(db *gorm.DB) {
        if startTime, ok := db.Get("start_time"); ok {
            duration := time.Since(startTime.(time.Time))
            if duration > threshold {
                pm.logger.Warn("检测到慢查询",
                    "sql", db.Statement.SQL.String(),
                    "duration", duration.String(),
                    "vars", db.Statement.Vars,
                )
            }
        }
    })
}
```
