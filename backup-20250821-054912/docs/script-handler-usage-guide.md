# 脚本执行 Handler 使用指南

## 概述

`internal/script/handler.go` 是 PaaS 平台的脚本执行 HTTP 处理器，提供完整的脚本执行、模板管理、任务调度和统计功能。

## 功能特性

### 1. 脚本执行管理
- ✅ 异步脚本执行任务提交
- ✅ 任务状态查询和监控
- ✅ 任务结果获取
- ✅ 执行日志查看
- ✅ 任务取消操作
- ✅ 产物下载功能

### 2. 脚本模板管理
- ✅ 创建和管理脚本模板
- ✅ 模板版本控制
- ✅ 模板分类和标签
- ✅ 模板权限控制

### 3. 任务调度功能
- ✅ 基于 Cron 表达式的定时调度
- ✅ 手动触发调度
- ✅ 调度状态管理
- ✅ 调度历史记录

### 4. 统计和监控
- ✅ 任务执行统计概览
- ✅ 性能指标监控
- ✅ 资源使用情况分析

## API 接口

### 脚本执行相关

#### 执行脚本
```http
POST /api/v1/scripts/execute
Content-Type: application/json

{
  "app_id": "app-123",
  "script_path": "main.py",
  "runtime_type": "python",
  "parameters": {
    "input_file": "data.csv",
    "output_format": "json"
  },
  "environment": {
    "PYTHONPATH": "/app/lib"
  },
  "timeout": 300,
  "priority": 5
}
```

#### 获取任务状态
```http
GET /api/v1/scripts/tasks/{task_id}
```

#### 获取任务结果
```http
GET /api/v1/scripts/tasks/{task_id}/result
```

#### 获取任务日志
```http
GET /api/v1/scripts/tasks/{task_id}/logs
```

#### 取消任务
```http
DELETE /api/v1/scripts/tasks/{task_id}
```

#### 下载产物
```http
GET /api/v1/scripts/tasks/{task_id}/artifacts/{artifact_name}
```

### 模板管理相关

#### 创建模板
```http
POST /api/v1/script-templates
Content-Type: application/json

{
  "name": "数据处理模板",
  "description": "用于数据清洗和转换的Python脚本模板",
  "category": "data-processing",
  "language": "python",
  "version": "1.0.0",
  "content": "import pandas as pd\n...",
  "parameters": {
    "input_file": {
      "type": "string",
      "required": true
    }
  },
  "tags": ["data", "etl", "pandas"]
}
```

#### 获取模板列表
```http
GET /api/v1/script-templates?category=data-processing&page=1&page_size=20
```

#### 获取模板详情
```http
GET /api/v1/script-templates/{template_id}
```

#### 更新模板
```http
PUT /api/v1/script-templates/{template_id}
Content-Type: application/json

{
  "name": "数据处理模板v2",
  "description": "更新的数据处理模板",
  "content": "import pandas as pd\n...",
  "status": "active"
}
```

#### 删除模板
```http
DELETE /api/v1/script-templates/{template_id}
```

### 任务调度相关

#### 创建调度
```http
POST /api/v1/script-schedules
Content-Type: application/json

{
  "name": "每日数据处理",
  "description": "每天凌晨执行数据处理任务",
  "app_id": "app-123",
  "script_path": "daily_process.py",
  "parameters": {
    "date": "{{.Date}}"
  },
  "cron_expr": "0 0 * * *",
  "timezone": "Asia/Shanghai"
}
```

#### 获取调度列表
```http
GET /api/v1/script-schedules?app_id=app-123&status=active
```

#### 手动触发调度
```http
POST /api/v1/script-schedules/{schedule_id}/trigger
Content-Type: application/json

{
  "parameters": {
    "force": true
  },
  "environment": {
    "MANUAL_RUN": "true"
  }
}
```

### 统计监控相关

#### 获取统计概览
```http
GET /api/v1/script-stats/overview
```

#### 获取指标数据
```http
GET /api/v1/script-stats/metrics?time_range=24h&interval=1h
```

## 集成使用

### 1. 服务初始化

```go
package main

import (
    "paas-platform/internal/script"
    "paas-platform/pkg/logger"
    "github.com/gin-gonic/gin"
)

func main() {
    // 初始化日志
    logger := logger.NewLogger()
    
    // 初始化数据库
    db, err := database.NewConnection()
    if err != nil {
        log.Fatalf("数据库连接失败: %v", err)
    }
    
    // 初始化脚本执行服务
    scriptService := script.NewScriptExecutionService(db, containerManager, runtimeManager, logger, serviceConfig)
    scriptHandler := script.NewHandler(scriptService, logger)
    
    // 设置路由
    router := gin.New()
    v1 := router.Group("/api/v1")
    
    // 注册脚本执行路由
    scriptHandler.RegisterRoutes(v1)
    
    // 启动服务
    router.Run(":8080")
}
```

### 2. 中间件要求

Handler 依赖以下中间件设置的上下文变量：
- `user_id`: 当前用户ID
- `tenant_id`: 当前租户ID

```go
// 认证中间件示例
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从JWT或其他方式获取用户信息
        userID := getUserIDFromToken(c)
        tenantID := getTenantIDFromToken(c)
        
        c.Set("user_id", userID)
        c.Set("tenant_id", tenantID)
        
        c.Next()
    }
}
```

## 权限控制

所有接口都实现了基于租户的权限控制：
- 用户只能访问自己租户的资源
- 跨租户访问会返回 403 Forbidden 错误
- 所有操作都会记录审计日志

## 错误处理

统一的错误响应格式：
```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "details": "详细错误信息"
}
```

常见错误码：
- `INVALID_REQUEST`: 请求参数错误
- `TASK_NOT_FOUND`: 任务不存在
- `ACCESS_DENIED`: 权限不足
- `SUBMIT_TASK_FAILED`: 提交任务失败

## 性能优化

1. **分页查询**: 所有列表接口都支持分页，默认每页20条记录
2. **索引优化**: 数据库表已添加必要的索引
3. **缓存机制**: 可以在服务层添加缓存来提高查询性能
4. **异步处理**: 脚本执行采用异步模式，不会阻塞HTTP请求

## 监控和日志

1. **结构化日志**: 所有操作都会记录详细的结构化日志
2. **指标收集**: 提供任务执行统计和性能指标
3. **健康检查**: 可以通过统计接口监控服务健康状态

## 扩展性

Handler 设计支持以下扩展：
1. **新的运行时**: 通过 RuntimeManager 添加新的脚本运行时
2. **存储后端**: 支持不同的产物存储后端
3. **调度引擎**: 可以集成更复杂的调度引擎
4. **通知机制**: 支持任务完成后的多种通知方式
