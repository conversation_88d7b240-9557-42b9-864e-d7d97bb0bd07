# API 版本控制系统

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的 API 版本控制系统。该系统提供了完整的 API 版本管理能力，包括版本检测、路由策略、向后兼容性处理、版本生命周期管理等功能，确保 API 的平滑演进和向后兼容。

## 🏗️ 系统架构

```
客户端请求 → 版本检测 → 版本路由 → 兼容性处理 → 目标服务
     ↓          ↓         ↓          ↓
[版本管理器] [路由器] [策略引擎] [转换器]
```

### 核心组件

1. **VersionManager** (`pkg/version/version_manager.go`)
   - API 版本注册和管理
   - 版本检测和验证
   - 版本生命周期管理

2. **VersionRouter** (`pkg/version/router.go`)
   - 版本路由策略
   - 兼容性处理
   - 请求转发

3. **VersionMiddleware** (`pkg/middleware/version.go`)
   - Gin 中间件集成
   - 版本控制流程
   - 错误处理

4. **ConfigLoader** (`pkg/version/config_loader.go`)
   - 版本配置加载
   - 配置验证
   - 动态配置更新

## ✅ 功能特性

### 1. 版本检测功能
- ✅ **多种检测方式**：请求头、路径、查询参数、子域名、Accept头
- ✅ **智能检测**：置信度评分和优先级排序
- ✅ **版本标准化**：自动格式化和验证
- ✅ **降级策略**：检测失败时的默认版本处理

### 2. 版本管理功能
- ✅ **版本注册**：支持多种版本格式（语义化、主版本、日期）
- ✅ **生命周期管理**：草稿、测试、稳定、废弃、下线状态
- ✅ **元数据管理**：版本描述、文档链接、维护者信息
- ✅ **服务映射**：版本到后端服务的映射关系

### 3. 路由策略
- ✅ **多种路由策略**：请求头路由、路径路由、查询参数路由
- ✅ **版本映射**：版本别名和重定向
- ✅ **降级策略**：最新版本、默认版本、兼容版本、错误返回
- ✅ **路径匹配**：支持通配符和参数匹配

### 4. 兼容性处理
- ✅ **向后兼容**：旧版本请求自动转换到新版本
- ✅ **向前兼容**：新版本功能在旧版本中的降级处理
- ✅ **数据转换**：字段重命名、默认值设置、值映射
- ✅ **桥接兼容**：跨版本的复杂兼容性处理

### 5. 策略管理
- ✅ **版本策略**：基于条件的版本处理规则
- ✅ **访问控制**：Beta版本访问限制
- ✅ **废弃警告**：自动添加废弃版本警告
- ✅ **迁移策略**：版本迁移的自动化处理

## 🔧 配置说明

### 1. 版本配置文件

版本配置使用 JSON 格式，存放在 `configs/versions/api-versions.json`：

```json
{
  "versions": [
    {
      "version": "v1",
      "name": "API Version 1.0",
      "description": "第一个稳定版本的API",
      "status": "stable",
      "is_default": true,
      "is_stable": true,
      "release_date": "2023-01-01T00:00:00Z",
      "base_url": "http://localhost:8080",
      "service_map": {
        "users": "user-service-v1",
        "apps": "app-service-v1"
      },
      "metadata": {
        "maintainer": "API Team",
        "documentation": "https://docs.example.com/api/v1"
      }
    }
  ],
  "routes": [
    {
      "path": "/api/*/users",
      "method": "GET",
      "versions": ["v1", "v2"],
      "default_version": "v2",
      "strategy": "header",
      "fallback_policy": "compatible"
    }
  ],
  "config": {
    "detection_methods": ["header", "path", "query"],
    "default_version": "v1",
    "version_format": "major"
  }
}
```

### 2. 版本中间件配置

```go
// 在 cmd/api-gateway/main.go 中
func setupVersionMiddleware(logger logger.Logger) *middleware.VersionMiddleware {
    // 创建版本管理器
    versionConfig := version.DefaultVersionConfig()
    versionManager := version.NewVersionManager(versionConfig, logger)
    
    // 加载配置
    configLoader := version.NewConfigLoader("configs/versions", logger)
    configLoader.LoadAndApplyConfig(versionManager, "api-versions.json")
    
    // 创建中间件
    middlewareConfig := middleware.DefaultVersionMiddlewareConfig()
    return middleware.NewVersionMiddleware(versionManager, versionRouter, logger, middlewareConfig)
}
```

## 🚀 使用示例

### 1. 版本检测示例

```bash
# 通过请求头指定版本
curl -H "API-Version: v2" http://localhost:8080/api/users

# 通过路径指定版本
curl http://localhost:8080/api/v1/users

# 通过查询参数指定版本
curl http://localhost:8080/api/users?version=v2

# 通过子域名指定版本
curl http://v2.api.localhost:8080/users

# 通过Accept头指定版本
curl -H "Accept: application/json;version=v2" http://localhost:8080/api/users
```

### 2. 版本响应头

```http
HTTP/1.1 200 OK
X-API-Version: v2
X-Version-Strategy: header
X-Version-Applied: true
X-API-Deprecated: false
Content-Type: application/json

{
  "data": [...],
  "version": "v2"
}
```

### 3. 兼容性处理示例

```json
// v0.9 请求 (旧版本)
{
  "user_id": "123",
  "name": "张三"
}

// 自动转换为 v1 格式
{
  "id": "123",
  "name": "张三",
  "created_at": "2023-01-01T00:00:00Z"
}
```

### 4. 版本迁移示例

```json
// 兼容性规则配置
{
  "compatibility_rules": [
    {
      "from_version": "v1",
      "to_version": "v2",
      "type": "forward",
      "transforms": [
        {
          "type": "add",
          "target": "deployment_config",
          "default_value": {
            "strategy": "rolling",
            "replicas": 1
          }
        },
        {
          "type": "mapping",
          "source": "language",
          "target": "runtime",
          "mapping": {
            "nodejs": "node:18",
            "python": "python:3.9"
          }
        }
      ]
    }
  ]
}
```

## 📊 支持的版本格式

### 版本格式类型

| 格式类型 | 描述 | 示例 | 正则表达式 |
|---------|------|------|-----------|
| `major` | 主版本号 | `v1`, `v2` | `^v?(\d+)$` |
| `semantic` | 语义化版本 | `v1.2.3`, `v2.0.0-beta.1` | `^v?(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\-\.]+))?` |
| `date` | 日期版本 | `2023-01-01`, `20230101` | `^(\d{4})-?(0[1-9]\|1[0-2])-?(0[1-9]\|[12]\d\|3[01])$` |
| `custom` | 自定义格式 | 任意格式 | 无限制 |

### 版本状态

| 状态 | 描述 | 用途 |
|------|------|------|
| `draft` | 草稿 | 开发中的版本 |
| `beta` | 测试版 | 公开测试的版本 |
| `stable` | 稳定版 | 生产环境使用的版本 |
| `deprecated` | 已废弃 | 不推荐使用但仍可用 |
| `sunset` | 已下线 | 不再提供服务 |

## 🔍 版本检测方法

### 检测优先级

1. **请求头检测** (`DetectionMethodHeader`)
   - `API-Version`
   - `X-API-Version`
   - `Version`
   - `X-Version`

2. **路径检测** (`DetectionMethodPath`)
   - `/api/v1/users`
   - `/v2/apps`

3. **查询参数检测** (`DetectionMethodQuery`)
   - `?version=v2`
   - `?api_version=v1`
   - `?v=2`

4. **子域名检测** (`DetectionMethodSubdomain`)
   - `v1.api.example.com`
   - `api-v2.example.com`

5. **Accept头检测** (`DetectionMethodAccept`)
   - `Accept: application/vnd.api+json;version=1`
   - `Accept: application/json;version=v2`

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/version -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试版本检测
curl -H "API-Version: v2" http://localhost:8080/api/v1/users
# 响应头: X-API-Version: v2

# 测试兼容性处理
curl -H "API-Version: v0.9" http://localhost:8080/api/users
# 自动转换到 v1 版本

# 测试废弃版本警告
curl -H "API-Version: v0.9" http://localhost:8080/api/users
# 响应头: X-API-Deprecated: true
```

## 🔍 监控和调试

### 版本使用统计
```bash
# 查看版本统计
curl http://localhost:8080/api/v1/version/stats

# 查看特定版本统计
curl http://localhost:8080/api/v1/version/stats/v2
```

### 版本健康检查
```bash
# 检查版本健康状态
curl http://localhost:8080/api/v1/version/health

# 检查特定版本健康
curl http://localhost:8080/api/v1/version/health/v2
```

### 日志监控
```bash
# 查看版本控制日志
tail -f logs/api-gateway.log | grep "版本"

# 查看版本检测详情
tail -f logs/api-gateway.log | grep "version"
```

## 🛠️ 故障排除

### 常见问题

1. **版本检测失败**
   - 检查版本格式是否正确
   - 确认版本是否已注册
   - 查看检测方法配置

2. **兼容性转换不生效**
   - 检查兼容性规则配置
   - 确认转换规则语法
   - 查看转换日志

3. **路由到错误版本**
   - 检查路由配置
   - 确认版本映射关系
   - 验证降级策略

### 调试技巧

1. **启用详细日志**：
   ```yaml
   log:
     level: debug
   ```

2. **检查版本配置**：
   ```bash
   # 验证配置文件语法
   cat configs/versions/api-versions.json | jq .
   ```

3. **测试版本检测**：
   ```bash
   # 使用不同方法测试版本检测
   curl -v -H "API-Version: v2" http://localhost:8080/api/users
   ```

## 📈 性能优化

1. **版本缓存**：版本信息会被缓存在内存中
2. **路由缓存**：路由结果支持可选缓存
3. **配置热更新**：支持配置文件的热更新
4. **批量处理**：支持批量版本操作

## 🔒 安全考虑

1. **版本访问控制**：Beta版本的访问权限控制
2. **版本验证**：严格的版本格式验证
3. **审计日志**：记录所有版本操作
4. **降级保护**：防止恶意版本请求

## 🚀 扩展功能

### 计划中的功能
- [ ] **版本分析**：版本使用情况分析和报告
- [ ] **自动迁移**：基于使用情况的自动版本迁移
- [ ] **A/B测试**：版本间的A/B测试支持
- [ ] **版本回滚**：快速版本回滚机制
- [ ] **多租户版本**：租户级别的版本管理

### 版本演进策略

1. **渐进式发布**：新版本的渐进式发布
2. **金丝雀部署**：小流量验证新版本
3. **蓝绿部署**：版本间的无缝切换
4. **特性开关**：基于版本的特性控制

---

**注意**：API 版本控制系统是现代 API 管理的核心组件，确保了 API 的平滑演进和向后兼容性。在生产环境中使用时，请仔细规划版本策略，确保不会影响现有客户端。
