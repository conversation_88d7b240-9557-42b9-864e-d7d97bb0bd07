# API 参数校验和验证系统

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的参数校验和验证系统。该系统提供了完整的 API 请求验证功能，包括参数类型检查、业务规则验证、JSON Schema 验证等。

## 🏗️ 系统架构

```
API 请求 → 验证中间件 → API 验证器 → 业务逻辑
           ↓
    [验证规则配置] + [JSON Schema]
```

### 核心组件

1. **APIValidator** (`pkg/validator/validator.go`)
   - 核心验证引擎
   - 支持多种验证规则
   - 可扩展的验证器架构

2. **ValidationMiddleware** (`pkg/middleware/validation.go`)
   - Gin 中间件集成
   - 请求拦截和验证
   - 错误响应处理

3. **RuleLoader** (`pkg/validator/loader.go`)
   - 验证规则加载器
   - 支持 YAML 配置文件
   - 动态规则管理

## ✅ 功能特性

### 1. 请求头验证
- 必需头部检查
- 头部值验证
- 内容类型验证

```yaml
headers:
  Content-Type: "required"
  Authorization: "required"
```

### 2. 查询参数验证
- 类型验证（string, int, float, bool）
- 范围验证（min, max）
- 长度验证（minLength, maxLength）
- 正则表达式验证
- 枚举值验证

```yaml
query_params:
  page:
    type: "int"
    required: false
    min: 1
    max: 1000
    default: 1
  status:
    type: "string"
    enum: ["active", "inactive", "pending"]
```

### 3. 路径参数验证
- UUID 格式验证
- 自定义正则表达式
- 必需参数检查

```yaml
path_params:
  id:
    type: "string"
    required: true
    pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
```

### 4. 请求体验证
- JSON Schema 验证
- 内容类型检查
- 大小限制
- 必需字段验证

```yaml
body:
  content_type: "application/json"
  required: true
  max_size: 10240
  schema: "create_app_request"
```

### 5. 业务规则验证
- 租户访问权限检查
- 资源所有者验证
- 角色权限验证
- 自定义业务逻辑

```yaml
business:
  - name: "tenant_access"
    description: "租户访问权限检查"
    message: "无权限访问该租户资源"
```

## 🔧 配置说明

### 1. 主配置文件 (`configs/api-gateway.yaml`)

```yaml
# API 参数验证配置
validation:
  enabled: true                       # 启用验证
  rules_file: "configs/validation-rules.yaml"  # 规则文件路径
  strict_mode: false                  # 严格模式
  log_validation: true                # 记录验证日志
  cache_results: false                # 缓存验证结果
```

### 2. 验证规则文件 (`configs/validation-rules.yaml`)

```yaml
# 验证规则定义
rules:
  - path: "/api/v1/apps"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "required"
    body:
      content_type: "application/json"
      required: true
      schema: "create_app_request"

# JSON Schema 定义
schemas:
  create_app_request:
    description: "创建应用请求"
    schema:
      type: "object"
      required: ["name", "language"]
      properties:
        name:
          type: "string"
          minLength: 1
          maxLength: 100
```

## 🚀 使用示例

### 1. 基本验证

```go
// 创建验证器
validator := validator.NewAPIValidator(logger)

// 注册验证规则
rule := &validator.ValidationRule{
    Path:   "/api/v1/users",
    Method: "POST",
    Headers: map[string]string{
        "Content-Type": "required",
    },
    Body: &validator.BodyRule{
        ContentType: "application/json",
        Required:    true,
        Schema:      "user_request",
    },
}
validator.RegisterRule(rule)

// 在 Gin 中使用
router.Use(validationMiddleware.Handler())
```

### 2. 自定义验证器

```go
// 注册自定义验证器
validator.RegisterValidation("phone", func(fl validator.FieldLevel) bool {
    phone := fl.Field().String()
    matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, phone)
    return matched
})
```

### 3. 业务规则验证

```go
// 自定义业务规则
func (av *APIValidator) evaluateBusinessRule(c *gin.Context, rule BusinessRule) bool {
    switch rule.Name {
    case "tenant_access":
        userTenantID := c.GetString("tenant_id")
        pathTenantID := c.Param("tenant_id")
        return userTenantID == pathTenantID
    }
    return true
}
```

## 📊 验证响应格式

### 成功响应
验证通过时，请求继续处理，无额外响应。

### 失败响应
```json
{
  "code": "VALIDATION_FAILED",
  "message": "请求参数验证失败",
  "details": [
    {
      "field": "query.page",
      "value": "0",
      "tag": "min",
      "message": "query参数 page 不能小于 1"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req-123456"
}
```

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/validator -v
```

### 测试覆盖率
```bash
go test ./pkg/validator -cover
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试验证功能
curl -X POST http://localhost:8080/api/v1/apps \
  -H "Content-Type: application/json" \
  -d '{"name": "test-app", "language": "nodejs"}'
```

## 🔍 监控和调试

### 验证日志
```
[INFO] API参数验证通过 | method=POST path=/api/v1/apps client_ip=127.0.0.1
[WARN] API参数验证失败 | method=POST path=/api/v1/apps errors=2 message=请求参数验证失败
```

### 验证统计
```go
// 获取验证统计信息
stats := validationMiddleware.GetValidationStats()
```

## 🛠️ 扩展和自定义

### 1. 添加新的验证规则
1. 在 `validation-rules.yaml` 中定义规则
2. 重启服务或动态重载配置

### 2. 自定义验证器
```go
// 实现自定义验证逻辑
func customValidator(fl validator.FieldLevel) bool {
    // 自定义验证逻辑
    return true
}

// 注册验证器
validator.RegisterValidation("custom", customValidator)
```

### 3. 扩展业务规则
在 `evaluateBusinessRule` 方法中添加新的业务规则处理逻辑。

## 📈 性能优化

1. **缓存验证结果**：启用 `cache_results` 配置
2. **跳过路径**：配置 `skip_paths` 跳过不需要验证的路径
3. **异步验证**：对于复杂验证，考虑异步处理

## 🔒 安全考虑

1. **输入验证**：所有用户输入都经过严格验证
2. **错误信息**：生产环境建议启用 `strict_mode: false`
3. **日志记录**：记录验证失败的详细信息用于安全审计

## 📚 相关文档

- [API 网关配置指南](./api-gateway-config.md)
- [中间件开发指南](./middleware-development.md)
- [JSON Schema 规范](https://json-schema.org/)

---

**注意**：本验证系统是 PaaS 平台 API 网关功能完善的重要组成部分，为系统安全性和数据完整性提供了强有力的保障。
