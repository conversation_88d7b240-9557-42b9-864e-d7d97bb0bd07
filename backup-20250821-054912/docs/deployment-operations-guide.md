# PaaS平台部署架构运维指南

## 📋 概述

本文档提供了PaaS平台两种部署架构（FaaS和SaaS模式）的详细运维指南，包括部署、监控、故障排除和性能优化等内容。

## 🏗️ 架构概览

### FaaS模式（按需执行）
- **特点**: 动态容器创建，执行完成后销毁
- **适用场景**: 短时间任务、事件驱动、无状态函数
- **优势**: 资源效率高、成本低、自动扩缩容
- **劣势**: 冷启动延迟、不适合长时间任务

### SaaS模式（常驻服务）
- **特点**: 预先部署，持续运行，请求转发
- **适用场景**: Web应用、数据库服务、实时通信
- **优势**: 响应速度快、状态保持、适合长时间任务
- **劣势**: 资源占用、成本较高、扩缩容复杂

## 🚀 部署指南

### 1. 环境准备

#### 系统要求
```bash
# 操作系统
Ubuntu 20.04+ / CentOS 8+ / RHEL 8+

# 硬件要求
CPU: 4核心以上
内存: 8GB以上
磁盘: 100GB以上 SSD
网络: 1Gbps以上

# 软件依赖
Docker: 20.10+
Docker Compose: 2.0+
Kubernetes: 1.24+ (可选)
```

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 验证安装
docker --version
docker-compose --version
```

### 2. FaaS模式部署

#### 快速部署
```bash
# 克隆项目
git clone <repository-url>
cd paas-platform

# 部署FaaS模式
cd deployments/faas
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f faas-executor
```

#### 配置说明
```yaml
# deployments/faas/.env
FAAS_PORT=8087
FAAS_MAX_CONCURRENT_EXECUTIONS=100
FAAS_DEFAULT_TIMEOUT=30s
FAAS_CONTAINER_POOL_SIZE=10
FAAS_PREWARM_CONTAINERS=3

# 数据库配置
POSTGRES_DB=paas_faas
POSTGRES_USER=paas_user
POSTGRES_PASSWORD=paas_password

# Redis配置
REDIS_PASSWORD=paas_redis_password
```

#### 健康检查
```bash
# 检查FaaS执行器健康状态
curl http://localhost:8087/api/v1/faas/functions/health

# 检查容器池状态
curl http://localhost:8087/api/v1/faas/functions/pool/stats

# 检查执行器指标
curl http://localhost:8087/api/v1/faas/functions/metrics
```

### 3. SaaS模式部署

#### 快速部署
```bash
# 部署SaaS模式
cd deployments/saas
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f saas-manager
```

#### 配置说明
```yaml
# deployments/saas/.env
SAAS_PORT=8088
SAAS_DEFAULT_REPLICAS=2
SAAS_MAX_REPLICAS=10
SAAS_AUTO_SCALING_ENABLED=true
SAAS_SCALE_UP_THRESHOLD=80.0
SAAS_SCALE_DOWN_THRESHOLD=20.0

# 负载均衡配置
LB_PORT=8086
LB_ALGORITHM=round_robin
LB_HEALTH_CHECK_INTERVAL=30s
```

#### 健康检查
```bash
# 检查SaaS管理器健康状态
curl http://localhost:8088/api/v1/saas/services/health

# 检查负载均衡器状态
curl http://localhost:8086/health

# 检查服务注册中心状态
curl http://localhost:8089/health
```

### 4. Kubernetes部署

#### 部署FaaS到K8s
```bash
# 创建命名空间和资源
kubectl apply -f deployments/k8s/faas-deployment.yaml

# 检查部署状态
kubectl get pods -n paas-faas
kubectl get services -n paas-faas
kubectl get ingress -n paas-faas

# 查看日志
kubectl logs -f deployment/faas-executor -n paas-faas
```

#### 扩缩容管理
```bash
# 手动扩缩容
kubectl scale deployment faas-executor --replicas=5 -n paas-faas

# 查看HPA状态
kubectl get hpa -n paas-faas
kubectl describe hpa faas-executor-hpa -n paas-faas
```

## 📊 监控和告警

### 1. 监控指标

#### FaaS模式关键指标
```yaml
# 执行器指标
- faas_total_executions: 总执行次数
- faas_successful_executions: 成功执行次数
- faas_failed_executions: 失败执行次数
- faas_active_executions: 活跃执行数
- faas_average_execution_time: 平均执行时间
- faas_container_pool_usage: 容器池使用率

# 系统指标
- cpu_usage_percent: CPU使用率
- memory_usage_bytes: 内存使用量
- disk_usage_percent: 磁盘使用率
- network_io_bytes: 网络IO
```

#### SaaS模式关键指标
```yaml
# 服务指标
- saas_total_services: 总服务数
- saas_running_services: 运行中服务数
- saas_total_instances: 总实例数
- saas_healthy_instances: 健康实例数
- saas_request_count: 请求总数
- saas_error_count: 错误总数
- saas_average_latency: 平均延迟

# 负载均衡指标
- lb_request_count: 负载均衡请求数
- lb_backend_status: 后端状态
- lb_response_time: 响应时间
```

### 2. Prometheus配置

#### FaaS监控配置
```yaml
# deployments/faas/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'faas-executor'
    static_configs:
      - targets: ['faas-executor:8087']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'docker'
    static_configs:
      - targets: ['localhost:9323']

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 告警规则
```yaml
# alert_rules.yml
groups:
- name: faas_alerts
  rules:
  - alert: FaaSHighErrorRate
    expr: rate(faas_failed_executions[5m]) / rate(faas_total_executions[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "FaaS执行错误率过高"
      description: "FaaS执行错误率超过10%，当前值: {{ $value }}"

  - alert: FaaSHighConcurrency
    expr: faas_active_executions > 80
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "FaaS并发执行数过高"
      description: "FaaS活跃执行数超过80，当前值: {{ $value }}"

  - alert: ContainerPoolLow
    expr: faas_container_pool_usage < 0.2
    for: 5m
    labels:
      severity: info
    annotations:
      summary: "容器池使用率过低"
      description: "容器池使用率低于20%，可能需要调整池大小"
```

### 3. Grafana仪表板

#### FaaS仪表板配置
```json
{
  "dashboard": {
    "title": "FaaS执行器监控",
    "panels": [
      {
        "title": "执行统计",
        "type": "stat",
        "targets": [
          {
            "expr": "faas_total_executions",
            "legendFormat": "总执行次数"
          }
        ]
      },
      {
        "title": "执行时间趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(faas_execution_duration_seconds_sum[5m]) / rate(faas_execution_duration_seconds_count[5m])",
            "legendFormat": "平均执行时间"
          }
        ]
      },
      {
        "title": "容器池状态",
        "type": "gauge",
        "targets": [
          {
            "expr": "faas_container_pool_usage",
            "legendFormat": "容器池使用率"
          }
        ]
      }
    ]
  }
}
```

## 🔧 故障排除

### 1. 常见问题

#### FaaS模式问题

**问题**: 函数执行超时
```bash
# 检查执行器日志
docker logs paas-faas-executor

# 检查容器创建情况
docker ps -a | grep faas

# 调整超时配置
# 在docker-compose.yml中修改
FAAS_DEFAULT_TIMEOUT=60s
FAAS_MAX_EXECUTION_TIME=10m
```

**问题**: 容器池耗尽
```bash
# 检查容器池状态
curl http://localhost:8087/api/v1/faas/functions/pool/stats

# 增加容器池大小
FAAS_CONTAINER_POOL_SIZE=20
FAAS_PREWARM_CONTAINERS=5

# 重启服务
docker-compose restart faas-executor
```

**问题**: 内存不足
```bash
# 检查系统内存
free -h
docker stats

# 调整资源限制
FAAS_MEMORY_LIMIT=256m
FAAS_CPU_LIMIT=0.25

# 清理无用容器
docker system prune -f
```

#### SaaS模式问题

**问题**: 服务实例启动失败
```bash
# 检查服务管理器日志
docker logs paas-saas-manager

# 检查服务状态
curl http://localhost:8088/api/v1/saas/services

# 检查Docker资源
docker system df
docker system events
```

**问题**: 负载均衡失效
```bash
# 检查负载均衡器状态
curl http://localhost:8086/health

# 检查服务注册情况
curl http://localhost:8089/services

# 重启负载均衡器
docker-compose restart load-balancer
```

### 2. 性能调优

#### FaaS性能优化
```yaml
# 容器池优化
container_pool_size: 15        # 根据并发需求调整
prewarm_containers: 5          # 预热容器数量
cleanup_interval: "3m"         # 清理间隔

# 资源限制优化
cpu_limit: "0.5"              # CPU限制
memory_limit: "512m"          # 内存限制
max_execution_time: "5m"      # 最大执行时间

# 并发控制
max_concurrent_executions: 150 # 最大并发数
```

#### SaaS性能优化
```yaml
# 自动扩缩容优化
auto_scaling_enabled: true
scale_up_threshold: 70.0       # 扩容阈值
scale_down_threshold: 30.0     # 缩容阈值
min_replicas: 2               # 最小副本数
max_replicas: 15              # 最大副本数

# 健康检查优化
health_check_interval: "20s"  # 健康检查间隔
health_check_timeout: "3s"    # 健康检查超时

# 负载均衡优化
algorithm: "least_connections" # 负载均衡算法
session_affinity: true        # 会话保持
```

## 📈 容量规划

### 1. 资源估算

#### FaaS模式资源需求
```yaml
# 单个执行器实例
CPU: 0.5-1.0 核心
内存: 1-2 GB
磁盘: 10-20 GB

# 容器池资源
每个预热容器: 100MB 内存
临时存储: 1GB per 执行

# 并发计算
100并发 ≈ 2GB 内存 + 1CPU核心
```

#### SaaS模式资源需求
```yaml
# 服务管理器
CPU: 0.5 核心
内存: 512MB
磁盘: 5GB

# 负载均衡器
CPU: 0.25 核心
内存: 256MB

# 每个服务实例
CPU: 0.1-0.5 核心
内存: 128MB-1GB
磁盘: 1-5GB
```

### 2. 扩容策略

#### 水平扩容
```bash
# FaaS模式
# 增加执行器实例
docker-compose up -d --scale faas-executor=3

# SaaS模式
# 增加服务实例
curl -X POST http://localhost:8088/api/v1/saas/services/{id}/scale \
  -H "Content-Type: application/json" \
  -d '{"replicas": 5}'
```

#### 垂直扩容
```yaml
# 调整资源限制
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

## 🔒 安全配置

### 1. 网络安全
```yaml
# 防火墙配置
sudo ufw allow 8087/tcp  # FaaS端口
sudo ufw allow 8088/tcp  # SaaS端口
sudo ufw deny 5432/tcp   # 数据库端口（仅内部访问）

# TLS配置
nginx:
  ssl_certificate: /etc/ssl/certs/paas.crt
  ssl_certificate_key: /etc/ssl/private/paas.key
  ssl_protocols: TLSv1.2 TLSv1.3
```

### 2. 访问控制
```yaml
# JWT认证配置
jwt:
  secret: "your-secret-key"
  expiration: "24h"
  
# RBAC权限配置
rbac:
  enabled: true
  roles:
    - admin
    - developer
    - viewer
```

## 📝 备份和恢复

### 1. 数据备份
```bash
# 数据库备份
docker exec paas-postgres pg_dump -U paas_user paas_faas > backup_faas.sql
docker exec paas-postgres pg_dump -U paas_user paas_saas > backup_saas.sql

# Redis备份
docker exec paas-redis redis-cli --rdb /data/dump.rdb

# 配置备份
tar -czf config_backup.tar.gz deployments/
```

### 2. 数据恢复
```bash
# 数据库恢复
docker exec -i paas-postgres psql -U paas_user paas_faas < backup_faas.sql

# Redis恢复
docker cp dump.rdb paas-redis:/data/
docker restart paas-redis
```

## 📞 支持和联系

- **技术支持**: <EMAIL>
- **文档更新**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

---

**注意**: 本文档会根据平台更新持续维护，请定期检查最新版本。
