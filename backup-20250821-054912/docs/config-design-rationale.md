# 配置设计理念和 Redis 可选配置方案

## 为什么需要区分 .env 配置和 YAML 配置？

### 传统方案的问题

在之前的设计中，我们确实混合使用了 `.env` 文件和 YAML 配置文件，这带来了一些问题：

1. **配置分散**: 配置信息分布在不同格式的文件中
2. **维护复杂**: 需要同时维护两套配置格式
3. **覆盖关系不清**: 不清楚哪个配置会覆盖另一个
4. **学习成本高**: 开发者需要了解两种配置方式

### 新的统一方案

我们现在采用了**统一的 YAML 配置 + 环境变量覆盖**的方案：

```yaml
# 主配置文件 (app.minimal.yaml)
redis:
  enabled: false          # 通过配置文件控制
  addr: "redis:6379"
  password: ""

# 环境变量覆盖 (可选)
# PAAS_REDIS_ENABLED=true
# PAAS_REDIS_ADDR=localhost:6379
```

**优势：**
- **单一真相源**: YAML 文件是配置的主要来源
- **环境变量覆盖**: 支持部署时的动态配置
- **结构化配置**: 支持复杂的嵌套配置
- **可读性强**: YAML 格式直观易懂

## Redis 可选配置的实现方案

### 1. 配置文件层面

#### 最小化配置（无 Redis）
```yaml
# configs/app.minimal.yaml
redis:
  enabled: false          # 禁用 Redis

# 使用内存缓存作为替代
resources:
  memory_cache:
    max_size: "100MB"
    ttl: "1h"
```

#### 标准配置（有 Redis）
```yaml
# configs/app.standard.yaml
redis:
  enabled: true           # 启用 Redis
  addr: "redis:6379"
  password: ""
  db: 0
  pool_size: 10
```

### 2. Docker Compose 层面

使用 **profiles** 机制控制服务启动：

```yaml
# docker-compose.flexible.yml
services:
  redis:
    image: redis:7-alpine
    # ... 其他配置
    profiles:
      - redis           # 仅在 redis profile 时启动
      - standard        # 标准模式包含 redis
      - full            # 完整模式包含 redis

  api-gateway:
    # ... 其他配置
    environment:
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
```

### 3. 应用代码层面

#### 缓存接口抽象
```go
// pkg/cache/interface.go
type Cache interface {
    Get(key string) (string, error)
    Set(key string, value string, ttl time.Duration) error
    Delete(key string) error
}

// Redis 实现
type RedisCache struct {
    client *redis.Client
}

// 内存缓存实现
type MemoryCache struct {
    store sync.Map
    ttl   time.Duration
}
```

#### 动态缓存初始化
```go
// internal/cache/manager.go
func NewCacheManager(config *Config) Cache {
    if config.Redis.Enabled {
        // 使用 Redis 缓存
        return NewRedisCache(config.Redis)
    } else {
        // 使用内存缓存
        return NewMemoryCache(config.Resources.MemoryCache)
    }
}
```

### 4. 启动脚本层面

智能检测和配置：

```bash
# scripts/start-local-dev.sh

# 根据配置模式设置环境变量
case "$config_mode" in
    "minimal")
        export REDIS_ENABLED=false
        # 不启动 Redis 服务
        PROFILES=""
        ;;
    "standard"|"full")
        export REDIS_ENABLED=true
        export REDIS_ADDR="redis:6379"
        # 启动 Redis 服务
        PROFILES="database,redis,cicd"
        ;;
esac
```

## 使用示例

### 场景1：不需要 Redis 的最小化环境

```bash
# 启动最小化环境（无 Redis）
./scripts/start-local-dev.sh start --minimal

# 或者显式禁用 Redis
export PAAS_REDIS_ENABLED=false
./scripts/start-local-dev.sh start
```

**结果：**
- 不启动 Redis 容器
- 应用使用内存缓存
- 节省资源和启动时间

### 场景2：需要 Redis 的标准环境

```bash
# 启动标准环境（包含 Redis）
./scripts/start-local-dev.sh start --standard

# 或者显式启用 Redis
export PAAS_REDIS_ENABLED=true
export PAAS_REDIS_ADDR="redis:6379"
./scripts/start-local-dev.sh start
```

**结果：**
- 启动 Redis 容器
- 应用连接到 Redis
- 提供分布式缓存能力

### 场景3：使用外部 Redis

```bash
# 使用外部 Redis 服务
export PAAS_REDIS_ENABLED=true
export PAAS_REDIS_ADDR="external-redis:6379"
export PAAS_REDIS_PASSWORD="your-password"

# 启动时不包含 Redis 容器
./scripts/start-local-dev.sh start --minimal
```

**结果：**
- 不启动本地 Redis 容器
- 应用连接到外部 Redis
- 适用于生产环境或共享 Redis 的场景

## 配置覆盖优先级

配置的优先级从高到低：

1. **环境变量** (`PAAS_REDIS_ENABLED=true`)
2. **命令行参数** (`--config app.custom.yaml`)
3. **配置文件** (`redis.enabled: true`)
4. **默认值** (代码中的默认配置)

```bash
# 示例：配置文件禁用 Redis，但环境变量启用
# configs/app.minimal.yaml: redis.enabled: false
export PAAS_REDIS_ENABLED=true  # 这个会覆盖配置文件

./scripts/start-local-dev.sh start -c configs/app.minimal.yaml
# 结果：Redis 被启用（环境变量优先级更高）
```

## 配置验证和错误处理

系统会自动验证配置的一致性：

```go
// pkg/config/manager.go
func (m *Manager) validate() error {
    // 验证 Redis 配置
    if m.config.Redis.Enabled && m.config.Redis.Addr == "" {
        return fmt.Errorf("Redis 已启用但未配置地址")
    }
    
    // 验证缓存配置
    if !m.config.Redis.Enabled && m.config.Resources.MemoryCache.MaxSize == "" {
        log.Warn("Redis 已禁用且未配置内存缓存大小，使用默认值")
        m.config.Resources.MemoryCache.MaxSize = "100MB"
    }
    
    return nil
}
```

## 迁移现有配置

如果您已经有现有的配置文件，可以使用迁移脚本：

```bash
# 自动分析现有配置并推荐迁移方案
./scripts/migrate-config.sh --dry-run

# 迁移到最小化配置（无 Redis）
./scripts/migrate-config.sh --target minimal --backup

# 迁移到标准配置（有 Redis）
./scripts/migrate-config.sh --target standard --backup
```

## 总结

新的配置方案解决了以下问题：

1. **统一配置格式**: 只使用 YAML，避免 .env 和 YAML 混合
2. **可选组件支持**: 通过 `enabled` 开关控制组件启用/禁用
3. **智能服务编排**: 根据配置自动选择需要启动的服务
4. **环境变量覆盖**: 保持部署时的灵活性
5. **配置验证**: 自动检查配置的有效性和一致性

这种设计既简化了配置管理，又提供了足够的灵活性来满足不同的部署需求。
