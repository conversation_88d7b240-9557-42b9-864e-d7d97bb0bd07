# CI/CD 服务使用指南

## 概述

CI/CD 服务是 PaaS 平台的核心组件之一，提供完整的持续集成和持续部署功能。本服务支持流水线管理、构建执行、部署管理、环境管理等功能，帮助开发团队实现自动化的软件交付流程。

## 主要特性

### 🚀 核心功能
- **流水线管理**: 支持 YAML 配置的灵活流水线定义
- **构建执行**: 多种构建步骤类型，支持并行和串行执行
- **部署管理**: 多环境部署，支持滚动更新、蓝绿部署等策略
- **实时监控**: 构建日志实时推送，状态实时更新
- **Webhook 集成**: 支持 Gitea、GitHub、GitLab 自动触发

### 🔧 技术特性
- **容器化构建**: 基于 Docker 的隔离构建环境
- **分布式架构**: 支持多节点构建，负载均衡
- **高可用设计**: 支持集群部署，故障自动恢复
- **安全可靠**: 完整的权限控制和审计日志
- **可扩展性**: 插件化架构，支持自定义构建步骤

## 快速开始

### 环境要求

- **Go**: 1.21 或更高版本
- **Docker**: 20.10 或更高版本
- **PostgreSQL**: 13 或更高版本（推荐）
- **Redis**: 6.0 或更高版本
- **Kubernetes**: 1.20 或更高版本（可选）

### 本地开发环境

1. **克隆代码**
```bash
git clone <repository-url>
cd paas-platform
```

2. **安装依赖**
```bash
go mod download
```

3. **启动依赖服务**
```bash
docker-compose -f deployments/docker/docker-compose.yml up -d postgres redis
```

4. **配置环境变量**
```bash
export DATABASE_DSN="host=localhost user=cicd password=cicd123 dbname=cicd_db port=5432 sslmode=disable"
export REDIS_ADDR="localhost:6379"
export LOG_LEVEL="debug"
```

5. **运行服务**
```bash
go run cmd/cicd-service/main.go
```

6. **验证服务**
```bash
curl http://localhost:8082/health
```

### Docker 部署

1. **使用部署脚本**
```bash
./scripts/deploy.sh docker -e dev
```

2. **手动部署**
```bash
cd deployments/docker
docker-compose up -d
```

3. **验证部署**
```bash
curl http://localhost:8082/health
```

### Kubernetes 部署

1. **使用部署脚本**
```bash
./scripts/deploy.sh k8s -e prod -t v1.0.0
```

2. **手动部署**
```bash
kubectl apply -f deployments/kubernetes/cicd-service.yaml
```

3. **检查部署状态**
```bash
kubectl get pods -n cicd-system
kubectl get services -n cicd-system
```

## 配置说明

### 服务配置

主要配置文件位于 `configs/cicd-service.yaml`，包含以下配置项：

#### 服务器配置
```yaml
server:
  port: 8082                    # 服务端口
  mode: debug                   # 运行模式: debug, release, test
  read_timeout: 30s             # 读取超时时间
  write_timeout: 30s            # 写入超时时间
```

#### 数据库配置
```yaml
database:
  driver: postgres              # 数据库驱动
  dsn: "host=localhost..."      # 数据源名称
  max_open_conns: 100           # 最大连接数
  max_idle_conns: 10            # 最大空闲连接数
```

#### 构建配置
```yaml
build:
  nodes:
    default_capacity: 5         # 默认节点容量
    heartbeat_interval: 30s     # 心跳间隔
  queue:
    max_concurrent_builds: 10   # 最大并发构建数
    retry_attempts: 3           # 重试次数
  timeout:
    default_build: 3600s        # 默认构建超时
```

### 环境变量

支持通过环境变量覆盖配置文件设置：

- `SERVER_PORT`: 服务端口
- `DATABASE_DSN`: 数据库连接字符串
- `REDIS_ADDR`: Redis 地址
- `LOG_LEVEL`: 日志级别
- `GIN_MODE`: Gin 运行模式

## 流水线配置

### 基本结构

```yaml
apiVersion: v1
kind: Pipeline
metadata:
  name: example-pipeline
  description: 示例流水线
spec:
  triggers:
    - type: push
      branches: [main, develop]
  stages:
    - name: 构建
      steps:
        - name: 检出代码
          type: git_checkout
        - name: 安装依赖
          type: shell
          config:
            commands: [npm install]
```

### 支持的步骤类型

#### 1. Git 检出 (git_checkout)
```yaml
- name: 检出代码
  type: git_checkout
  config:
    repository: https://github.com/example/repo.git
    branch: main
    depth: 1
```

#### 2. Shell 命令 (shell)
```yaml
- name: 运行测试
  type: shell
  config:
    commands:
      - npm install
      - npm test
    working_dir: /app
    env:
      NODE_ENV: test
```

#### 3. Docker 构建 (docker_build)
```yaml
- name: 构建镜像
  type: docker_build
  config:
    dockerfile: Dockerfile
    context: .
    tags:
      - app:${BUILD_NUMBER}
      - app:latest
    build_args:
      VERSION: ${BUILD_NUMBER}
```

#### 4. Docker 推送 (docker_push)
```yaml
- name: 推送镜像
  type: docker_push
  config:
    image: app:${BUILD_NUMBER}
    registry: localhost:5000
    username: admin
    password: ${REGISTRY_PASSWORD}
```

#### 5. 部署 (deploy)
```yaml
- name: 部署应用
  type: deploy
  config:
    environment: production
    strategy: rolling
    replicas: 3
    image: app:${BUILD_NUMBER}
```

#### 6. 缓存操作 (cache)
```yaml
- name: 恢复缓存
  type: cache
  config:
    action: restore
    key: deps-${BRANCH}-${COMMIT_HASH}
    paths: [node_modules, .npm]
```

### 内置变量

流水线执行时可以使用以下内置变量：

- `${BUILD_ID}`: 构建 ID
- `${BUILD_NUMBER}`: 构建编号
- `${PIPELINE_ID}`: 流水线 ID
- `${BRANCH}`: 分支名称
- `${COMMIT_HASH}`: 提交哈希
- `${COMMIT_MSG}`: 提交消息
- `${AUTHOR}`: 提交作者
- `${TIMESTAMP}`: 构建时间戳

## API 使用

### 认证

所有 API 请求需要在请求头中包含 JWT Token：

```bash
curl -H "Authorization: Bearer <your-token>" \
     http://localhost:8082/api/v1/pipelines
```

### 常用 API

#### 创建流水线
```bash
curl -X POST http://localhost:8082/api/v1/pipelines \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d @pipeline-config.json
```

#### 触发构建
```bash
curl -X POST http://localhost:8082/api/v1/builds \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "pipeline_id": "pipeline-123",
    "branch": "main",
    "trigger_type": "manual"
  }'
```

#### 获取构建日志
```bash
curl http://localhost:8082/api/v1/builds/build-123/logs \
  -H "Authorization: Bearer <token>"
```

#### 实时日志流
```bash
wscat -c ws://localhost:8082/api/v1/builds/build-123/logs/stream
```

## Webhook 配置

### Gitea 配置

1. 进入仓库设置 → Webhooks
2. 添加 Webhook：
   - URL: `http://your-cicd-service:8082/api/v1/webhooks/gitea`
   - 密钥: 配置文件中的 `webhook.secret`
   - 事件: Push events

### GitHub 配置

1. 进入仓库设置 → Webhooks
2. 添加 Webhook：
   - Payload URL: `http://your-cicd-service:8082/api/v1/webhooks/github`
   - Content type: `application/json`
   - Secret: 配置文件中的 `webhook.secret`

### GitLab 配置

1. 进入项目设置 → Webhooks
2. 添加 Webhook：
   - URL: `http://your-cicd-service:8082/api/v1/webhooks/gitlab`
   - Secret Token: 配置文件中的 `webhook.secret`
   - Trigger: Push events

## 监控和日志

### 健康检查

- **健康状态**: `GET /health`
- **就绪状态**: `GET /ready`
- **指标数据**: `GET /metrics` (Prometheus 格式)

### 日志管理

服务支持多种日志输出方式：

1. **控制台输出**: 开发环境默认
2. **文件输出**: 生产环境推荐
3. **结构化日志**: JSON 格式，便于分析

配置示例：
```yaml
log:
  level: info
  format: json
  output: file
  file_path: /app/logs/cicd-service.log
```

### 性能监控

集成 Prometheus 指标收集：

- 构建任务数量和状态
- 构建执行时间分布
- 节点资源使用情况
- API 请求响应时间

## 故障排除

### 常见问题

#### 1. 服务启动失败
- 检查配置文件格式
- 验证数据库连接
- 查看服务日志

#### 2. 构建任务卡住
- 检查构建节点状态
- 查看构建日志
- 验证资源限制

#### 3. Webhook 不工作
- 验证 Webhook 配置
- 检查网络连通性
- 查看服务日志

### 调试技巧

1. **启用调试日志**
```bash
export LOG_LEVEL=debug
```

2. **查看详细错误**
```bash
curl -v http://localhost:8082/api/v1/builds/build-123
```

3. **检查服务状态**
```bash
docker-compose logs cicd-service
kubectl logs -n cicd-system deployment/cicd-service
```

## 最佳实践

### 流水线设计

1. **阶段划分**: 合理划分构建阶段，便于并行执行
2. **错误处理**: 添加适当的错误处理和重试机制
3. **缓存使用**: 合理使用缓存减少构建时间
4. **资源限制**: 设置合适的资源限制避免资源竞争

### 安全考虑

1. **密钥管理**: 使用环境变量或密钥管理系统
2. **网络隔离**: 配置适当的网络策略
3. **权限控制**: 实施最小权限原则
4. **审计日志**: 启用完整的审计日志

### 性能优化

1. **并行构建**: 合理配置并发构建数量
2. **资源监控**: 定期监控资源使用情况
3. **缓存策略**: 实施有效的缓存策略
4. **清理策略**: 定期清理旧的构建数据

## 扩展开发

### 自定义构建步骤

可以通过实现 `BuildStep` 接口来添加自定义构建步骤：

```go
type CustomStep struct {
    // 步骤配置
}

func (s *CustomStep) Execute(ctx context.Context, config map[string]interface{}) error {
    // 实现自定义逻辑
    return nil
}
```

### 插件开发

服务支持插件化扩展，可以开发自定义插件来扩展功能。

## 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：

- 提交 Issue
- 发送邮件
- 在线文档

---

**版本**: v1.0.0  
**更新时间**: 2024-01-15  
**维护团队**: PaaS 平台开发团队
