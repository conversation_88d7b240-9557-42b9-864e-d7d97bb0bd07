# GraphQL 网关

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的 GraphQL 网关功能。该系统提供了完整的 GraphQL 协议支持，包括 Schema 聚合、查询路由、缓存优化、性能监控、查询验证和安全控制等功能，能够将多个 GraphQL 服务统一为单一的 GraphQL 端点，简化客户端集成并提升开发效率。

## 🏗️ 系统架构

```
GraphQL 客户端 → GraphQL 网关 → Schema 聚合 → 查询路由 → 后端服务
       ↓              ↓           ↓         ↓         ↓
   [查询解析]    [查询验证]    [缓存检查]  [服务选择]  [结果合并]
       ↓              ↓           ↓         ↓         ↓
   [安全检查]    [复杂度分析]  [结果缓存]  [负载均衡]  [响应优化]
```

### 核心组件

1. **GraphQLGateway** (`pkg/graphql/gateway.go`)
   - GraphQL 网关核心实现
   - 查询解析、验证和路由
   - Schema 聚合和管理

2. **Service** (`pkg/graphql/gateway.go`)
   - GraphQL 服务抽象
   - 服务注册和管理
   - 健康检查和负载均衡

3. **Cache** (`pkg/graphql/cache.go`)
   - 查询结果缓存
   - 内存和 Redis 缓存支持
   - 缓存策略和过期管理

4. **GraphQLMiddleware** (`pkg/middleware/graphql.go`)
   - Gin 中间件集成
   - 请求拦截和处理
   - 安全控制和 CORS 支持

5. **APIHandler** (`pkg/graphql/api.go`)
   - RESTful 管理 API
   - 服务管理和监控
   - Schema 管理和内省

## ✅ 功能特性

### 1. Schema 管理
- ✅ **Schema 聚合**：多个 GraphQL 服务的 Schema 自动聚合
- ✅ **Schema 验证**：Schema 格式验证和兼容性检查
- ✅ **Schema 内省**：支持 GraphQL 内省查询
- ✅ **Schema 版本控制**：Schema 版本管理和更新
- ✅ **动态 Schema**：运行时 Schema 更新和重载

### 2. 查询处理
- ✅ **查询解析**：GraphQL 查询语法解析和验证
- ✅ **查询路由**：智能查询路由到对应服务
- ✅ **查询验证**：查询深度、复杂度和格式验证
- ✅ **查询优化**：查询重写和优化
- ✅ **批量查询**：多查询批量处理和优化

### 3. 服务管理
- ✅ **服务注册**：动态服务注册和注销
- ✅ **服务发现**：自动服务发现和健康检查
- ✅ **负载均衡**：多种负载均衡策略
- ✅ **故障转移**：服务故障自动转移
- ✅ **服务监控**：服务状态和性能监控

### 4. 缓存系统
- ✅ **查询缓存**：GraphQL 查询结果缓存
- ✅ **多级缓存**：内存和分布式缓存支持
- ✅ **缓存策略**：TTL、LRU 等缓存策略
- ✅ **缓存失效**：智能缓存失效和更新
- ✅ **缓存统计**：缓存命中率和性能统计

### 5. 安全功能
- ✅ **查询限制**：查询深度和复杂度限制
- ✅ **认证授权**：基于 Token 的认证和授权
- ✅ **速率限制**：查询速率限制和防护
- ✅ **CORS 支持**：跨域请求支持和控制
- ✅ **输入验证**：查询参数验证和过滤

### 6. 开发工具
- ✅ **GraphQL Playground**：交互式查询界面
- ✅ **Schema 浏览器**：Schema 结构浏览和文档
- ✅ **查询调试**：查询执行跟踪和调试
- ✅ **性能分析**：查询性能分析和优化建议
- ✅ **错误诊断**：详细的错误信息和诊断

## 🔧 配置说明

### 1. GraphQL 网关配置

```go
type Config struct {
    Enabled              bool          // 是否启用
    Path                 string        // GraphQL 路径
    PlaygroundEnabled    bool          // 启用 Playground
    PlaygroundPath       string        // Playground 路径
    IntrospectionEnabled bool          // 启用内省
    MaxQueryDepth        int           // 最大查询深度
    MaxQueryComplexity   int           // 最大查询复杂度
    Timeout              time.Duration // 查询超时时间
    CacheEnabled         bool          // 启用缓存
    CacheTTL             time.Duration // 缓存TTL
    EnableMetrics        bool          // 启用指标
    EnableTracing        bool          // 启用追踪
    EnableLogging        bool          // 启用日志
    RateLimitEnabled     bool          // 启用速率限制
    RateLimit            int           // 速率限制
    AuthRequired         bool          // 需要认证
    CorsEnabled          bool          // 启用CORS
    CorsOrigins          []string      // CORS来源
}
```

### 2. 服务配置

```go
type Service struct {
    ID          string            // 服务ID
    Name        string            // 服务名称
    URL         string            // 服务URL
    Schema      string            // GraphQL Schema
    Headers     map[string]string // 请求头
    Timeout     time.Duration     // 超时时间
    RetryCount  int               // 重试次数
    Enabled     bool              // 是否启用
    HealthCheck string            // 健康检查URL
    Weight      int               // 负载均衡权重
    Version     string            // 服务版本
    Metadata    map[string]interface{} // 元数据
}
```

### 3. 中间件配置

```go
type GraphQLMiddlewareConfig struct {
    Enabled            bool     // 是否启用
    Path               string   // GraphQL 路径
    PlaygroundPath     string   // Playground 路径
    SkipPaths          []string // 跳过的路径
    OnlyPaths          []string // 仅处理的路径
    RequireAuth        bool     // 需要认证
    AuthHeader         string   // 认证头名称
    UserIDHeader       string   // 用户ID头名称
    LogRequests        bool     // 记录请求日志
    LogResponses       bool     // 记录响应日志
    AddHeaders         bool     // 添加响应头
    CorsEnabled        bool     // 启用CORS
    CorsOrigins        []string // CORS来源
    RateLimitEnabled   bool     // 启用速率限制
    RateLimit          int      // 速率限制
    MaxQueryDepth      int      // 最大查询深度
    MaxQueryComplexity int      // 最大查询复杂度
}
```

## 🚀 使用示例

### 1. 基础 GraphQL 查询

```javascript
// 客户端查询
const query = `
  query GetUser($id: ID!) {
    user(id: $id) {
      id
      name
      email
      profile {
        avatar
        bio
      }
    }
  }
`;

const variables = { id: "123" };

fetch('/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    query,
    variables,
    operationName: 'GetUser'
  })
})
.then(response => response.json())
.then(data => {
  console.log('GraphQL 响应:', data);
});
```

### 2. 服务注册和管理

```bash
# 注册 GraphQL 服务
curl -X POST http://localhost:8080/api/v1/graphql/services \
  -H "Content-Type: application/json" \
  -d '{
    "id": "user-service",
    "name": "用户服务",
    "url": "http://user-service:4000/graphql",
    "schema": "type Query { user(id: ID!): User } type User { id: ID! name: String! email: String! }",
    "headers": {
      "Authorization": "Bearer service-token"
    },
    "timeout": 30,
    "retry_count": 3,
    "enabled": true,
    "weight": 1,
    "version": "1.0.0"
  }'

# 获取所有服务
curl http://localhost:8080/api/v1/graphql/services

# 获取聚合 Schema
curl http://localhost:8080/api/v1/graphql/schema

# 获取统计信息
curl http://localhost:8080/api/v1/graphql/stats
```

### 3. GraphQL Playground 使用

```bash
# 访问 GraphQL Playground
open http://localhost:8080/graphql/playground

# 在 Playground 中执行查询
query {
  hello
  version
}

# 执行带变量的查询
query GetUser($id: ID!) {
  user(id: $id) {
    id
    name
    email
  }
}

# 变量
{
  "id": "123"
}
```

### 4. 高级查询示例

```graphql
# 复杂嵌套查询
query GetUserWithPosts($userId: ID!, $limit: Int = 10) {
  user(id: $userId) {
    id
    name
    email
    profile {
      avatar
      bio
      createdAt
    }
    posts(limit: $limit) {
      id
      title
      content
      publishedAt
      comments {
        id
        content
        author {
          name
        }
      }
    }
  }
}

# 多字段查询
query GetDashboardData {
  currentUser {
    id
    name
    notifications {
      count
      unread
    }
  }
  recentPosts {
    id
    title
    author {
      name
    }
  }
  statistics {
    totalUsers
    totalPosts
    activeUsers
  }
}

# 变更操作
mutation CreatePost($input: CreatePostInput!) {
  createPost(input: $input) {
    id
    title
    content
    author {
      name
    }
    createdAt
  }
}
```

## 📊 查询格式

### 1. 标准查询格式

```json
{
  "query": "query GetUser($id: ID!) { user(id: $id) { id name email } }",
  "variables": {
    "id": "123"
  },
  "operationName": "GetUser",
  "extensions": {
    "tracing": true,
    "caching": {
      "ttl": 300
    }
  }
}
```

### 2. 响应格式

```json
{
  "data": {
    "user": {
      "id": "123",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  },
  "errors": null,
  "extensions": {
    "tracing": {
      "version": 1,
      "startTime": "2023-12-01T10:30:00.000Z",
      "endTime": "2023-12-01T10:30:00.150Z",
      "duration": 150000000
    }
  }
}
```

### 3. 错误响应

```json
{
  "data": null,
  "errors": [
    {
      "message": "用户不存在",
      "locations": [
        {
          "line": 2,
          "column": 3
        }
      ],
      "path": ["user"],
      "extensions": {
        "code": "USER_NOT_FOUND",
        "timestamp": "2023-12-01T10:30:00Z"
      }
    }
  ]
}
```

## 📈 监控和统计

### 1. 统计信息

```json
{
  "total_requests": 10000,
  "success_requests": 9500,
  "error_requests": 500,
  "cache_hits": 3000,
  "cache_misses": 7000,
  "average_latency": "150ms",
  "total_latency": "25m",
  "query_count": 8000,
  "mutation_count": 1500,
  "subscription_count": 500,
  "last_updated": "2023-12-01T10:30:00Z"
}
```

### 2. 服务健康检查

```bash
# GraphQL 健康检查
curl http://localhost:8080/graphql/health

# 响应示例
{
  "status": "healthy",
  "graphql": {
    "enabled": true,
    "total_services": 5,
    "enabled_services": 4,
    "total_requests": 10000,
    "success_requests": 9500,
    "error_requests": 500,
    "cache_hits": 3000,
    "cache_misses": 7000,
    "average_latency": "150ms",
    "last_updated": "2023-12-01T10:30:00Z"
  }
}
```

### 3. 性能指标

| 指标 | 描述 | 正常范围 |
|------|------|----------|
| 查询延迟 | GraphQL 查询平均延迟 | < 200ms |
| 成功率 | 查询成功率 | > 99% |
| 缓存命中率 | 查询缓存命中率 | > 70% |
| 服务可用性 | 后端服务可用性 | > 99.9% |
| 查询复杂度 | 平均查询复杂度 | < 500 |

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/graphql -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试 GraphQL 查询
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ hello }"}'

# 测试 Playground
open http://localhost:8080/graphql/playground

# 测试 Schema 获取
curl http://localhost:8080/graphql/schema

# 测试服务管理 API
curl http://localhost:8080/api/v1/graphql/services
curl http://localhost:8080/api/v1/graphql/stats
```

## 🔍 故障排除

### 常见问题

1. **查询失败**
   - 检查 Schema 定义
   - 验证查询语法
   - 确认服务可用性

2. **缓存问题**
   - 检查缓存配置
   - 验证缓存键生成
   - 确认缓存过期设置

3. **性能问题**
   - 分析查询复杂度
   - 检查服务响应时间
   - 优化查询结构

### 调试技巧

1. **启用详细日志**：
   ```go
   config.EnableLogging = true
   config.EnableTracing = true
   ```

2. **使用 Playground 调试**：
   ```bash
   # 访问 Playground 进行交互式调试
   open http://localhost:8080/graphql/playground
   ```

3. **检查服务状态**：
   ```bash
   # 查看服务列表和状态
   curl http://localhost:8080/api/v1/graphql/services
   
   # 查看健康状态
   curl http://localhost:8080/graphql/health
   ```

## 📈 性能优化

1. **查询优化**：
   - 使用查询缓存
   - 限制查询深度和复杂度
   - 优化 Schema 设计

2. **缓存策略**：
   - 合理设置缓存TTL
   - 使用分层缓存
   - 实现智能缓存失效

3. **服务优化**：
   - 实现服务负载均衡
   - 使用连接池
   - 优化网络配置

## 🔒 安全考虑

1. **查询安全**：查询深度和复杂度限制
2. **认证授权**：强制认证和基于角色的访问控制
3. **输入验证**：严格的输入验证和过滤
4. **速率限制**：查询速率限制和防护
5. **审计日志**：记录所有查询和操作

## 🚀 扩展功能

### 计划中的功能
- [ ] **订阅支持**：GraphQL 订阅和实时数据推送
- [ ] **联邦网关**：Apollo Federation 支持
- [ ] **查询分析**：查询性能分析和优化建议
- [ ] **自动化测试**：GraphQL 查询自动化测试

### 扩展策略

1. **联邦架构**：支持 GraphQL 联邦和微服务架构
2. **实时功能**：集成 WebSocket 支持订阅
3. **AI 优化**：使用 AI 进行查询优化和缓存策略
4. **多协议支持**：同时支持 REST 和 GraphQL

---

**注意**：GraphQL 网关是现代 API 架构的重要组成部分，能够简化客户端集成并提升开发效率。在生产环境中使用时，请根据实际业务场景合理配置参数，确保系统的安全性和性能。
