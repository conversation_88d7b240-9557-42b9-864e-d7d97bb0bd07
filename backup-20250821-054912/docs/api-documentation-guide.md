# PaaS 平台 API 文档使用指南

## 概述

PaaS 平台应用管理服务提供了完整的 RESTful API，用于管理应用的生命周期。本文档介绍如何访问和使用 API 文档。

## 访问 API 文档

### 1. Swagger UI 界面

**访问地址**: `http://localhost:8082/swagger/index.html`

Swagger UI 提供了交互式的 API 文档界面，您可以：
- 浏览所有可用的 API 接口
- 查看接口的详细参数和响应格式
- 直接在界面中测试 API 接口
- 下载 API 规范文件

### 2. 快速访问链接

- **文档首页**: `http://localhost:8082/docs` (自动重定向到 Swagger UI)
- **API 信息**: `http://localhost:8082/api/docs` (返回 API 基本信息的 JSON)

### 3. API 规范文件

- **JSON 格式**: `http://localhost:8082/swagger/doc.json`
- **YAML 格式**: 项目根目录下的 `docs/swagger.yaml`

## API 基本信息

- **标题**: PaaS 平台应用管理服务 API
- **版本**: 1.0.0
- **基础路径**: `/api/v1`
- **主机地址**: `localhost:8082`
- **协议**: HTTP

## 认证方式

API 使用 Bearer Token 认证方式：

```http
Authorization: Bearer <your-jwt-token>
```

### 开发环境认证

在开发环境中，您可以使用以下测试令牌：

```
Bearer dev-token
```

## 主要 API 接口

### 1. 应用管理

#### 创建应用
- **接口**: `POST /api/v1/apps`
- **描述**: 创建新的应用
- **认证**: 需要
- **请求体**:
```json
{
  "name": "my-app",
  "description": "我的应用",
  "language": "nodejs",
  "framework": "express"
}
```

#### 获取应用列表
- **接口**: `GET /api/v1/apps`
- **描述**: 获取应用列表，支持分页和过滤
- **认证**: 需要
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `page_size`: 每页大小 (默认: 20)
  - `name`: 应用名称过滤
  - `status`: 状态过滤

#### 获取应用详情
- **接口**: `GET /api/v1/apps/{id}`
- **描述**: 根据应用ID获取详细信息
- **认证**: 需要

#### 更新应用
- **接口**: `PUT /api/v1/apps/{id}`
- **描述**: 更新应用信息
- **认证**: 需要

#### 删除应用
- **接口**: `DELETE /api/v1/apps/{id}`
- **描述**: 删除指定的应用
- **认证**: 需要

### 2. 应用操作

#### 部署应用
- **接口**: `POST /api/v1/apps/{id}/deploy`
- **描述**: 部署应用到指定环境
- **认证**: 需要

#### 停止应用
- **接口**: `POST /api/v1/apps/{id}/stop`
- **描述**: 停止运行中的应用
- **认证**: 需要

#### 重启应用
- **接口**: `POST /api/v1/apps/{id}/restart`
- **描述**: 重启应用
- **认证**: 需要

#### 扩缩容
- **接口**: `POST /api/v1/apps/{id}/scale`
- **描述**: 调整应用实例数量
- **认证**: 需要

### 3. 版本管理

#### 获取版本列表
- **接口**: `GET /api/v1/apps/{id}/versions`
- **描述**: 获取应用的版本历史
- **认证**: 需要

#### 创建版本
- **接口**: `POST /api/v1/apps/{id}/versions`
- **描述**: 创建新的应用版本
- **认证**: 需要

#### 版本回滚
- **接口**: `POST /api/v1/apps/{id}/rollback/{version}`
- **描述**: 回滚到指定版本
- **认证**: 需要

### 4. 监控和日志

#### 获取应用指标
- **接口**: `GET /api/v1/apps/{id}/metrics`
- **描述**: 获取应用的性能指标
- **认证**: 需要

#### 获取应用日志
- **接口**: `GET /api/v1/apps/{id}/logs`
- **描述**: 获取应用的运行日志
- **认证**: 需要

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2025-08-10T08:00:00Z"
}
```

### 错误响应

```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "details": "详细错误信息",
  "timestamp": "2025-08-10T08:00:00Z"
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      // 数据项列表
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

## 状态码说明

- **200**: 请求成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **403**: 权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **500**: 服务器内部错误

## 使用示例

### 使用 curl 测试

```bash
# 获取 API 信息
curl http://localhost:8082/api/docs

# 创建应用 (需要认证)
curl -X POST http://localhost:8082/api/v1/apps \
  -H "Authorization: Bearer dev-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-app",
    "description": "测试应用",
    "language": "nodejs",
    "framework": "express"
  }'

# 获取应用列表
curl -H "Authorization: Bearer dev-token" \
  "http://localhost:8082/api/v1/apps?page=1&page_size=10"
```

### 使用 JavaScript 调用

```javascript
// 设置基础配置
const API_BASE = 'http://localhost:8082/api/v1';
const TOKEN = 'dev-token';

// 创建应用
async function createApp(appData) {
  const response = await fetch(`${API_BASE}/apps`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${TOKEN}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(appData)
  });
  
  return await response.json();
}

// 获取应用列表
async function getApps(page = 1, pageSize = 20) {
  const response = await fetch(
    `${API_BASE}/apps?page=${page}&page_size=${pageSize}`,
    {
      headers: {
        'Authorization': `Bearer ${TOKEN}`
      }
    }
  );
  
  return await response.json();
}
```

## 开发和测试

### 1. 本地开发

确保服务运行在开发模式下，Swagger UI 将自动启用：

```bash
# 启动服务
./scripts/start-app-manager.sh

# 访问文档
open http://localhost:8082/swagger/index.html
```

### 2. API 测试

推荐使用以下工具进行 API 测试：

- **Swagger UI**: 内置的交互式测试界面
- **Postman**: 专业的 API 测试工具
- **curl**: 命令行测试工具
- **HTTPie**: 用户友好的命令行 HTTP 客户端

### 3. 自动化测试

```bash
# 运行 API 集成测试
go test ./test/api -v

# 运行特定的测试用例
go test ./test/api -run TestCreateApp -v
```

## 常见问题

### Q1: 无法访问 Swagger UI

**解决方案**:
1. 确保服务运行在开发模式 (`debug`)
2. 检查端口是否正确 (默认 8082)
3. 确认防火墙设置

### Q2: API 调用返回 401 错误

**解决方案**:
1. 检查 Authorization 头是否正确设置
2. 确认使用正确的 Bearer Token 格式
3. 在开发环境中使用 `dev-token`

### Q3: 接口响应格式不符合预期

**解决方案**:
1. 查看 Swagger 文档中的响应示例
2. 检查请求的 Content-Type 头
3. 确认 API 版本是否正确

## 更新和维护

### 更新 API 文档

当 API 接口发生变化时，需要更新文档：

```bash
# 重新生成 Swagger 文档
swag init -g cmd/app-manager/main.go -o docs

# 重启服务以加载新文档
./scripts/stop-app-manager.sh
./scripts/start-app-manager.sh
```

### 版本管理

API 文档版本与服务版本保持同步，主要版本变更时会更新文档版本号。

## 联系支持

如有问题或建议，请联系：
- **邮箱**: <EMAIL>
- **文档**: 查看项目 README 和其他文档
- **问题反馈**: 通过项目 Issue 系统提交
