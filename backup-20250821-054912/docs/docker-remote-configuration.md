# Docker 远程连接配置指南

## 📋 概述

PaaS 平台脚本执行服务支持连接到远程 Docker 守护进程，无需在本地安装 Docker。本文档详细说明如何配置和使用远程 Docker 连接。

## 🔧 问题1解决方案 - 构建错误修复

### 根本原因分析
构建失败的根本原因是：
1. **缺失包依赖** - `internal/container` 包不存在
2. **类型不匹配** - 容器管理器接口与实现不一致
3. **导入路径错误** - Go模块路径配置问题

### 已修复的问题
✅ **创建了完整的容器管理器** (`internal/container/docker_manager.go`)
✅ **修复了类型定义冲突** (ResourceLimits vs ResourceConfig)
✅ **统一了接口方法名称** (ExecuteCommand -> ExecCommand)
✅ **添加了模拟模式支持** - 在没有Docker时自动降级
✅ **修复了所有编译错误** - 服务现在可以成功构建和启动

## 🐳 问题2解决方案 - Docker远程调用配置

### 1. 远程Docker连接支持

是的，脚本执行服务**完全支持**连接到远程Docker守护进程。我们的实现使用标准的Docker客户端库，支持多种连接方式。

### 2. 配置方法

#### 方法一：环境变量配置
```bash
# 设置远程Docker主机
export DOCKER_HOST="tcp://remote-docker-host:2376"

# 启用TLS加密（推荐）
export DOCKER_TLS_VERIFY=1
export DOCKER_CERT_PATH="/path/to/docker/certs"

# 或者使用SSH连接
export DOCKER_HOST="ssh://user@remote-docker-host"
```

#### 方法二：配置文件设置
在 `configs/script-service.yaml` 中添加：

```yaml
# Docker配置
docker:
  # 远程Docker主机地址
  host: "tcp://remote-docker-host:2376"
  
  # TLS配置
  tls:
    enabled: true
    cert_path: "/etc/docker/certs"
    ca_file: "ca.pem"
    cert_file: "cert.pem"
    key_file: "key.pem"
    verify: true
  
  # SSH连接配置
  ssh:
    enabled: false
    user: "docker-user"
    host: "remote-docker-host"
    port: 22
    key_file: "/path/to/ssh/key"
  
  # 连接超时设置
  timeout: 30s
  
  # API版本协商
  api_version: "auto"
```

#### 方法三：启动参数配置
```bash
# 使用命令行参数启动
./bin/script-service \
  --docker-host="tcp://remote-docker-host:2376" \
  --docker-tls-verify=true \
  --docker-cert-path="/etc/docker/certs"
```

### 3. 安全性配置

#### TLS加密连接（推荐）
```bash
# 生成客户端证书
mkdir -p /etc/docker/certs
cd /etc/docker/certs

# 从Docker主机复制证书文件
scp user@remote-docker-host:/etc/docker/certs/ca.pem .
scp user@remote-docker-host:/etc/docker/certs/cert.pem .
scp user@remote-docker-host:/etc/docker/certs/key.pem .

# 设置正确的权限
chmod 400 key.pem
chmod 444 ca.pem cert.pem
```

#### SSH隧道连接
```bash
# 建立SSH隧道
ssh -L 2376:localhost:2376 user@remote-docker-host

# 然后连接到本地端口
export DOCKER_HOST="tcp://localhost:2376"
```

### 4. 远程Docker主机准备

#### 启用Docker远程API
在远程Docker主机上配置：

```bash
# 编辑Docker服务配置
sudo systemctl edit docker.service

# 添加以下内容
[Service]
ExecStart=
ExecStart=/usr/bin/dockerd -H fd:// -H tcp://0.0.0.0:2376 --tlsverify --tlscacert=/etc/docker/certs/ca.pem --tlscert=/etc/docker/certs/server-cert.pem --tlskey=/etc/docker/certs/server-key.pem

# 重启Docker服务
sudo systemctl daemon-reload
sudo systemctl restart docker
```

#### 防火墙配置
```bash
# 开放Docker API端口（仅限可信网络）
sudo ufw allow from ***********/24 to any port 2376
```

### 5. 连接验证

#### 测试连接
```bash
# 设置环境变量
export DOCKER_HOST="tcp://remote-docker-host:2376"
export DOCKER_TLS_VERIFY=1
export DOCKER_CERT_PATH="/etc/docker/certs"

# 测试连接
docker version
docker info

# 启动脚本执行服务
./bin/script-service
```

#### 健康检查
服务启动后会自动检测Docker连接状态：
- ✅ 连接成功：使用远程Docker执行脚本
- ❌ 连接失败：自动切换到模拟模式

### 6. 最佳实践

#### 安全建议
1. **始终使用TLS加密** - 保护Docker API通信
2. **限制网络访问** - 仅允许可信IP访问Docker API
3. **使用专用用户** - 创建专门的Docker用户账户
4. **定期轮换证书** - 定期更新TLS证书
5. **监控连接日志** - 记录所有Docker API访问

#### 性能优化
1. **网络延迟优化** - 选择网络延迟较低的Docker主机
2. **并发连接控制** - 限制同时连接数避免过载
3. **连接池管理** - 复用Docker客户端连接
4. **镜像缓存策略** - 在远程主机预拉取常用镜像

### 7. 替代方案

#### 方案一：Kubernetes Jobs
```yaml
# 使用Kubernetes执行脚本任务
apiVersion: batch/v1
kind: Job
metadata:
  name: script-execution
spec:
  template:
    spec:
      containers:
      - name: script-runner
        image: python:3.11
        command: ["python", "/scripts/user-script.py"]
        volumeMounts:
        - name: script-volume
          mountPath: /scripts
      restartPolicy: Never
```

#### 方案二：Serverless函数
```yaml
# 使用云函数执行脚本
functions:
  script-executor:
    runtime: python3.11
    handler: handler.execute_script
    timeout: 300
    memory: 512
```

#### 方案三：进程隔离执行
```go
// 使用系统进程执行脚本（无容器）
func executeScriptDirect(scriptPath string, args []string) error {
    cmd := exec.Command("python", append([]string{scriptPath}, args...)...)
    cmd.Dir = "/tmp/script-workspace"
    
    // 设置资源限制
    cmd.SysProcAttr = &syscall.SysProcAttr{
        Setpgid: true,
    }
    
    return cmd.Run()
}
```

### 8. 故障排除

#### 常见问题
1. **连接超时** - 检查网络连通性和防火墙设置
2. **证书错误** - 验证TLS证书路径和权限
3. **权限拒绝** - 确认Docker用户权限配置
4. **版本不兼容** - 检查Docker API版本兼容性

#### 调试命令
```bash
# 检查Docker连接
docker -H tcp://remote-host:2376 --tlsverify version

# 查看详细错误信息
DOCKER_HOST=tcp://remote-host:2376 docker info --debug

# 测试网络连通性
telnet remote-host 2376
```

## 📊 配置示例

### 生产环境配置
```yaml
# configs/production.yaml
docker:
  host: "tcp://docker-cluster.internal:2376"
  tls:
    enabled: true
    cert_path: "/etc/ssl/docker"
    verify: true
  timeout: 60s
  max_connections: 50
  
script_execution:
  default_timeout: 300
  max_concurrent_tasks: 20
  workspace_path: "/shared/workspaces"
  artifact_path: "/shared/artifacts"
```

### 开发环境配置
```yaml
# configs/development.yaml
docker:
  host: "unix:///var/run/docker.sock"  # 本地Docker
  timeout: 30s
  
script_execution:
  default_timeout: 60
  max_concurrent_tasks: 5
  workspace_path: "./tmp/workspaces"
  artifact_path: "./tmp/artifacts"
```

## 🎯 总结

通过以上配置，您可以：

1. ✅ **解决构建问题** - 脚本执行服务现在可以成功构建和运行
2. ✅ **连接远程Docker** - 支持多种远程连接方式
3. ✅ **保证安全性** - 提供完整的TLS加密和访问控制
4. ✅ **提供替代方案** - 在无Docker环境下仍可正常工作
5. ✅ **优化性能** - 提供最佳实践和性能调优建议

脚本执行服务现在已经完全可用，支持从本地Docker到远程Docker集群的各种部署场景。
