# 开发环境认证配置指南

本文档详细说明如何在开发环境中配置应用程序以跳过用户身份认证机制，简化开发流程。

## ⚠️ 重要安全警告

**🚨 此配置仅适用于开发环境，绝不能在生产环境使用！**

- 🔓 开发模式会完全跳过用户认证
- 🧑‍💻 所有API请求将使用默认开发用户身份
- 🚨 生产环境使用此配置会造成严重安全风险

## 📋 目录

1. [认证架构概述](#认证架构概述)
2. [开发环境配置方法](#开发环境配置方法)
3. [环境变量控制](#环境变量控制)
4. [配置文件说明](#配置文件说明)
5. [快速启动指南](#快速启动指南)
6. [测试验证](#测试验证)
7. [故障排除](#故障排除)
8. [最佳实践](#最佳实践)

## 认证架构概述

### 当前认证实现

系统使用基于JWT的认证机制，包含以下组件：

- **JWT服务** (`internal/auth/jwt.go`): 负责令牌生成、验证和管理
- **认证中间件** (`pkg/middleware/auth.go`): 处理HTTP请求的认证逻辑
- **权限控制** (`pkg/middleware/permission.go`): 基于角色和权限的访问控制
- **用户管理** (`internal/auth/service.go`): 用户登录、注册和管理

### 开发模式支持

认证中间件内置了开发模式支持：

```go
// 开发模式配置
type AuthConfig struct {
    Enabled   bool          // 是否启用JWT校验
    DevMode   bool          // 开发模式开关
    DevToken  string        // 开发模式令牌
    DevUser   DevUserConfig // 开发模式用户信息
}
```

## 开发环境配置方法

### 方法一：使用开发环境配置文件（推荐）

系统为每个服务提供了专门的开发环境配置文件：

- `configs/app-manager.dev.yaml` - 应用管理服务
- `configs/script-service.dev.yaml` - 脚本执行服务
- `configs/cicd-service.dev.yaml` - CI/CD服务
- `configs/config-service.dev.yaml` - 配置服务

**配置示例：**

```yaml
# 安全配置 - 开发环境
security:
  jwt:
    enabled: false                    # ❌ 禁用JWT校验
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "dev-token-2024"       # 🔑 开发令牌
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "dev-user-001"
      tenant_id: "dev-tenant-001"
      username: "开发者"
      email: "developer@localhost"
      roles: ["admin", "developer"]
```

### 方法二：使用环境变量（灵活）

通过环境变量动态控制认证行为：

```bash
# 禁用认证
export PAAS_AUTH_ENABLED=false

# 启用开发模式
export PAAS_DEV_MODE=true

# 设置开发用户信息
export PAAS_DEV_USER_ID=dev-user-001
export PAAS_DEV_USER_ROLES=admin,developer
```

### 方法三：修改现有配置文件

在现有配置文件中修改认证设置：

```yaml
security:
  jwt:
    enabled: false    # 禁用认证
    dev_mode: true    # 启用开发模式
```

## 环境变量控制

### 核心环境变量

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|--------|------|
| `PAAS_AUTH_ENABLED` | 是否启用JWT认证 | `true` | `false` |
| `PAAS_DEV_MODE` | 是否启用开发模式 | `false` | `true` |
| `PAAS_DEV_TOKEN` | 开发模式令牌 | `dev-token` | `dev-token-2024` |
| `PAAS_JWT_SECRET` | JWT密钥 | - | `dev-jwt-secret` |

### 开发用户配置

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| `PAAS_DEV_USER_ID` | 开发用户ID | `dev-user-001` |
| `PAAS_DEV_USER_TENANT_ID` | 开发租户ID | `dev-tenant-001` |
| `PAAS_DEV_USER_USERNAME` | 开发用户名 | `开发者` |
| `PAAS_DEV_USER_EMAIL` | 开发用户邮箱 | `developer@localhost` |
| `PAAS_DEV_USER_ROLES` | 开发用户角色 | `admin,developer` |

### 环境检测

系统会自动检测以下环境标识符来启用开发模式：

```bash
ENV=development
NODE_ENV=development
GO_ENV=development
```

当检测到开发环境时，系统会自动：
- 禁用JWT认证 (`enabled: false`)
- 启用开发模式 (`dev_mode: true`)
- 使用开发用户身份

## 配置文件说明

### 开发环境配置文件特点

开发环境配置文件具有以下特点：

1. **认证跳过**: 完全禁用JWT认证
2. **开发用户**: 预设管理员权限的开发用户
3. **详细日志**: 启用debug级别日志
4. **本地存储**: 使用SQLite数据库
5. **安全警告**: 显示开发模式警告信息

### 配置文件结构

```yaml
# 🔓 安全配置 - 开发环境
security:
  jwt:
    enabled: false                    # 禁用认证
    dev_mode: true                    # 开发模式
    dev_token: "dev-token-2024"       # 开发令牌
    dev_user:                         # 开发用户
      id: "dev-user-001"
      roles: ["admin"]
    skip_paths:                       # 跳过认证路径
      - "/health"
      - "/swagger"

# 🚨 安全警告配置
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  开发模式已启用 - 用户认证已禁用
    🚨 此配置仅适用于开发环境！
```

## 快速启动指南

### 使用自动化脚本（推荐）

1. **启动开发环境**：
   ```bash
   ./scripts/start-dev-mode.sh
   ```

2. **测试认证配置**：
   ```bash
   ./scripts/test-dev-auth.sh
   ```

3. **停止开发环境**：
   ```bash
   ./scripts/stop-dev-mode.sh
   ```

### 手动启动

1. **设置环境变量**：
   ```bash
   source .env.development
   ```

2. **启动服务**：
   ```bash
   # 应用管理服务
   ./bin/app-manager --config=configs/app-manager.dev.yaml
   
   # 脚本执行服务
   ./bin/script-service --config=configs/script-service.dev.yaml
   ```

3. **验证配置**：
   ```bash
   # 无需认证即可访问
   curl http://localhost:8081/api/v1/apps
   ```

## 测试验证

### 自动化测试

运行认证测试脚本：

```bash
./scripts/test-dev-auth.sh
```

测试内容包括：
- ✅ 健康检查接口访问
- ✅ 无认证API访问
- ✅ 开发令牌API访问
- ✅ Swagger文档访问

### 手动测试

1. **健康检查**：
   ```bash
   curl http://localhost:8081/health
   ```

2. **无认证API访问**：
   ```bash
   curl http://localhost:8081/api/v1/apps
   ```

3. **开发令牌访问**：
   ```bash
   curl -H "Authorization: Bearer dev-token-2024" \
        http://localhost:8081/api/v1/apps
   ```

4. **Swagger文档**：
   ```bash
   open http://localhost:8081/swagger/index.html
   ```

### 预期结果

- ✅ 所有API请求返回200或404（而非401/403）
- ✅ 日志显示开发模式警告信息
- ✅ 请求上下文包含开发用户信息

## 故障排除

### 常见问题

1. **API返回401未授权**
   - 检查环境变量 `PAAS_AUTH_ENABLED=false`
   - 确认使用了开发环境配置文件
   - 验证认证中间件配置加载

2. **服务启动失败**
   - 检查端口是否被占用
   - 确认配置文件路径正确
   - 查看服务日志文件

3. **开发用户权限不足**
   - 检查 `PAAS_DEV_USER_ROLES` 环境变量
   - 确认开发用户角色配置
   - 验证权限中间件配置

### 调试方法

1. **查看日志**：
   ```bash
   tail -f logs/app-manager.log
   ```

2. **检查环境变量**：
   ```bash
   env | grep PAAS_
   ```

3. **验证配置加载**：
   ```bash
   curl http://localhost:8081/debug/config
   ```

### 重置环境

如果遇到问题，可以重置开发环境：

```bash
# 停止所有服务
./scripts/stop-dev-mode.sh --clean-workspace

# 清理数据
rm -rf data/ logs/

# 重新启动
./scripts/start-dev-mode.sh
```

## 最佳实践

### 开发环境安全

1. **环境隔离**：
   - 使用专门的开发环境配置
   - 不要在生产环境使用开发配置
   - 定期检查配置文件

2. **访问控制**：
   - 限制开发环境网络访问
   - 使用防火墙规则
   - 定期更新开发令牌

3. **数据保护**：
   - 不要在开发环境使用生产数据
   - 定期清理开发数据
   - 使用模拟数据进行测试

### 配置管理

1. **版本控制**：
   - 将开发配置文件纳入版本控制
   - 使用 `.env.example` 文件作为模板
   - 不要提交包含敏感信息的 `.env` 文件

2. **文档维护**：
   - 及时更新配置文档
   - 记录配置变更原因
   - 提供清晰的使用示例

3. **团队协作**：
   - 统一开发环境配置
   - 提供自动化启动脚本
   - 建立配置变更流程

### 监控和日志

1. **日志配置**：
   ```yaml
   log:
     level: debug      # 开发环境详细日志
     format: text      # 易读格式
     output: stdout    # 控制台输出
   ```

2. **监控指标**：
   - 启用健康检查
   - 监控服务状态
   - 记录性能指标

3. **告警设置**：
   - 服务异常告警
   - 资源使用告警
   - 配置变更通知

## 总结

通过本指南，您可以：

1. ✅ 理解系统的认证架构
2. ✅ 配置开发环境跳过认证
3. ✅ 使用环境变量灵活控制
4. ✅ 快速启动和测试开发环境
5. ✅ 解决常见配置问题
6. ✅ 遵循安全最佳实践

**记住：开发环境认证跳过是为了提高开发效率，但必须确保生产环境的安全性！**

## 相关文档

- [JWT认证指南](jwt-auth-guide.md) - 生产环境JWT认证配置
- [API文档指南](api-documentation-guide.md) - API接口文档
- [架构文档](architecture.md) - 系统整体架构说明

## 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看 [故障排除](#故障排除) 部分
2. 检查系统日志文件
3. 提交Issue或联系开发团队

---

*最后更新：2024年8月*
