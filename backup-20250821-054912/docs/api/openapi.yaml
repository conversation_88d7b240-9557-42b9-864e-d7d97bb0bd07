openapi: 3.0.3
info:
  title: PaaS 平台 API
  description: |
    PaaS 平台提供完整的应用管理、CI/CD、监控等功能的 RESTful API。
    
    ## 认证方式
    使用 JWT Bearer Token 进行认证：
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## 错误处理
    所有错误响应都遵循统一的格式，包含错误码、错误信息和详细描述。
    
    ## 限流策略
    - 每个用户每分钟最多 100 次请求
    - 管理员用户每分钟最多 500 次请求
    
  version: 1.0.0
  contact:
    name: PaaS 平台开发团队
    email: <EMAIL>
    url: https://docs.paas-platform.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.paas-platform.com/api/v1
    description: 生产环境
  - url: https://staging-api.paas-platform.com/api/v1
    description: 测试环境
  - url: http://localhost:8080/api/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  # 认证相关
  /auth/login:
    post:
      tags:
        - 认证
      summary: 用户登录
      description: 使用用户名和密码进行登录，获取访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: 用户名
                  example: admin
                password:
                  type: string
                  description: 密码
                  example: password123
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          token:
                            type: string
                            description: 访问令牌
                          refresh_token:
                            type: string
                            description: 刷新令牌
                          expires_in:
                            type: integer
                            description: 令牌过期时间（秒）
                          user:
                            $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/me:
    get:
      tags:
        - 认证
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细信息
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # 用户管理
  /users:
    get:
      tags:
        - 用户管理
      summary: 获取用户列表
      description: 分页获取用户列表，支持搜索和筛选
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: status
          in: query
          description: 用户状态
          schema:
            type: string
            enum: [active, disabled, pending]
        - name: role
          in: query
          description: 用户角色
          schema:
            type: string
            enum: [admin, developer, operator, user]
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          users:
                            type: array
                            items:
                              $ref: '#/components/schemas/User'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

    post:
      tags:
        - 用户管理
      summary: 创建用户
      description: 创建新用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          $ref: '#/components/responses/Conflict'

  /users/{id}:
    get:
      tags:
        - 用户管理
      summary: 获取用户详情
      description: 根据用户ID获取用户详细信息
      parameters:
        - name: id
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - 用户管理
      summary: 更新用户
      description: 更新用户信息
      parameters:
        - name: id
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - 用户管理
      summary: 删除用户
      description: 删除指定用户
      parameters:
        - name: id
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # 应用管理
  /applications:
    get:
      tags:
        - 应用管理
      summary: 获取应用列表
      description: 分页获取应用列表，支持搜索和筛选
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: status
          in: query
          description: 应用状态
          schema:
            type: string
            enum: [running, stopped, deploying, error]
        - name: environment
          in: query
          description: 环境
          schema:
            type: string
            enum: [development, testing, production]
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          applications:
                            type: array
                            items:
                              $ref: '#/components/schemas/Application'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - 应用管理
      summary: 创建应用
      description: 创建新应用
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApplicationRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Application'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: 操作成功
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
          example: "2024-01-15T14:30:25Z"

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            error:
              type: object
              properties:
                type:
                  type: string
                  description: 错误类型
                details:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      message:
                        type: string

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        page_size:
          type: integer
          description: 每页数量
        total:
          type: integer
          description: 总记录数
        total_pages:
          type: integer
          description: 总页数

    User:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
          example: "1"
        username:
          type: string
          description: 用户名
          example: admin
        email:
          type: string
          format: email
          description: 邮箱
          example: <EMAIL>
        real_name:
          type: string
          description: 真实姓名
          example: 系统管理员
        phone:
          type: string
          description: 手机号
          example: "13800138000"
        department:
          type: string
          description: 部门
          example: IT部门
        position:
          type: string
          description: 职位
          example: 系统管理员
        status:
          type: string
          enum: [active, disabled, pending]
          description: 用户状态
          example: active
        roles:
          type: array
          items:
            type: string
            enum: [admin, developer, operator, user]
          description: 用户角色
          example: [admin]
        avatar:
          type: string
          description: 头像URL
          example: https://example.com/avatar.jpg
        is_online:
          type: boolean
          description: 是否在线
          example: true
        last_login_at:
          type: string
          format: date-time
          description: 最后登录时间
          example: "2024-01-15T14:30:00Z"
        last_login_ip:
          type: string
          description: 最后登录IP
          example: "*************"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15T14:30:00Z"

    CreateUserRequest:
      type: object
      required:
        - username
        - email
        - password
      properties:
        username:
          type: string
          description: 用户名
          example: newuser
        email:
          type: string
          format: email
          description: 邮箱
          example: <EMAIL>
        password:
          type: string
          description: 密码
          example: password123
        real_name:
          type: string
          description: 真实姓名
          example: 新用户
        phone:
          type: string
          description: 手机号
          example: "13800138001"
        department:
          type: string
          description: 部门
          example: 开发部门
        position:
          type: string
          description: 职位
          example: 开发工程师
        roles:
          type: array
          items:
            type: string
            enum: [admin, developer, operator, user]
          description: 用户角色
          example: [developer]

    UpdateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: 邮箱
        real_name:
          type: string
          description: 真实姓名
        phone:
          type: string
          description: 手机号
        department:
          type: string
          description: 部门
        position:
          type: string
          description: 职位
        status:
          type: string
          enum: [active, disabled, pending]
          description: 用户状态
        roles:
          type: array
          items:
            type: string
            enum: [admin, developer, operator, user]
          description: 用户角色

    Application:
      type: object
      properties:
        id:
          type: string
          description: 应用ID
          example: "1"
        name:
          type: string
          description: 应用名称
          example: web-app
        description:
          type: string
          description: 应用描述
          example: 主要的Web应用程序
        repository_url:
          type: string
          description: 代码仓库URL
          example: https://github.com/user/repo.git
        branch:
          type: string
          description: 分支
          example: main
        environment:
          type: string
          enum: [development, testing, production]
          description: 环境
          example: production
        status:
          type: string
          enum: [running, stopped, deploying, error]
          description: 应用状态
          example: running
        owner_id:
          type: string
          description: 负责人ID
          example: "1"
        owner:
          type: string
          description: 负责人
          example: 张三
        config:
          type: object
          description: 应用配置
          properties:
            port:
              type: integer
              example: 8080
            env_vars:
              type: object
              additionalProperties:
                type: string
              example:
                NODE_ENV: production
            resources:
              type: object
              properties:
                cpu:
                  type: string
                  example: "500m"
                memory:
                  type: string
                  example: "512Mi"
        metrics:
          type: object
          description: 应用指标
          properties:
            cpu:
              type: number
              description: CPU使用率
              example: 45.2
            memory:
              type: number
              description: 内存使用率
              example: 67.8
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15T14:30:00Z"

    CreateApplicationRequest:
      type: object
      required:
        - name
        - repository_url
        - branch
        - environment
      properties:
        name:
          type: string
          description: 应用名称
          example: my-app
        description:
          type: string
          description: 应用描述
          example: 我的应用程序
        repository_url:
          type: string
          description: 代码仓库URL
          example: https://github.com/user/repo.git
        branch:
          type: string
          description: 分支
          example: main
        environment:
          type: string
          enum: [development, testing, production]
          description: 环境
          example: development
        config:
          type: object
          description: 应用配置
          properties:
            port:
              type: integer
              example: 8080
            env_vars:
              type: object
              additionalProperties:
                type: string
              example:
                NODE_ENV: development
            resources:
              type: object
              properties:
                cpu:
                  type: string
                  example: "500m"
                memory:
                  type: string
                  example: "512Mi"

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: 请求参数错误
            error:
              type: ValidationError
              details:
                - field: username
                  message: 用户名不能为空
            timestamp: "2024-01-15T14:30:25Z"

    Unauthorized:
      description: 未授权
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: 未授权访问
            error:
              type: AuthenticationError
            timestamp: "2024-01-15T14:30:25Z"

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 403
            message: 权限不足
            error:
              type: AuthorizationError
            timestamp: "2024-01-15T14:30:25Z"

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 404
            message: 资源不存在
            error:
              type: ResourceNotFound
            timestamp: "2024-01-15T14:30:25Z"

    Conflict:
      description: 资源冲突
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 409
            message: 资源已存在
            error:
              type: ResourceConflict
            timestamp: "2024-01-15T14:30:25Z"

tags:
  - name: 认证
    description: 用户认证相关接口
  - name: 用户管理
    description: 用户管理相关接口
  - name: 应用管理
    description: 应用管理相关接口
  - name: CI/CD
    description: 持续集成和部署相关接口
  - name: 配置管理
    description: 配置管理相关接口
  - name: 监控
    description: 监控和告警相关接口
  - name: 脚本管理
    description: 脚本管理相关接口
