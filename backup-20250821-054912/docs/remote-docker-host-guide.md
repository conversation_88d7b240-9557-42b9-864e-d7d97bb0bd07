# 远程 Docker Host 连接配置指南

## 概述

本指南详细说明如何配置 PaaS 平台连接到远程 Docker Host，支持多种连接方式，包括 TCP、TLS 和 SSH 连接。

## 连接方式对比

| 连接方式 | 安全性 | 配置复杂度 | 适用场景 | 推荐度 |
|---------|--------|-----------|----------|--------|
| TCP (无加密) | ❌ 低 | ⭐ 简单 | 仅开发环境 | ❌ 不推荐 |
| TCP + TLS | ✅ 高 | ⭐⭐⭐ 中等 | 生产环境 | ✅ 推荐 |
| SSH | ✅ 高 | ⭐⭐ 简单 | 所有环境 | ✅ 强烈推荐 |

## 方法一：SSH 连接（推荐）

### 1.1 配置步骤

SSH 连接是最安全和简单的方式，利用现有的 SSH 基础设施。

#### 步骤 1：确保远程主机 SSH 访问
```bash
# 测试 SSH 连接
ssh root@远程主机IP

# 或使用密钥认证
ssh -i ~/.ssh/id_rsa root@远程主机IP
```

#### 步骤 2：修改环境变量
```bash
# 在 .env.development 中设置
DOCKER_HOST=ssh://root@*************
# 或指定端口
DOCKER_HOST=ssh://root@*************:22
# 或使用密钥文件
DOCKER_HOST=ssh://root@*************
DOCKER_SSH_KEY_PATH=~/.ssh/id_rsa
```

#### 步骤 3：测试连接
```bash
# 设置环境变量
export DOCKER_HOST=ssh://root@*************

# 测试连接
docker version
docker ps
```

### 1.2 SSH 密钥配置

如果使用密钥认证：

```bash
# 生成 SSH 密钥对（如果没有）
ssh-keygen -t rsa -b 4096 -f ~/.ssh/docker_host_key

# 将公钥复制到远程主机
ssh-copy-id -i ~/.ssh/docker_host_key.pub root@远程主机IP

# 在环境变量中指定密钥路径
DOCKER_HOST=ssh://root@*************
DOCKER_SSH_KEY_PATH=~/.ssh/docker_host_key
```

## 方法二：TCP + TLS 连接（生产环境推荐）

### 2.1 远程主机配置

#### 步骤 1：生成 TLS 证书

在远程主机上执行：

```bash
# 创建证书目录
mkdir -p /etc/docker/certs

# 生成 CA 私钥
openssl genrsa -aes256 -out ca-key.pem 4096

# 生成 CA 证书
openssl req -new -x509 -days 365 -key ca-key.pem -sha256 -out ca.pem

# 生成服务器私钥
openssl genrsa -out server-key.pem 4096

# 生成服务器证书签名请求
openssl req -subj "/CN=远程主机IP" -sha256 -new -key server-key.pem -out server.csr

# 生成服务器证书
echo subjectAltName = DNS:远程主机域名,IP:远程主机IP,IP:127.0.0.1 >> extfile.cnf
echo extendedKeyUsage = serverAuth >> extfile.cnf
openssl x509 -req -days 365 -sha256 -in server.csr -CA ca.pem -CAkey ca-key.pem -out server-cert.pem -extfile extfile.cnf -CAcreateserial

# 生成客户端私钥
openssl genrsa -out key.pem 4096

# 生成客户端证书签名请求
openssl req -subj '/CN=client' -new -key key.pem -out client.csr

# 生成客户端证书
echo extendedKeyUsage = clientAuth > extfile-client.cnf
openssl x509 -req -days 365 -sha256 -in client.csr -CA ca.pem -CAkey ca-key.pem -out cert.pem -extfile extfile-client.cnf -CAcreateserial

# 设置权限
chmod -v 0400 ca-key.pem key.pem server-key.pem
chmod -v 0444 ca.pem server-cert.pem cert.pem

# 清理临时文件
rm -v client.csr server.csr extfile.cnf extfile-client.cnf
```

#### 步骤 2：配置 Docker 守护进程

编辑 `/etc/docker/daemon.json`：

```json
{
  "hosts": [
    "unix:///var/run/docker.sock",
    "tcp://0.0.0.0:2376"
  ],
  "tls": true,
  "tlscert": "/etc/docker/certs/server-cert.pem",
  "tlskey": "/etc/docker/certs/server-key.pem",
  "tlsverify": true,
  "tlscacert": "/etc/docker/certs/ca.pem"
}
```

重启 Docker 服务：
```bash
systemctl restart docker
```

### 2.2 客户端配置

#### 步骤 1：复制证书到本地

```bash
# 创建本地证书目录
mkdir -p ./docker-certs

# 从远程主机复制证书
scp root@远程主机IP:/etc/docker/certs/ca.pem ./docker-certs/
scp root@远程主机IP:/etc/docker/certs/cert.pem ./docker-certs/
scp root@远程主机IP:/etc/docker/certs/key.pem ./docker-certs/
```

#### 步骤 2：配置环境变量

```bash
# 在 .env.development 中设置
DOCKER_HOST=tcp://*************:2376
DOCKER_TLS_VERIFY=1
DOCKER_CERT_PATH=./docker-certs
```

#### 步骤 3：测试连接

```bash
# 设置环境变量
export DOCKER_HOST=tcp://*************:2376
export DOCKER_TLS_VERIFY=1
export DOCKER_CERT_PATH=./docker-certs

# 测试连接
docker version
```

## 方法三：TCP 连接（仅开发环境）

⚠️ **警告**：此方法不安全，仅适用于隔离的开发环境。

### 3.1 远程主机配置

编辑 `/etc/docker/daemon.json`：

```json
{
  "hosts": [
    "unix:///var/run/docker.sock",
    "tcp://0.0.0.0:2375"
  ]
}
```

重启 Docker 服务：
```bash
systemctl restart docker
```

### 3.2 客户端配置

```bash
# 在 .env.development 中设置
DOCKER_HOST=tcp://*************:2375
DOCKER_TLS_VERIFY=0
```

## 环境变量配置示例

### 开发环境配置

```bash
# SSH 连接（推荐）
DOCKER_HOST=ssh://root@*************
DOCKER_SSH_KEY_PATH=~/.ssh/id_rsa

# 或 TCP 连接（不安全）
# DOCKER_HOST=tcp://*************:2375
# DOCKER_TLS_VERIFY=0
```

### 生产环境配置

```bash
# TLS 连接（推荐）
DOCKER_HOST=tcp://prod-docker-host:2376
DOCKER_TLS_VERIFY=1
DOCKER_CERT_PATH=/etc/docker/certs

# 或 SSH 连接
# DOCKER_HOST=ssh://docker-user@prod-docker-host
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查远程主机 Docker 服务状态
   ssh root@远程主机IP "systemctl status docker"
   
   # 检查端口是否开放
   telnet 远程主机IP 2376
   ```

2. **TLS 证书错误**
   ```bash
   # 验证证书
   openssl x509 -in ./docker-certs/cert.pem -text -noout
   
   # 检查证书有效期
   openssl x509 -in ./docker-certs/cert.pem -noout -dates
   ```

3. **SSH 连接失败**
   ```bash
   # 测试 SSH 连接
   ssh -v root@远程主机IP
   
   # 检查 SSH 密钥权限
   chmod 600 ~/.ssh/id_rsa
   ```

### 调试命令

```bash
# 显示详细的 Docker 客户端信息
docker version --format '{{.Client.Version}}'

# 显示 Docker 守护进程信息
docker system info

# 测试连接延迟
time docker ps
```

## 安全建议

1. **使用 SSH 或 TLS**：永远不要在生产环境使用未加密的 TCP 连接
2. **限制访问**：使用防火墙限制 Docker API 端口的访问
3. **定期更新证书**：TLS 证书应定期更新
4. **监控连接**：监控 Docker API 的访问日志
5. **最小权限原则**：为 Docker 连接创建专用用户，限制其权限

## 性能优化

1. **网络延迟**：选择网络延迟最低的连接方式
2. **连接池**：配置适当的连接超时和重试机制
3. **本地缓存**：缓存常用的 Docker 镜像到本地

## 相关配置文件

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `docker-certs/` - TLS 证书目录
- `~/.ssh/` - SSH 密钥目录
