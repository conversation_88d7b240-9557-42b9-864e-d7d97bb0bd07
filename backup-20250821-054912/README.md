# PaaS 平台前端管理界面

基于 Vue.js 3 + TypeScript 构建的现代化企业级 PaaS 平台管理控制台，提供应用管理、CI/CD、用户权限、监控等功能。

## 🚀 技术栈

- **框架**: Vue.js 3.4+ (Composition API)
- **语言**: TypeScript 5.3+
- **构建工具**: Vite 5.0+
- **UI 组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **HTTP 客户端**: Axios 1.6+
- **图表库**: ECharts 5.4+ (vue-echarts)
- **样式预处理**: SCSS
- **代码规范**: ESLint + Prettier
- **测试框架**: Vitest + Vue Test Utils
- **包管理器**: npm/yarn/pnpm

## 📁 项目结构

```
web/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口定义
│   │   ├── auth.ts        # 认证相关 API
│   │   ├── apps.ts        # 应用管理 API
│   │   └── cicd.ts        # CI/CD 相关 API
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   │   ├── common/        # 通用组件
│   │   └── layout/        # 布局组件
│   ├── layouts/           # 页面布局
│   │   └── MainLayout.vue # 主布局
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   │   └── user.ts        # 用户状态
│   ├── styles/            # 全局样式
│   │   ├── index.scss     # 主样式文件
│   │   ├── variables.scss # SCSS 变量
│   │   └── mixins.scss    # SCSS 混合宏
│   ├── types/             # TypeScript 类型定义
│   │   └── index.ts       # 通用类型
│   ├── utils/             # 工具函数
│   │   ├── auth.ts        # 认证工具
│   │   └── request.ts     # HTTP 请求封装
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── apps/          # 应用管理页面
│   │   ├── cicd/          # CI/CD 页面
│   │   ├── dashboard/     # 控制台页面
│   │   └── error/         # 错误页面
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── tests/                 # 测试文件
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目说明
```

## 🎯 核心功能

### 1. 用户认证与权限管理
- ✅ 用户登录/登出
- ✅ JWT Token 管理
- ✅ 权限验证
- ✅ 角色管理
- ✅ 路由守卫

### 2. 应用管理
- ✅ 应用列表展示
- ✅ 应用创建向导
- ✅ 应用详情查看
- ✅ 应用实例管理
- ✅ 应用扩缩容
- ✅ 应用日志查看

### 3. CI/CD 管理
- ✅ 流水线管理
- ✅ 构建历史
- ✅ 部署管理
- ✅ 构建日志查看
- ✅ 部署回滚

### 4. 监控与日志
- ✅ 应用监控图表
- ✅ 系统资源监控
- ✅ 实时日志查看
- ✅ 事件时间线

### 5. 系统管理
- ✅ 用户管理
- ✅ 角色权限配置
- ✅ 配置管理
- ✅ 密钥管理

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd web
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 代码检查

```bash
# ESLint 检查
npm run lint

# 代码格式化
npm run format

# TypeScript 类型检查
npm run type-check
```

### 运行测试

```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行测试 UI
npm run test:ui
```

## 🎨 设计系统

### 色彩规范
- **主色调**: #409EFF (蓝色)
- **成功色**: #67C23A (绿色)
- **警告色**: #E6A23C (橙色)
- **危险色**: #F56C6C (红色)
- **信息色**: #909399 (灰色)

### 字体规范
- **主字体**: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif
- **等宽字体**: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace

### 间距规范
- **基础间距**: 4px 的倍数 (4px, 8px, 12px, 16px, 20px, 24px...)
- **组件间距**: 通常使用 16px 或 20px
- **页面边距**: 通常使用 20px 或 24px

## 📱 响应式设计

项目采用移动优先的响应式设计策略：

- **手机端**: < 768px
- **平板端**: 768px - 1200px  
- **桌面端**: > 1200px

## 🔧 配置说明

### 环境变量

创建 `.env.local` 文件配置本地环境变量：

```bash
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080

# 应用标题
VITE_APP_TITLE=PaaS 管理平台

# 是否启用 Mock 数据
VITE_USE_MOCK=false
```

### 代理配置

开发环境下，API 请求会自动代理到后端服务：

```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true
    }
  }
}
```

## 🧪 测试策略

### 单元测试
- 组件测试：测试组件的渲染和交互
- 工具函数测试：测试纯函数的输入输出
- Store 测试：测试状态管理逻辑

### 测试覆盖率目标
- 语句覆盖率: > 80%
- 分支覆盖率: > 75%
- 函数覆盖率: > 80%
- 行覆盖率: > 80%

## 📦 部署指南

### Docker 部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx 配置

```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 变量和函数名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### 提交信息规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙋‍♂️ 支持

如果您在使用过程中遇到问题，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

---

**注意**: 这是一个演示项目，用于展示现代化前端开发的最佳实践。在生产环境中使用前，请确保进行充分的测试和安全评估。
