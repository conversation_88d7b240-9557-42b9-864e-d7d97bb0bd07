#!/bin/bash

# 配置服务启动脚本
# 用途: 启动配置服务及其依赖服务

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
SERVICE_NAME="config-service"
LOG_FILE="${PROJECT_ROOT}/logs/${SERVICE_NAME}.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装，请先安装 Go 1.24+"
        exit 1
    fi
    
    # 检查 Go 版本
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    if [[ "$(printf '%s\n' "1.24" "$GO_VERSION" | sort -V | head -n1)" != "1.24" ]]; then
        log_error "Go 版本过低，需要 1.24+，当前版本: $GO_VERSION"
        exit 1
    fi
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装: $(docker --version)"
    else
        log_warn "Docker 未安装，将使用本地模式运行"
    fi
    
    # 检查 PostgreSQL (可选)
    if command -v psql &> /dev/null; then
        log_info "PostgreSQL 客户端已安装"
    else
        log_warn "PostgreSQL 客户端未安装，将使用 SQLite"
    fi
    
    # 检查 Redis (可选)
    if command -v redis-cli &> /dev/null; then
        log_info "Redis 客户端已安装"
    else
        log_warn "Redis 客户端未安装，缓存功能可能受限"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p "${PROJECT_ROOT}/logs"
    mkdir -p "${PROJECT_ROOT}/data"
    mkdir -p "${PROJECT_ROOT}/tmp"
    
    log_info "目录创建完成"
}

# 构建服务
build_service() {
    log_info "构建配置服务..."
    
    cd "${PROJECT_ROOT}"
    
    # 下载依赖
    log_info "下载 Go 依赖..."
    go mod download
    go mod tidy
    
    # 构建二进制文件
    log_info "编译配置服务..."
    CGO_ENABLED=0 go build -ldflags="-w -s" -o "bin/${SERVICE_NAME}" "./cmd/${SERVICE_NAME}"
    
    if [[ ! -f "bin/${SERVICE_NAME}" ]]; then
        log_error "构建失败，二进制文件不存在"
        exit 1
    fi
    
    log_info "构建完成: bin/${SERVICE_NAME}"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 检查是否有初始化脚本
    if [[ -f "${PROJECT_ROOT}/scripts/init-db.sql" ]]; then
        log_info "发现数据库初始化脚本"
        
        # 如果有 PostgreSQL，尝试初始化
        if command -v psql &> /dev/null; then
            log_info "使用 PostgreSQL 初始化数据库"
            # 这里可以添加 PostgreSQL 初始化逻辑
        else
            log_info "使用 SQLite，无需手动初始化"
        fi
    fi
}

# 启动依赖服务
start_dependencies() {
    log_info "启动依赖服务..."
    
    # 检查是否使用 Docker Compose
    if [[ -f "${PROJECT_ROOT}/deployments/docker/docker-compose.config-service.yml" ]] && command -v docker-compose &> /dev/null; then
        log_info "使用 Docker Compose 启动依赖服务..."
        
        cd "${PROJECT_ROOT}/deployments/docker"
        docker-compose -f docker-compose.config-service.yml up -d postgres redis
        
        # 等待服务启动
        log_info "等待依赖服务启动..."
        sleep 10
        
        # 检查服务状态
        if docker-compose -f docker-compose.config-service.yml ps | grep -q "Up"; then
            log_info "依赖服务启动成功"
        else
            log_warn "依赖服务可能未完全启动，请检查"
        fi
    else
        log_warn "未找到 Docker Compose 文件或 Docker Compose 未安装，跳过依赖服务启动"
    fi
}

# 启动配置服务
start_service() {
    log_info "启动配置服务..."
    
    cd "${PROJECT_ROOT}"
    
    # 检查端口是否被占用
    if lsof -Pi :8083 -sTCP:LISTEN -t >/dev/null; then
        log_error "端口 8083 已被占用，请先停止占用该端口的进程"
        exit 1
    fi

    # 设置环境变量
    export LOG_LEVEL=${LOG_LEVEL:-info}
    export LOG_FORMAT=${LOG_FORMAT:-json}
    export PORT=${PORT:-8083}

    # 启动服务
    log_info "启动配置服务在端口 8083..."
    
    if [[ "${DAEMON}" == "true" ]]; then
        # 后台运行
        nohup "./bin/${SERVICE_NAME}" > "${LOG_FILE}" 2>&1 &
        SERVICE_PID=$!
        echo $SERVICE_PID > "${PROJECT_ROOT}/tmp/${SERVICE_NAME}.pid"
        log_info "配置服务已在后台启动，PID: $SERVICE_PID"
        log_info "日志文件: ${LOG_FILE}"
    else
        # 前台运行
        "./bin/${SERVICE_NAME}"
    fi
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:8083/health >/dev/null 2>&1; then
        log_info "配置服务健康检查通过"

        # 显示服务信息
        echo ""
        log_info "=== 配置服务信息 ==="
        log_info "服务地址: http://localhost:8083"
        log_info "健康检查: http://localhost:8083/health"
        log_info "API 文档: http://localhost:8083/swagger"
        log_info "指标监控: http://localhost:8083/metrics"
        echo ""
        
        return 0
    else
        log_error "配置服务健康检查失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "配置服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --daemon     后台运行服务"
    echo "  -b, --build      重新构建服务"
    echo "  -c, --clean      清理构建文件"
    echo "  -s, --skip-deps  跳过依赖检查"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  LOG_LEVEL        日志级别 (debug, info, warn, error)"
    echo "  LOG_FORMAT       日志格式 (json, text)"
    echo "  PORT             服务端口 (默认: 8083)"
    echo "  DB_TYPE          数据库类型 (postgres, sqlite)"
    echo "  REDIS_ADDR       Redis 地址 (默认: localhost:6379)"
    echo ""
    echo "示例:"
    echo "  $0                    # 前台启动服务"
    echo "  $0 -d                 # 后台启动服务"
    echo "  $0 -b -d              # 重新构建并后台启动"
    echo "  LOG_LEVEL=debug $0    # 使用调试日志级别启动"
}

# 清理函数
cleanup() {
    log_info "清理构建文件..."
    rm -rf "${PROJECT_ROOT}/bin/${SERVICE_NAME}"
    rm -rf "${PROJECT_ROOT}/tmp"
    log_info "清理完成"
}

# 主函数
main() {
    local build_flag=false
    local daemon_flag=false
    local skip_deps=false
    local clean_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--daemon)
                daemon_flag=true
                shift
                ;;
            -b|--build)
                build_flag=true
                shift
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -s|--skip-deps)
                skip_deps=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    export DAEMON=$daemon_flag
    
    log_info "开始启动配置服务..."
    
    # 清理
    if [[ "$clean_flag" == "true" ]]; then
        cleanup
        exit 0
    fi
    
    # 检查依赖
    if [[ "$skip_deps" != "true" ]]; then
        check_dependencies
    fi
    
    # 创建目录
    create_directories
    
    # 构建服务
    if [[ "$build_flag" == "true" ]] || [[ ! -f "${PROJECT_ROOT}/bin/${SERVICE_NAME}" ]]; then
        build_service
    fi
    
    # 初始化数据库
    init_database
    
    # 启动依赖服务
    start_dependencies
    
    # 启动配置服务
    start_service
    
    # 检查服务状态
    if [[ "$daemon_flag" == "true" ]]; then
        check_service
        if [[ $? -eq 0 ]]; then
            log_info "配置服务启动成功！"
        else
            log_error "配置服务启动失败，请检查日志: ${LOG_FILE}"
            exit 1
        fi
    fi
}

# 信号处理
trap 'log_info "收到中断信号，正在停止服务..."; exit 0' INT TERM

# 执行主函数
main "$@"
