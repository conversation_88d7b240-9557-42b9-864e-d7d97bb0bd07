#!/bin/bash

# PaaS平台登录功能集成测试脚本
# 测试前端登录页面与后端认证API的完整交互流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 测试后端认证API
test_backend_auth_api() {
    log_test "测试后端认证API..."
    
    local base_url="http://localhost:8081"
    
    # 1. 测试健康检查
    log_test "1. 健康检查测试"
    local health_response=$(curl -s "$base_url/health" 2>/dev/null || echo "")
    
    if [ -n "$health_response" ]; then
        log_success "健康检查通过"
        echo "    响应: $(echo "$health_response" | jq -r '.status // "unknown"' 2>/dev/null || echo "解析失败")"
        
        # 检查开发模式状态
        local dev_mode=$(echo "$health_response" | jq -r '.dev_mode // false' 2>/dev/null || echo "false")
        local auth_enabled=$(echo "$health_response" | jq -r '.auth_enabled // true' 2>/dev/null || echo "true")
        
        echo "    开发模式: $dev_mode"
        echo "    认证启用: $auth_enabled"
    else
        log_error "健康检查失败 - 应用管理服务未运行"
        return 1
    fi
    
    echo
    
    # 2. 测试登录API
    log_test "2. 登录API测试"
    local login_url="$base_url/api/v1/auth/login"
    local login_data='{"username":"testuser","password":"testpass"}'
    
    local login_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$login_url" 2>/dev/null || echo "HTTP_CODE:000")
    
    local login_code=$(echo "$login_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local login_body=$(echo "$login_response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$login_code" = "200" ]; then
        log_success "登录API测试成功 (HTTP $login_code)"
        
        # 检查响应格式
        local access_token=$(echo "$login_body" | jq -r '.access_token // empty' 2>/dev/null || echo "")
        local user_id=$(echo "$login_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
        
        if [ -n "$access_token" ] && [ -n "$user_id" ]; then
            log_success "登录响应格式正确"
            echo "    用户ID: $user_id"
            echo "    Token: ${access_token:0:20}..."
        else
            log_warn "登录响应格式可能有问题"
            echo "    响应: ${login_body:0:200}..."
        fi
    elif [ "$login_code" = "401" ]; then
        log_warn "登录API返回401 (认证失败) - 这在生产模式下是正常的"
        echo "    如果是开发模式，这可能表示开发模式配置未生效"
    elif [ "$login_code" = "000" ]; then
        log_error "登录API连接失败"
        return 1
    else
        log_error "登录API测试失败 (HTTP $login_code)"
        echo "    响应: $login_body"
        return 1
    fi
    
    echo
}

# 测试前端API代理
test_frontend_proxy() {
    log_test "测试前端API代理配置..."
    
    # 检查前端是否已构建
    if [ ! -d "web/node_modules" ]; then
        log_warn "前端依赖未安装，跳过代理测试"
        return 0
    fi
    
    # 检查Vite配置
    if [ -f "web/vite.config.ts" ]; then
        local proxy_target=$(grep -A 5 "proxy:" web/vite.config.ts | grep "target:" | sed "s/.*target: *['\"]//;s/['\"].*//" || echo "")
        
        if [ -n "$proxy_target" ]; then
            log_info "前端代理配置: /api -> $proxy_target"
            
            if [ "$proxy_target" = "http://localhost:8081" ]; then
                log_success "代理配置正确 (指向应用管理服务)"
            else
                log_warn "代理配置可能需要调整"
                echo "    当前配置: $proxy_target"
                echo "    建议配置: http://localhost:8081"
            fi
        else
            log_warn "无法解析代理配置"
        fi
    else
        log_warn "Vite配置文件不存在"
    fi
    
    echo
}

# 模拟前端登录请求
simulate_frontend_login() {
    log_test "模拟前端登录请求..."
    
    local api_url="http://localhost:8081/api/v1/auth/login"
    local test_credentials='{"username":"frontend-test","password":"test123"}'
    
    # 模拟前端发送的登录请求
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -H "User-Agent: Mozilla/5.0 (Frontend Test)" \
        -H "Origin: http://localhost:3000" \
        -d "$test_credentials" \
        "$api_url" 2>/dev/null || echo "HTTP_CODE:000")
    
    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$http_code" = "200" ]; then
        log_success "前端登录请求模拟成功 (HTTP $http_code)"
        
        # 解析响应
        local access_token=$(echo "$body" | jq -r '.access_token // empty' 2>/dev/null || echo "")
        local username=$(echo "$body" | jq -r '.user.username // empty' 2>/dev/null || echo "")
        
        if [ -n "$access_token" ] && [ -n "$username" ]; then
            log_success "登录响应完整"
            echo "    用户名: $username"
            echo "    Token: ${access_token:0:30}..."
            
            # 测试Token验证
            test_token_validation "$access_token"
        else
            log_warn "登录响应不完整"
        fi
    elif [ "$http_code" = "401" ]; then
        log_warn "登录请求被拒绝 (HTTP $http_code)"
        echo "    这可能表示开发模式未正确配置"
        echo "    响应: $body"
    else
        log_error "登录请求失败 (HTTP $http_code)"
        echo "    响应: $body"
        return 1
    fi
    
    echo
}

# 测试Token验证
test_token_validation() {
    local token=$1
    
    log_test "测试Token验证..."
    
    # 使用Token访问需要认证的API
    local api_url="http://localhost:8081/api/v1/apps"
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Authorization: Bearer $token" \
        "$api_url" 2>/dev/null || echo "HTTP_CODE:000")
    
    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "404" ]; then
        log_success "Token验证成功 (HTTP $http_code)"
    elif [ "$http_code" = "401" ]; then
        log_warn "Token验证失败 (HTTP $http_code) - 可能需要检查认证中间件配置"
    else
        log_warn "Token验证返回 HTTP $http_code"
    fi
}

# 运行完整测试
run_integration_tests() {
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                  登录功能集成测试                          ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 测试后端认证API
    total_tests=$((total_tests + 1))
    if test_backend_auth_api; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 测试前端代理配置
    total_tests=$((total_tests + 1))
    if test_frontend_proxy; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 模拟前端登录
    total_tests=$((total_tests + 1))
    if simulate_frontend_login; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 显示测试结果
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        测试结果                            ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║  总测试数: $total_tests                                           ║${NC}"
    echo -e "${BLUE}║  通过: ${GREEN}$passed_tests${BLUE}                                              ║${NC}"
    echo -e "${BLUE}║  失败: ${RED}$failed_tests${BLUE}                                              ║${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${BLUE}║  状态: ${GREEN}✅ 所有测试通过${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${GREEN}前端登录功能应该正常工作${BLUE}                      ║${NC}"
    else
        echo -e "${BLUE}║  状态: ${RED}❌ 部分测试失败${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${RED}前端登录可能仍有问题${BLUE}                          ║${NC}"
    fi
    
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    return $failed_tests
}

# 显示修复建议
show_fix_suggestions() {
    echo -e "${YELLOW}🔧 登录问题修复建议：${NC}"
    echo
    echo -e "${YELLOW}1. 确保后端服务运行：${NC}"
    echo "   ./scripts/start-dev-mode.sh"
    echo
    echo -e "${YELLOW}2. 检查应用管理服务状态：${NC}"
    echo "   curl http://localhost:8081/health"
    echo
    echo -e "${YELLOW}3. 测试登录API：${NC}"
    echo '   curl -X POST -H "Content-Type: application/json" \'
    echo '        -d '"'"'{"username":"test","password":"test"}'"'"' \'
    echo '        http://localhost:8081/api/v1/auth/login'
    echo
    echo -e "${YELLOW}4. 启动前端开发服务器：${NC}"
    echo "   ./scripts/start-frontend-dev.sh"
    echo
    echo -e "${YELLOW}5. 检查浏览器开发者工具：${NC}"
    echo "   - Network面板查看API请求"
    echo "   - Console面板查看错误信息"
    echo "   - 确认代理请求到达8081端口"
    echo
    echo -e "${YELLOW}6. 开发模式登录测试：${NC}"
    echo "   - 任意用户名/密码都应该能登录成功"
    echo "   - 登录后应该获得开发用户身份"
    echo "   - 控制台应该显示开发模式警告"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🧪 PaaS平台登录功能集成测试${NC}"
    echo
    
    # 检查jq工具
    if ! command -v jq &> /dev/null; then
        log_warn "jq工具未安装，JSON解析功能受限"
        echo "安装命令: sudo apt-get install jq 或 brew install jq"
        echo
    fi
    
    # 运行集成测试
    if run_integration_tests; then
        log_success "🎉 登录功能集成测试通过！"
        echo -e "${GREEN}✅ 前端登录页面应该能正常工作${NC}"
        echo -e "${GREEN}✅ 后端认证API正常响应${NC}"
        echo -e "${GREEN}✅ 开发模式配置正确${NC}"
        echo
        echo -e "${CYAN}🚀 下一步：${NC}"
        echo -e "${CYAN}1. 启动前端: ./scripts/start-frontend-dev.sh${NC}"
        echo -e "${CYAN}2. 访问: http://localhost:3000${NC}"
        echo -e "${CYAN}3. 使用任意用户名/密码登录测试${NC}"
    else
        log_error "❌ 登录功能集成测试失败"
        echo
        show_fix_suggestions
        exit 1
    fi
}

# 执行主函数
main "$@"
