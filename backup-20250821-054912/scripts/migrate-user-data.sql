-- PaaS 平台用户数据迁移脚本
-- 将用户相关数据从 App Manager 数据库迁移到独立的 User Service 数据库
-- 
-- 执行前请确保：
-- 1. 已创建 User Service 数据库
-- 2. 已备份原始数据
-- 3. 在维护窗口期间执行

-- ============================================================================
-- 第一步：创建 User Service 数据库和表结构
-- ============================================================================

-- 创建 User Service 数据库（如果不存在）
-- CREATE DATABASE paas_users;
-- USE paas_users;

-- 🔧 用户表结构（与原 App Manager 兼容）
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL COMMENT '用户名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '用户状态：active, inactive, suspended',
    tenant_id UUID NOT NULL COMMENT '租户ID',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号码',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    failed_login_attempts INT DEFAULT 0 COMMENT '失败登录尝试次数',
    locked_until TIMESTAMP NULL COMMENT '账户锁定到期时间',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否已验证',
    two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
    preferences JSON COMMENT '用户偏好设置',
    metadata JSON COMMENT '用户元数据',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间（软删除）',
    
    -- 索引
    INDEX idx_users_email (email),
    INDEX idx_users_tenant_id (tenant_id),
    INDEX idx_users_status (status),
    INDEX idx_users_deleted_at (deleted_at),
    INDEX idx_users_last_login (last_login_at),
    INDEX idx_users_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 🔧 角色表
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    display_name VARCHAR(100) NOT NULL COMMENT '角色显示名称',
    description TEXT COMMENT '角色描述',
    tenant_id UUID NOT NULL COMMENT '租户ID',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统角色',
    permissions JSON COMMENT '角色权限列表',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    UNIQUE KEY uk_roles_name_tenant (name, tenant_id, deleted_at),
    INDEX idx_roles_tenant_id (tenant_id),
    INDEX idx_roles_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 🔧 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL COMMENT '用户ID',
    role_id UUID NOT NULL COMMENT '角色ID',
    granted_by UUID COMMENT '授权人ID',
    granted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_roles (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    INDEX idx_user_roles_user (user_id),
    INDEX idx_user_roles_role (role_id),
    INDEX idx_user_roles_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 🔧 会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL COMMENT '用户ID',
    session_id VARCHAR(128) NOT NULL UNIQUE COMMENT '会话ID',
    access_token_hash VARCHAR(255) NOT NULL COMMENT '访问令牌哈希',
    refresh_token_hash VARCHAR(255) COMMENT '刷新令牌哈希',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    device_info JSON COMMENT '设备信息',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    last_activity_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sessions_user (user_id),
    INDEX idx_sessions_expires (expires_at),
    INDEX idx_sessions_activity (last_activity_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- ============================================================================
-- 第二步：数据迁移（从 App Manager 数据库）
-- ============================================================================

-- 🔧 迁移用户基础数据
-- 注意：请根据实际的源数据库名称调整 'paas_app' 
INSERT INTO users (
    id, name, email, password_hash, status, tenant_id, 
    avatar_url, phone, last_login_at, login_count,
    created_at, updated_at, deleted_at
)
SELECT 
    id, name, email, password_hash, 
    COALESCE(status, 'active') as status,
    COALESCE(tenant_id, 'default-tenant') as tenant_id,
    avatar_url, phone, last_login_at, 
    COALESCE(login_count, 0) as login_count,
    created_at, updated_at, deleted_at
FROM paas_app.users 
WHERE deleted_at IS NULL
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    password_hash = VALUES(password_hash),
    status = VALUES(status),
    updated_at = VALUES(updated_at);

-- 🔧 迁移角色数据（如果存在）
INSERT INTO roles (
    id, name, display_name, description, tenant_id, 
    is_system, permissions, created_at, updated_at
)
SELECT 
    id, name, 
    COALESCE(display_name, name) as display_name,
    description,
    COALESCE(tenant_id, 'default-tenant') as tenant_id,
    COALESCE(is_system, FALSE) as is_system,
    permissions, created_at, updated_at
FROM paas_app.roles 
WHERE deleted_at IS NULL
ON DUPLICATE KEY UPDATE
    display_name = VALUES(display_name),
    description = VALUES(description),
    permissions = VALUES(permissions),
    updated_at = VALUES(updated_at);

-- 🔧 迁移用户角色关联数据（如果存在）
INSERT INTO user_roles (user_id, role_id, granted_at, created_at)
SELECT ur.user_id, ur.role_id, 
       COALESCE(ur.granted_at, ur.created_at) as granted_at,
       ur.created_at
FROM paas_app.user_roles ur
INNER JOIN users u ON ur.user_id = u.id
INNER JOIN roles r ON ur.role_id = r.id
ON DUPLICATE KEY UPDATE
    granted_at = VALUES(granted_at);

-- ============================================================================
-- 第三步：创建默认数据
-- ============================================================================

-- 🔧 创建默认租户（如果不存在）
INSERT IGNORE INTO roles (id, name, display_name, description, tenant_id, is_system, permissions)
VALUES 
    ('admin-role-001', 'admin', '系统管理员', '拥有所有权限的系统管理员角色', 'default-tenant', TRUE, '["*"]'),
    ('user-role-001', 'user', '普通用户', '普通用户角色', 'default-tenant', TRUE, '["read"]'),
    ('developer-role-001', 'developer', '开发者', '开发者角色，拥有开发相关权限', 'default-tenant', TRUE, '["read", "write", "deploy"]');

-- 🔧 创建默认管理员用户（如果不存在）
INSERT IGNORE INTO users (id, name, email, password_hash, status, tenant_id, email_verified)
VALUES (
    'admin-user-001', 
    '系统管理员', 
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'active',
    'default-tenant',
    TRUE
);

-- 🔧 为默认管理员分配管理员角色
INSERT IGNORE INTO user_roles (user_id, role_id)
VALUES ('admin-user-001', 'admin-role-001');

-- ============================================================================
-- 第四步：数据验证
-- ============================================================================

-- 🔍 验证迁移结果
SELECT 
    '用户数据验证' as check_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN email_verified = TRUE THEN 1 END) as verified_users
FROM users;

SELECT 
    '角色数据验证' as check_type,
    COUNT(*) as total_roles,
    COUNT(CASE WHEN is_system = TRUE THEN 1 END) as system_roles
FROM roles;

SELECT 
    '用户角色关联验证' as check_type,
    COUNT(*) as total_assignments,
    COUNT(DISTINCT user_id) as users_with_roles,
    COUNT(DISTINCT role_id) as roles_assigned
FROM user_roles;

-- 🔍 检查数据完整性
SELECT 
    '数据完整性检查' as check_type,
    (SELECT COUNT(*) FROM user_roles ur LEFT JOIN users u ON ur.user_id = u.id WHERE u.id IS NULL) as orphaned_user_roles,
    (SELECT COUNT(*) FROM user_roles ur LEFT JOIN roles r ON ur.role_id = r.id WHERE r.id IS NULL) as orphaned_role_assignments;

-- ============================================================================
-- 第五步：清理和优化
-- ============================================================================

-- 🔧 清理过期会话（如果有）
DELETE FROM user_sessions WHERE expires_at < NOW();

-- 🔧 重置失败登录计数（可选）
-- UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE locked_until < NOW();

-- 🔧 优化表
OPTIMIZE TABLE users, roles, user_roles, user_sessions;

-- ============================================================================
-- 迁移完成提示
-- ============================================================================

SELECT 
    '🎉 数据迁移完成！' as message,
    NOW() as completed_at,
    '请验证数据完整性并测试用户认证功能' as next_steps;

-- 📝 迁移后检查清单：
-- □ 验证用户可以正常登录
-- □ 验证角色权限正确分配
-- □ 验证API Gateway路由正确
-- □ 验证前端登录流程
-- □ 备份原始数据
-- □ 更新应用配置
-- □ 重启相关服务
