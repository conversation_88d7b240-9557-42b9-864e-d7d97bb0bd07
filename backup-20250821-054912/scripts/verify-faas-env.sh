#!/bin/bash

# FaaS 服务环境变量验证脚本
# 用于验证 FaaS 服务的环境变量配置是否正确

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENV_FILE="${PROJECT_ROOT}/.env.development"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查环境变量文件是否存在
check_env_file() {
    log_info "检查环境变量文件..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "环境变量文件不存在: $ENV_FILE"
        return 1
    fi
    
    log_info "✅ 环境变量文件存在: $ENV_FILE"
    return 0
}

# 加载环境变量
load_env_vars() {
    log_info "加载环境变量..."
    
    # 加载环境变量文件
    set -a
    source "$ENV_FILE"
    set +a
    
    log_info "✅ 环境变量加载完成"
}

# 验证基础配置
verify_basic_config() {
    log_info "验证 FaaS 服务基础配置..."
    
    local errors=0
    
    # 检查端口配置
    if [[ -z "$FAAS_SERVICE_PORT" ]]; then
        log_error "❌ FAAS_SERVICE_PORT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_PORT = $FAAS_SERVICE_PORT"
    fi
    
    # 检查数据库配置
    if [[ -z "$FAAS_SERVICE_DB_DSN" ]]; then
        log_error "❌ FAAS_SERVICE_DB_DSN 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_DB_DSN = $FAAS_SERVICE_DB_DSN"
    fi
    
    # 检查日志配置
    if [[ -z "$FAAS_SERVICE_LOG_LEVEL" ]]; then
        log_error "❌ FAAS_SERVICE_LOG_LEVEL 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_LOG_LEVEL = $FAAS_SERVICE_LOG_LEVEL"
    fi
    
    # 检查日志文件配置
    if [[ -z "$FAAS_SERVICE_LOG_FILE" ]]; then
        log_error "❌ FAAS_SERVICE_LOG_FILE 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_LOG_FILE = $FAAS_SERVICE_LOG_FILE"
    fi
    
    # 检查 Redis 配置
    if [[ -z "$FAAS_SERVICE_REDIS_DB" ]]; then
        log_error "❌ FAAS_SERVICE_REDIS_DB 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_REDIS_DB = $FAAS_SERVICE_REDIS_DB"
    fi
    
    # 检查性能分析端口
    if [[ -z "$FAAS_SERVICE_PPROF_PORT" ]]; then
        log_error "❌ FAAS_SERVICE_PPROF_PORT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_PPROF_PORT = $FAAS_SERVICE_PPROF_PORT"
    fi
    
    return $errors
}

# 验证执行器配置
verify_executor_config() {
    log_info "验证 FaaS 执行器配置..."
    
    local errors=0
    
    # 检查并发配置
    if [[ -z "$FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS" ]]; then
        log_error "❌ FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS = $FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS"
    fi
    
    # 检查超时配置
    if [[ -z "$FAAS_SERVICE_DEFAULT_TIMEOUT" ]]; then
        log_error "❌ FAAS_SERVICE_DEFAULT_TIMEOUT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_DEFAULT_TIMEOUT = $FAAS_SERVICE_DEFAULT_TIMEOUT"
    fi
    
    # 检查最大执行时间
    if [[ -z "$FAAS_SERVICE_MAX_EXECUTION_TIME" ]]; then
        log_error "❌ FAAS_SERVICE_MAX_EXECUTION_TIME 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_MAX_EXECUTION_TIME = $FAAS_SERVICE_MAX_EXECUTION_TIME"
    fi
    
    # 检查容器池配置
    if [[ -z "$FAAS_SERVICE_CONTAINER_POOL_SIZE" ]]; then
        log_error "❌ FAAS_SERVICE_CONTAINER_POOL_SIZE 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_CONTAINER_POOL_SIZE = $FAAS_SERVICE_CONTAINER_POOL_SIZE"
    fi
    
    return $errors
}

# 验证资源限制配置
verify_resource_config() {
    log_info "验证 FaaS 资源限制配置..."
    
    local errors=0
    
    # 检查 CPU 限制
    if [[ -z "$FAAS_SERVICE_CPU_LIMIT" ]]; then
        log_error "❌ FAAS_SERVICE_CPU_LIMIT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_CPU_LIMIT = $FAAS_SERVICE_CPU_LIMIT"
    fi
    
    # 检查内存限制
    if [[ -z "$FAAS_SERVICE_MEMORY_LIMIT" ]]; then
        log_error "❌ FAAS_SERVICE_MEMORY_LIMIT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_MEMORY_LIMIT = $FAAS_SERVICE_MEMORY_LIMIT"
    fi
    
    # 检查磁盘限制
    if [[ -z "$FAAS_SERVICE_DISK_LIMIT" ]]; then
        log_error "❌ FAAS_SERVICE_DISK_LIMIT 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_DISK_LIMIT = $FAAS_SERVICE_DISK_LIMIT"
    fi
    
    return $errors
}

# 验证存储配置
verify_storage_config() {
    log_info "验证 FaaS 存储配置..."
    
    local errors=0
    
    # 检查工作空间路径
    if [[ -z "$FAAS_SERVICE_WORKSPACE_BASE_PATH" ]]; then
        log_error "❌ FAAS_SERVICE_WORKSPACE_BASE_PATH 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_WORKSPACE_BASE_PATH = $FAAS_SERVICE_WORKSPACE_BASE_PATH"
    fi
    
    # 检查产物存储路径
    if [[ -z "$FAAS_SERVICE_ARTIFACTS_BASE_PATH" ]]; then
        log_error "❌ FAAS_SERVICE_ARTIFACTS_BASE_PATH 未配置"
        errors=$((errors + 1))
    else
        log_info "✅ FAAS_SERVICE_ARTIFACTS_BASE_PATH = $FAAS_SERVICE_ARTIFACTS_BASE_PATH"
    fi
    
    return $errors
}

# 创建必要的目录
create_directories() {
    log_info "创建 FaaS 服务必要的目录..."
    
    local dirs=(
        "$(dirname "$FAAS_SERVICE_DB_DSN")"
        "$(dirname "$FAAS_SERVICE_LOG_FILE")"
        "$FAAS_SERVICE_WORKSPACE_BASE_PATH"
        "$FAAS_SERVICE_ARTIFACTS_BASE_PATH"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        else
            log_info "✅ 目录已存在: $dir"
        fi
    done
}

# 主函数
main() {
    log_info "开始验证 FaaS 服务环境变量配置..."
    echo
    
    local total_errors=0
    
    # 检查环境变量文件
    if ! check_env_file; then
        exit 1
    fi
    echo
    
    # 加载环境变量
    load_env_vars
    echo
    
    # 验证各项配置
    verify_basic_config
    total_errors=$((total_errors + $?))
    echo
    
    verify_executor_config
    total_errors=$((total_errors + $?))
    echo
    
    verify_resource_config
    total_errors=$((total_errors + $?))
    echo
    
    verify_storage_config
    total_errors=$((total_errors + $?))
    echo
    
    # 创建必要的目录
    create_directories
    echo
    
    # 输出验证结果
    if [[ $total_errors -eq 0 ]]; then
        log_info "🎉 FaaS 服务环境变量配置验证通过！"
        log_info "所有必需的环境变量都已正确配置"
    else
        log_error "❌ FaaS 服务环境变量配置验证失败！"
        log_error "发现 $total_errors 个配置错误，请检查并修复"
        exit 1
    fi
}

# 执行主函数
main "$@"
