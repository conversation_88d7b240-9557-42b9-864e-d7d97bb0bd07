#!/bin/bash

# PaaS 平台指标收集和展示服务启动脚本
# 启动完整的指标收集、存储和展示系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"

echo -e "${PURPLE}📊 PaaS 平台指标服务启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查指标服务依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/bin"
    mkdir -p "$PROJECT_ROOT/data/metrics"
    
    print_success "目录创建完成"
}

# 函数：生成指标配置文件
generate_metrics_config() {
    print_info "生成指标配置文件..."
    
    local config_file="$CONFIG_DIR/metrics.yaml"
    
    cat > "$config_file" << EOF
# PaaS 平台指标服务配置

# 服务配置
server:
  port: 8084
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file: "$LOG_DIR/metrics.log"

# 指标收集配置
metrics:
  collection_interval: 30s
  retention_period: "7d"
  max_storage_size: 10000
  
  # 收集器配置
  collectors:
    system:
      enabled: true
      cpu: true
      memory: true
      disk: true
      network: true
      process: true
      
    application:
      enabled: true
      goroutines: true
      gc: true
      heap: true
      
    custom:
      enabled: true

# 存储配置
storage:
  type: "memory"  # memory, file, database
  
  # 内存存储配置
  memory:
    max_size: 10000
    retention_period: "7d"
    
  # 文件存储配置
  file:
    data_dir: "$PROJECT_ROOT/data/metrics"
    file_size_limit: "100MB"
    compression: true
    
  # 数据库存储配置
  database:
    host: "\${DB_HOST:localhost}"
    port: "\${DB_PORT:5432}"
    user: "\${DB_USER:postgres}"
    password: "\${DB_PASSWORD:password}"
    dbname: "\${DB_NAME:paas_metrics}"
    table: "system_metrics"

# 告警集成配置
alerting:
  enabled: true
  thresholds:
    cpu_usage: 80.0
    memory_usage: 85.0
    disk_usage: 90.0
    goroutines: 10000
    
  notification_channels:
    - "email"
    - "slack"

# 导出配置
export:
  prometheus:
    enabled: true
    endpoint: "/metrics"
    
  json:
    enabled: true
    endpoint: "/export"
    
  csv:
    enabled: true
    endpoint: "/export"

# 安全配置
security:
  api_key: "\${METRICS_API_KEY:}"
  jwt_secret: "\${JWT_SECRET:}"
  
# 限流配置
rate_limiting:
  enabled: true
  requests_per_minute: 1000
  burst_size: 100

# 缓存配置
cache:
  enabled: true
  ttl: "5m"
  max_size: 1000
EOF

    print_success "指标配置文件已生成: $config_file"
}

# 函数：构建指标服务
build_metrics_service() {
    print_info "构建指标服务..."
    
    cd "$PROJECT_ROOT"
    
    # 构建指标服务
    go build -o bin/metrics-service cmd/metrics-service/main.go
    
    if [[ ! -f "bin/metrics-service" ]]; then
        print_error "指标服务构建失败"
        exit 1
    fi
    
    print_success "指标服务构建完成"
}

# 函数：启动指标服务
start_metrics_service() {
    print_info "启动指标服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export CONFIG_FILE="$CONFIG_DIR/metrics.yaml"
    export LOG_LEVEL="info"
    export GIN_MODE="release"
    
    # 启动服务
    ./bin/metrics-service &
    METRICS_PID=$!
    
    # 等待服务启动
    print_info "等待指标服务启动..."
    sleep 10
    
    # 检查服务健康状态
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:8084/health" &> /dev/null; then
            print_success "指标服务启动成功"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "指标服务启动失败"
            return 1
        fi
        
        print_info "等待指标服务启动... ($attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
}

# 函数：测试指标功能
test_metrics_features() {
    print_info "测试指标功能..."
    
    local base_url="http://localhost:8084/api/v1/metrics"
    
    # 测试获取当前指标
    print_info "测试获取当前指标..."
    if curl -s -f "$base_url/current" > /dev/null; then
        print_success "当前指标接口正常"
    else
        print_warning "当前指标接口异常"
    fi
    
    # 测试获取指标摘要
    print_info "测试获取指标摘要..."
    if curl -s -f "$base_url/summary" > /dev/null; then
        print_success "指标摘要接口正常"
    else
        print_warning "指标摘要接口异常"
    fi
    
    # 测试添加自定义指标
    print_info "测试添加自定义指标..."
    local custom_metric='{
        "name": "test_metric",
        "value": 42.0
    }'
    
    if curl -s -f -X POST \
        -H "Content-Type: application/json" \
        -d "$custom_metric" \
        "$base_url/custom" > /dev/null; then
        print_success "自定义指标接口正常"
    else
        print_warning "自定义指标接口异常"
    fi
    
    # 测试 Prometheus 导出
    print_info "测试 Prometheus 导出..."
    if curl -s -f "$base_url/prometheus" > /dev/null; then
        print_success "Prometheus 导出接口正常"
    else
        print_warning "Prometheus 导出接口异常"
    fi
    
    print_success "指标功能测试完成"
}

# 函数：启动指标可视化面板
start_metrics_dashboard() {
    print_info "启动指标可视化面板..."
    
    # 创建简单的 HTML 面板
    local dashboard_file="$PROJECT_ROOT/web/metrics-dashboard.html"
    mkdir -p "$(dirname "$dashboard_file")"
    
    cat > "$dashboard_file" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaaS 平台指标监控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: #333; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2.5em; font-weight: bold; margin-bottom: 5px; }
        .metric-label { color: #666; font-size: 0.9em; }
        .metric-unit { font-size: 0.7em; color: #999; }
        .charts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); gap: 20px; }
        .chart-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-title { font-size: 1.2em; font-weight: bold; margin-bottom: 15px; color: #333; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 PaaS 平台指标监控</h1>
            <p>实时系统性能和资源使用监控</p>
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value status-good" id="cpu-usage">-</div>
                <div class="metric-label">CPU 使用率 <span class="metric-unit">%</span></div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-good" id="memory-usage">-</div>
                <div class="metric-label">内存使用率 <span class="metric-unit">%</span></div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-good" id="disk-usage">-</div>
                <div class="metric-label">磁盘使用率 <span class="metric-unit">%</span></div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-good" id="goroutines">-</div>
                <div class="metric-label">协程数量 <span class="metric-unit">个</span></div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-good" id="heap-alloc">-</div>
                <div class="metric-label">堆内存 <span class="metric-unit">MB</span></div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-good" id="network-io">-</div>
                <div class="metric-label">网络 I/O <span class="metric-unit">MB/s</span></div>
            </div>
        </div>
        
        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-title">系统资源使用趋势</div>
                <canvas id="system-chart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">应用性能指标</div>
                <canvas id="app-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        const systemCtx = document.getElementById('system-chart').getContext('2d');
        const systemChart = new Chart(systemCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU 使用率',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1
                }, {
                    label: '内存使用率',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1
                }, {
                    label: '磁盘使用率',
                    data: [],
                    borderColor: 'rgb(255, 205, 86)',
                    backgroundColor: 'rgba(255, 205, 86, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        const appCtx = document.getElementById('app-chart').getContext('2d');
        const appChart = new Chart(appCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '协程数量',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y'
                }, {
                    label: '堆内存 (MB)',
                    data: [],
                    borderColor: 'rgb(153, 102, 255)',
                    backgroundColor: 'rgba(153, 102, 255, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // 更新指标数据
        function updateMetrics() {
            fetch('/api/v1/metrics/current')
                .then(response => response.json())
                .then(result => {
                    const data = result.data;
                    
                    // 更新指标卡片
                    updateMetricCard('cpu-usage', data.cpu.usage_percent, '%');
                    updateMetricCard('memory-usage', data.memory.used_percent, '%');
                    updateMetricCard('disk-usage', data.disk.used_percent, '%');
                    updateMetricCard('goroutines', data.application.goroutines, '');
                    updateMetricCard('heap-alloc', (data.application.heap_alloc / 1024 / 1024).toFixed(1), 'MB');
                    
                    const networkIO = ((data.network.bytes_sent + data.network.bytes_recv) / 1024 / 1024).toFixed(2);
                    updateMetricCard('network-io', networkIO, 'MB');
                    
                    // 更新图表
                    const now = new Date().toLocaleTimeString();
                    
                    // 系统资源图表
                    systemChart.data.labels.push(now);
                    systemChart.data.datasets[0].data.push(data.cpu.usage_percent);
                    systemChart.data.datasets[1].data.push(data.memory.used_percent);
                    systemChart.data.datasets[2].data.push(data.disk.used_percent);
                    
                    // 应用性能图表
                    appChart.data.labels.push(now);
                    appChart.data.datasets[0].data.push(data.application.goroutines);
                    appChart.data.datasets[1].data.push(data.application.heap_alloc / 1024 / 1024);
                    
                    // 保持最近 20 个数据点
                    if (systemChart.data.labels.length > 20) {
                        systemChart.data.labels.shift();
                        systemChart.data.datasets.forEach(dataset => dataset.data.shift());
                        
                        appChart.data.labels.shift();
                        appChart.data.datasets.forEach(dataset => dataset.data.shift());
                    }
                    
                    systemChart.update('none');
                    appChart.update('none');
                })
                .catch(error => {
                    console.error('获取指标数据失败:', error);
                });
        }

        function updateMetricCard(id, value, unit) {
            const element = document.getElementById(id);
            element.textContent = value;
            
            // 根据值设置颜色
            element.className = 'metric-value';
            if (id.includes('usage') && parseFloat(value) > 80) {
                element.className += ' status-danger';
            } else if (id.includes('usage') && parseFloat(value) > 60) {
                element.className += ' status-warning';
            } else {
                element.className += ' status-good';
            }
        }

        function refreshData() {
            updateMetrics();
        }

        // 每 5 秒更新一次数据
        setInterval(updateMetrics, 5000);
        
        // 立即加载数据
        updateMetrics();
    </script>
</body>
</html>
EOF

    print_success "指标可视化面板已创建: $dashboard_file"
    print_info "可通过浏览器访问: http://localhost:8084/dashboard"
}

# 函数：显示服务信息
show_service_info() {
    echo ""
    echo "=================================================="
    print_success "📊 指标服务启动完成！"
    echo "=================================================="
    echo ""
    
    echo "🔗 服务地址："
    echo "  • 指标服务: http://localhost:8084"
    echo "  • 健康检查: http://localhost:8084/health"
    echo "  • 可视化面板: http://localhost:8084/dashboard"
    echo "  • Prometheus 指标: http://localhost:8084/api/v1/metrics/prometheus"
    echo ""
    
    echo "📋 API 端点："
    echo "  • 当前指标: GET /api/v1/metrics/current"
    echo "  • 历史指标: GET /api/v1/metrics/history"
    echo "  • 聚合指标: GET /api/v1/metrics/aggregated"
    echo "  • 指标摘要: GET /api/v1/metrics/summary"
    echo "  • 自定义指标: POST /api/v1/metrics/custom"
    echo "  • 导出指标: GET /api/v1/metrics/export"
    echo ""
    
    echo "📁 重要文件："
    echo "  • 配置文件: $CONFIG_DIR/metrics.yaml"
    echo "  • 日志文件: $LOG_DIR/metrics.log"
    echo "  • 可视化面板: $PROJECT_ROOT/web/metrics-dashboard.html"
    echo "  • 服务进程: PID $METRICS_PID"
    echo ""
    
    echo "🔧 管理命令："
    echo "  • 查看日志: tail -f $LOG_DIR/metrics.log"
    echo "  • 停止服务: kill $METRICS_PID"
    echo "  • 重启服务: $0 --restart"
    echo ""
}

# 函数：清理服务
cleanup_service() {
    print_info "清理指标服务..."
    
    if [[ -n "$METRICS_PID" ]]; then
        kill "$METRICS_PID" 2>/dev/null || true
        print_success "指标服务已停止"
    fi
}

# 主函数
main() {
    local restart=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --restart)
                restart=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --restart    重启服务"
                echo "  -h, --help   显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    trap cleanup_service EXIT
    
    # 如果是重启，先停止现有服务
    if [[ "$restart" == "true" ]]; then
        print_info "重启指标服务..."
        pkill -f "metrics-service" || true
        sleep 3
    fi
    
    # 执行启动流程
    check_dependencies
    create_directories
    generate_metrics_config
    build_metrics_service
    
    if start_metrics_service; then
        test_metrics_features
        start_metrics_dashboard
        show_service_info
        
        # 保持服务运行
        print_info "指标服务正在运行，按 Ctrl+C 停止..."
        wait "$METRICS_PID"
    else
        print_error "指标服务启动失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
