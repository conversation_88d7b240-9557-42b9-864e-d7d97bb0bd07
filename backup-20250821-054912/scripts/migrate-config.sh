#!/bin/bash

# 配置迁移脚本
# 将现有的 .env 和分散的 YAML 配置迁移到统一的配置文件

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
配置迁移脚本

用法: $0 [选项]

选项:
  -h, --help        显示此帮助信息
  -t, --target MODE 目标配置模式 (minimal, standard, full)
  -o, --output FILE 输出配置文件路径
  -b, --backup      备份现有配置文件
  --dry-run         仅显示迁移计划，不执行实际迁移

示例:
  $0 --target minimal --output configs/app.minimal.yaml
  $0 --target standard --backup
  $0 --dry-run

EOF
}

# 检测现有配置
detect_existing_config() {
    log_info "检测现有配置文件..."
    
    local config_files=()
    local env_files=()
    
    # 查找 YAML 配置文件
    for file in configs/*.yaml configs/*.yml; do
        if [[ -f "$file" ]]; then
            config_files+=("$file")
        fi
    done
    
    # 查找 .env 文件
    for file in .env .env.local .env.dev .env.development; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            env_files+=("$file")
        fi
    done
    
    log_info "找到 YAML 配置文件: ${config_files[*]:-无}"
    log_info "找到环境变量文件: ${env_files[*]:-无}"
    
    # 返回找到的文件
    echo "${config_files[*]} ${env_files[*]}"
}

# 分析配置需求
analyze_config_requirements() {
    local existing_files="$1"
    
    log_info "分析配置需求..."
    
    local needs_postgres=false
    local needs_redis=false
    local needs_cicd=false
    local needs_git=false
    local needs_registry=false
    
    # 分析现有配置文件
    for file in $existing_files; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            # 检查数据库需求
            if grep -q -i "postgres\|postgresql" "$PROJECT_ROOT/$file"; then
                needs_postgres=true
            fi
            
            # 检查 Redis 需求
            if grep -q -i "redis" "$PROJECT_ROOT/$file"; then
                needs_redis=true
            fi
            
            # 检查 CI/CD 需求
            if grep -q -i "cicd\|ci-cd\|gitea\|webhook" "$PROJECT_ROOT/$file"; then
                needs_cicd=true
            fi
            
            # 检查 Git 需求
            if grep -q -i "gitea\|github\|gitlab" "$PROJECT_ROOT/$file"; then
                needs_git=true
            fi
            
            # 检查镜像仓库需求
            if grep -q -i "registry\|docker.*registry" "$PROJECT_ROOT/$file"; then
                needs_registry=true
            fi
        fi
    done
    
    # 检查 docker-compose 文件
    for compose_file in docker-compose*.yml docker-compose*.yaml; do
        if [[ -f "$PROJECT_ROOT/$compose_file" ]]; then
            if grep -q "postgres:" "$PROJECT_ROOT/$compose_file"; then
                needs_postgres=true
            fi
            if grep -q "redis:" "$PROJECT_ROOT/$compose_file"; then
                needs_redis=true
            fi
            if grep -q "gitea:" "$PROJECT_ROOT/$compose_file"; then
                needs_git=true
            fi
            if grep -q "registry:" "$PROJECT_ROOT/$compose_file"; then
                needs_registry=true
            fi
        fi
    done
    
    # 推荐配置模式
    local recommended_mode="minimal"
    if [[ "$needs_postgres" == "true" || "$needs_redis" == "true" || "$needs_cicd" == "true" ]]; then
        recommended_mode="standard"
    fi
    if [[ "$needs_git" == "true" || "$needs_registry" == "true" ]]; then
        recommended_mode="full"
    fi
    
    log_info "配置需求分析结果:"
    log_info "  PostgreSQL: $needs_postgres"
    log_info "  Redis: $needs_redis"
    log_info "  CI/CD: $needs_cicd"
    log_info "  Git 集成: $needs_git"
    log_info "  镜像仓库: $needs_registry"
    log_info "  推荐模式: $recommended_mode"
    
    echo "$recommended_mode"
}

# 提取环境变量配置
extract_env_config() {
    local env_file="$1"
    local temp_file="/tmp/env_config.yaml"
    
    log_info "从 $env_file 提取配置..."
    
    cat > "$temp_file" << 'EOF'
# 从环境变量文件提取的配置
extracted_config:
EOF
    
    # 读取环境变量文件
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        [[ "$key" =~ ^#.*$ ]] && continue
        [[ -z "$key" ]] && continue
        
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
        
        # 转换常见的环境变量到 YAML 配置
        case "$key" in
            "SERVER_PORT"|"PORT")
                echo "  server_port: $value" >> "$temp_file"
                ;;
            "LOG_LEVEL")
                echo "  log_level: \"$value\"" >> "$temp_file"
                ;;
            "DATABASE_DRIVER")
                echo "  database_driver: \"$value\"" >> "$temp_file"
                ;;
            "DATABASE_DSN")
                echo "  database_dsn: \"$value\"" >> "$temp_file"
                ;;
            "REDIS_ADDR")
                echo "  redis_addr: \"$value\"" >> "$temp_file"
                ;;
            "JWT_SECRET")
                echo "  jwt_secret: \"$value\"" >> "$temp_file"
                ;;
            "DOCKER_REGISTRY")
                echo "  docker_registry: \"$value\"" >> "$temp_file"
                ;;
        esac
    done < "$PROJECT_ROOT/$env_file"
    
    echo "$temp_file"
}

# 生成新配置文件
generate_config() {
    local target_mode="$1"
    local output_file="$2"
    local existing_files="$3"
    
    log_info "生成 $target_mode 模式配置文件: $output_file"
    
    # 复制基础模板
    local template_file="$PROJECT_ROOT/configs/app.$target_mode.yaml"
    if [[ ! -f "$template_file" ]]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    cp "$template_file" "$PROJECT_ROOT/$output_file"
    
    # 提取现有配置中的自定义值
    local custom_values=()
    
    # 从环境变量文件提取配置
    for file in $existing_files; do
        if [[ "$file" =~ \.env ]]; then
            local env_config
            env_config=$(extract_env_config "$file")
            
            # 读取提取的配置并应用到新文件
            # 这里可以添加更复杂的配置合并逻辑
            log_info "已提取 $file 中的配置"
        fi
    done
    
    log_success "配置文件生成完成: $output_file"
}

# 备份现有配置
backup_existing_config() {
    local backup_dir="$PROJECT_ROOT/configs/backup-$(date +%Y%m%d-%H%M%S)"
    
    log_info "备份现有配置到: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    if [[ -d "$PROJECT_ROOT/configs" ]]; then
        cp -r "$PROJECT_ROOT/configs"/* "$backup_dir/" 2>/dev/null || true
    fi
    
    # 备份环境变量文件
    for env_file in .env .env.local .env.dev .env.development; do
        if [[ -f "$PROJECT_ROOT/$env_file" ]]; then
            cp "$PROJECT_ROOT/$env_file" "$backup_dir/"
        fi
    done
    
    # 备份 docker-compose 文件
    for compose_file in docker-compose*.yml docker-compose*.yaml; do
        if [[ -f "$PROJECT_ROOT/$compose_file" ]]; then
            cp "$PROJECT_ROOT/$compose_file" "$backup_dir/"
        fi
    done
    
    log_success "配置备份完成: $backup_dir"
}

# 显示迁移计划
show_migration_plan() {
    local target_mode="$1"
    local output_file="$2"
    local existing_files="$3"
    
    log_info "迁移计划:"
    echo
    echo "  目标模式: $target_mode"
    echo "  输出文件: $output_file"
    echo "  现有配置文件:"
    for file in $existing_files; do
        echo "    - $file"
    done
    echo
    echo "  迁移步骤:"
    echo "    1. 备份现有配置文件"
    echo "    2. 分析配置需求"
    echo "    3. 生成新的统一配置文件"
    echo "    4. 验证配置文件有效性"
    echo "    5. 更新启动脚本"
    echo
}

# 主函数
main() {
    local target_mode=""
    local output_file=""
    local backup=false
    local dry_run=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--target)
                target_mode="$2"
                shift 2
                ;;
            -o|--output)
                output_file="$2"
                shift 2
                ;;
            -b|--backup)
                backup=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检测现有配置
    local existing_files
    existing_files=$(detect_existing_config)
    
    if [[ -z "$existing_files" ]]; then
        log_warning "未找到现有配置文件，将创建默认配置"
        existing_files=""
    fi
    
    # 分析配置需求并推荐模式
    local recommended_mode
    recommended_mode=$(analyze_config_requirements "$existing_files")
    
    # 设置默认值
    if [[ -z "$target_mode" ]]; then
        target_mode="$recommended_mode"
        log_info "使用推荐的配置模式: $target_mode"
    fi
    
    if [[ -z "$output_file" ]]; then
        output_file="configs/app.$target_mode.yaml"
    fi
    
    # 显示迁移计划
    show_migration_plan "$target_mode" "$output_file" "$existing_files"
    
    # 如果是试运行，只显示计划
    if [[ "$dry_run" == "true" ]]; then
        log_info "试运行模式，不执行实际迁移"
        exit 0
    fi
    
    # 确认执行
    read -p "是否继续执行迁移？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "迁移已取消"
        exit 0
    fi
    
    # 执行迁移
    if [[ "$backup" == "true" ]]; then
        backup_existing_config
    fi
    
    generate_config "$target_mode" "$output_file" "$existing_files"
    
    # 验证生成的配置文件
    if [[ -f "$PROJECT_ROOT/$output_file" ]]; then
        log_success "配置迁移完成！"
        log_info "新配置文件: $output_file"
        log_info "使用以下命令启动服务:"
        log_info "  ./scripts/start-local-dev.sh start -c $output_file"
    else
        log_error "配置文件生成失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
