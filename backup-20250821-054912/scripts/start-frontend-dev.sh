#!/bin/bash

# PaaS平台前端开发环境启动脚本
# 此脚本用于启动前端开发服务器，并确保与后端服务正确连接

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示开发模式说明
show_dev_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                  前端开发环境启动                          ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🌐 前端开发服务器: http://localhost:3000                 ║${NC}"
    echo -e "${CYAN}║  🔗 API代理目标: http://localhost:8081 (应用管理服务)      ║${NC}"
    echo -e "${CYAN}║  🔓 开发模式: 认证已简化，支持开发用户登录                ║${NC}"
    echo -e "${CYAN}║  📱 登录测试: 任意用户名/密码都可以登录                   ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查必要工具
check_prerequisites() {
    log_info "检查必要工具..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 16或更高版本"
        exit 1
    fi
    
    # 检查npm或yarn
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
    else
        log_error "npm或yarn未安装，请先安装包管理器"
        exit 1
    fi
    
    log_info "使用包管理器: $PACKAGE_MANAGER"
    log_info "Node.js版本: $(node --version)"
}

# 检查后端服务状态
check_backend_services() {
    log_info "检查后端服务状态..."
    
    # 检查应用管理服务 (包含认证API)
    if curl -s http://localhost:8081/health > /dev/null 2>&1; then
        log_info "✅ 应用管理服务 (8081) 运行正常"
        
        # 检查认证API
        local auth_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
            -H "Content-Type: application/json" \
            -d '{"username":"test","password":"test"}' \
            http://localhost:8081/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
        
        local auth_code=$(echo "$auth_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
        
        if [ "$auth_code" = "200" ] || [ "$auth_code" = "401" ]; then
            log_info "✅ 认证API (/api/v1/auth/login) 可访问"
        else
            log_warn "⚠️  认证API响应异常 (HTTP $auth_code)"
        fi
    else
        log_error "❌ 应用管理服务 (8081) 未运行"
        log_error "请先启动后端服务: ./scripts/start-dev-mode.sh"
        exit 1
    fi
    
    # 检查其他服务 (可选)
    local other_services=(
        "脚本执行服务:8084"
        "CI/CD服务:8082"
        "配置服务:8083"
    )
    
    for service_info in "${other_services[@]}"; do
        IFS=':' read -r service_name port <<< "$service_info"
        
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            log_info "✅ ${service_name} ($port) 运行正常"
        else
            log_warn "⚠️  ${service_name} ($port) 未运行"
        fi
    done
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装前端依赖..."
    
    cd web
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json文件不存在"
        exit 1
    fi
    
    # 检查node_modules是否存在
    if [ ! -d "node_modules" ]; then
        log_info "安装依赖包..."
        $PACKAGE_MANAGER install
    else
        log_info "依赖包已存在，跳过安装"
    fi
    
    cd ..
}

# 启动前端开发服务器
start_frontend_dev() {
    log_info "启动前端开发服务器..."
    
    cd web
    
    # 设置开发环境变量
    export NODE_ENV=development
    export VITE_API_BASE_URL=http://localhost:8081
    export VITE_DEV_MODE=true
    
    # 启动开发服务器
    log_info "正在启动Vite开发服务器..."
    log_info "前端地址: http://localhost:3000"
    log_info "API代理: /api -> http://localhost:8081"
    
    # 使用exec替换当前进程，这样Ctrl+C可以正确终止
    exec $PACKAGE_MANAGER run dev
}

# 显示使用说明
show_usage_info() {
    echo
    log_info "前端开发环境使用说明："
    echo -e "${CYAN}• 🌐 前端地址: http://localhost:3000${NC}"
    echo -e "${CYAN}• 🔗 API代理: /api -> http://localhost:8081${NC}"
    echo -e "${CYAN}• 🔓 开发登录: 任意用户名/密码都可以登录${NC}"
    echo -e "${CYAN}• 🧑‍💻 开发用户: 自动获得管理员权限${NC}"
    echo -e "${CYAN}• 🛑 停止服务: Ctrl+C${NC}"
    echo
    echo -e "${YELLOW}⚠️  注意：${NC}"
    echo -e "${YELLOW}• 确保后端服务已启动 (./scripts/start-dev-mode.sh)${NC}"
    echo -e "${YELLOW}• 开发模式下认证已简化，仅适用于开发环境${NC}"
    echo -e "${YELLOW}• 浏览器开发者工具可查看API请求详情${NC}"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🌐 启动PaaS平台前端开发环境${NC}"
    echo
    
    # 显示开发模式说明
    show_dev_info
    
    # 检查必要工具
    check_prerequisites
    
    # 检查后端服务
    check_backend_services
    
    # 安装依赖
    install_dependencies
    
    # 显示使用说明
    show_usage_info
    
    # 启动前端开发服务器
    start_frontend_dev
}

# 执行主函数
main "$@"
