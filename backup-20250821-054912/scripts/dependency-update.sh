#!/bin/bash

# PaaS 平台依赖库更新和维护脚本
# 更新过时的依赖库，修复安全漏洞，保持技术栈的现代化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$PROJECT_ROOT/dependency-reports"

echo -e "${BLUE}📦 PaaS 平台依赖库更新和维护脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖管理工具
check_dependency_tools() {
    print_info "检查依赖管理工具..."
    
    local missing_tools=()
    
    # 检查基础工具
    for tool in curl jq git; do
        if ! command -v $tool &> /dev/null; then
            missing_tools+=($tool)
        fi
    done
    
    # 检查 Go 工具
    if ! command -v go &> /dev/null; then
        missing_tools+=("go")
    fi
    
    # 检查 go-mod-outdated
    if ! command -v go-mod-outdated &> /dev/null; then
        print_info "安装 go-mod-outdated..."
        go install github.com/psampaz/go-mod-outdated@latest
    fi
    
    # 检查 govulncheck
    if ! command -v govulncheck &> /dev/null; then
        print_info "安装 govulncheck..."
        go install golang.org/x/vuln/cmd/govulncheck@latest
    fi
    
    # 检查 Node.js 工具（如果有前端项目）
    if [[ -d "$PROJECT_ROOT/web" ]] && [[ -f "$PROJECT_ROOT/web/package.json" ]]; then
        if ! command -v npm &> /dev/null; then
            missing_tools+=("npm")
        fi
        
        # 检查 npm-check-updates
        if ! command -v ncu &> /dev/null; then
            print_info "安装 npm-check-updates..."
            npm install -g npm-check-updates
        fi
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    print_success "依赖管理工具检查完成"
}

# 函数：创建报告目录
setup_reports_directory() {
    print_info "创建依赖报告目录..."
    
    mkdir -p "$REPORTS_DIR"
    rm -f "$REPORTS_DIR"/*.txt
    rm -f "$REPORTS_DIR"/*.json
    rm -f "$REPORTS_DIR"/*.md
    
    print_success "依赖报告目录准备完成"
}

# 函数：分析 Go 依赖
analyze_go_dependencies() {
    print_info "分析 Go 依赖..."
    
    cd "$PROJECT_ROOT"
    
    local go_deps_report="$REPORTS_DIR/go-dependencies.txt"
    
    echo "# Go 依赖分析报告" > "$go_deps_report"
    echo "生成时间: $(date)" >> "$go_deps_report"
    echo "" >> "$go_deps_report"
    
    # 列出当前依赖
    print_info "列出当前 Go 依赖..."
    echo "## 当前依赖列表" >> "$go_deps_report"
    go list -m all >> "$go_deps_report"
    echo "" >> "$go_deps_report"
    
    # 检查过时的依赖
    print_info "检查过时的 Go 依赖..."
    echo "## 过时依赖检查" >> "$go_deps_report"
    
    if go list -u -m all | grep '\[' > /dev/null; then
        go list -u -m all | grep '\[' >> "$go_deps_report"
    else
        echo "未发现过时的依赖" >> "$go_deps_report"
    fi
    
    echo "" >> "$go_deps_report"
    
    # 使用 go-mod-outdated 进行详细分析
    print_info "运行 go-mod-outdated 分析..."
    echo "## 详细过时依赖分析" >> "$go_deps_report"
    
    if go list -u -m -json all | go-mod-outdated -update -direct >> "$go_deps_report" 2>&1; then
        print_success "go-mod-outdated 分析完成"
    else
        print_warning "go-mod-outdated 分析可能不完整"
    fi
    
    echo "" >> "$go_deps_report"
    
    # 检查依赖漏洞
    print_info "检查 Go 依赖漏洞..."
    echo "## 依赖漏洞检查" >> "$go_deps_report"
    
    if govulncheck ./... >> "$go_deps_report" 2>&1; then
        print_success "未发现已知漏洞"
    else
        print_warning "发现依赖漏洞"
    fi
    
    print_info "报告已保存: $go_deps_report"
}

# 函数：分析前端依赖
analyze_frontend_dependencies() {
    if [[ ! -d "$PROJECT_ROOT/web" ]] || [[ ! -f "$PROJECT_ROOT/web/package.json" ]]; then
        print_info "未找到前端项目，跳过前端依赖分析"
        return 0
    fi
    
    print_info "分析前端依赖..."
    
    cd "$PROJECT_ROOT/web"
    
    local frontend_deps_report="$REPORTS_DIR/frontend-dependencies.txt"
    
    echo "# 前端依赖分析报告" > "$frontend_deps_report"
    echo "生成时间: $(date)" >> "$frontend_deps_report"
    echo "" >> "$frontend_deps_report"
    
    # 列出当前依赖
    print_info "列出当前前端依赖..."
    echo "## 当前依赖列表" >> "$frontend_deps_report"
    npm list --depth=0 >> "$frontend_deps_report" 2>&1 || true
    echo "" >> "$frontend_deps_report"
    
    # 检查过时的依赖
    print_info "检查过时的前端依赖..."
    echo "## 过时依赖检查" >> "$frontend_deps_report"
    
    npm outdated >> "$frontend_deps_report" 2>&1 || echo "未发现过时的依赖" >> "$frontend_deps_report"
    echo "" >> "$frontend_deps_report"
    
    # 使用 npm-check-updates 进行详细分析
    print_info "运行 npm-check-updates 分析..."
    echo "## 详细过时依赖分析" >> "$frontend_deps_report"
    
    ncu >> "$frontend_deps_report" 2>&1 || echo "ncu 分析失败" >> "$frontend_deps_report"
    echo "" >> "$frontend_deps_report"
    
    # 检查安全漏洞
    print_info "检查前端依赖安全漏洞..."
    echo "## 安全漏洞检查" >> "$frontend_deps_report"
    
    npm audit >> "$frontend_deps_report" 2>&1 || echo "npm audit 检查完成" >> "$frontend_deps_report"
    
    cd "$PROJECT_ROOT"
    print_info "报告已保存: $frontend_deps_report"
}

# 函数：分析 Docker 依赖
analyze_docker_dependencies() {
    print_info "分析 Docker 依赖..."
    
    local docker_deps_report="$REPORTS_DIR/docker-dependencies.txt"
    
    echo "# Docker 依赖分析报告" > "$docker_deps_report"
    echo "生成时间: $(date)" >> "$docker_deps_report"
    echo "" >> "$docker_deps_report"
    
    # 检查 Dockerfile
    if [[ -f "$PROJECT_ROOT/Dockerfile" ]]; then
        echo "## Dockerfile 基础镜像检查" >> "$docker_deps_report"
        
        local base_images=$(grep -E "^FROM" "$PROJECT_ROOT/Dockerfile" | awk '{print $2}')
        
        for image in $base_images; do
            echo "基础镜像: $image" >> "$docker_deps_report"
            
            # 检查镜像是否有更新版本
            if command -v docker &> /dev/null; then
                echo "  检查镜像更新..." >> "$docker_deps_report"
                docker pull "$image" >> "$docker_deps_report" 2>&1 || echo "  无法拉取镜像" >> "$docker_deps_report"
            fi
        done
        
        echo "" >> "$docker_deps_report"
    fi
    
    # 检查 docker-compose 文件
    local compose_files=(
        "docker-compose.yml"
        "docker-compose.yaml"
        "docker-compose.dev.yml"
        "docker-compose.prod.yml"
        "docker-compose.monitoring.yml"
        "docker-compose.logging.yml"
    )
    
    for compose_file in "${compose_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$compose_file" ]]; then
            echo "## $compose_file 镜像检查" >> "$docker_deps_report"
            
            # 提取镜像名称
            local images=$(grep -E "image:" "$PROJECT_ROOT/$compose_file" | awk '{print $2}' | tr -d '"')
            
            for image in $images; do
                echo "服务镜像: $image" >> "$docker_deps_report"
                
                # 检查是否使用了 latest 标签
                if [[ "$image" == *":latest" ]] || [[ "$image" != *":"* ]]; then
                    echo "  ⚠️  警告: 使用了 latest 标签或未指定版本" >> "$docker_deps_report"
                fi
            done
            
            echo "" >> "$docker_deps_report"
        fi
    done
    
    print_info "报告已保存: $docker_deps_report"
}

# 函数：更新 Go 依赖
update_go_dependencies() {
    print_info "更新 Go 依赖..."
    
    cd "$PROJECT_ROOT"
    
    local update_log="$REPORTS_DIR/go-update.log"
    
    echo "# Go 依赖更新日志" > "$update_log"
    echo "更新时间: $(date)" >> "$update_log"
    echo "" >> "$update_log"
    
    # 备份当前 go.mod 和 go.sum
    cp go.mod go.mod.backup
    cp go.sum go.sum.backup
    
    print_info "备份 go.mod 和 go.sum 文件"
    echo "已备份 go.mod 和 go.sum 文件" >> "$update_log"
    
    # 更新所有依赖到最新版本
    print_info "更新所有 Go 依赖到最新版本..."
    echo "## 更新所有依赖" >> "$update_log"
    
    if go get -u ./... >> "$update_log" 2>&1; then
        print_success "Go 依赖更新成功"
        echo "依赖更新成功" >> "$update_log"
    else
        print_error "Go 依赖更新失败"
        echo "依赖更新失败，恢复备份文件" >> "$update_log"
        
        # 恢复备份
        mv go.mod.backup go.mod
        mv go.sum.backup go.sum
        return 1
    fi
    
    # 清理模块缓存
    print_info "清理 Go 模块缓存..."
    echo "## 清理模块缓存" >> "$update_log"
    
    go mod tidy >> "$update_log" 2>&1
    
    # 验证更新
    print_info "验证 Go 依赖更新..."
    echo "## 验证更新" >> "$update_log"
    
    if go mod verify >> "$update_log" 2>&1; then
        print_success "Go 依赖验证成功"
        echo "依赖验证成功" >> "$update_log"
        
        # 删除备份文件
        rm -f go.mod.backup go.sum.backup
    else
        print_error "Go 依赖验证失败"
        echo "依赖验证失败，恢复备份文件" >> "$update_log"
        
        # 恢复备份
        mv go.mod.backup go.mod
        mv go.sum.backup go.sum
        return 1
    fi
    
    print_info "更新日志已保存: $update_log"
}

# 函数：更新前端依赖
update_frontend_dependencies() {
    if [[ ! -d "$PROJECT_ROOT/web" ]] || [[ ! -f "$PROJECT_ROOT/web/package.json" ]]; then
        print_info "未找到前端项目，跳过前端依赖更新"
        return 0
    fi
    
    print_info "更新前端依赖..."
    
    cd "$PROJECT_ROOT/web"
    
    local update_log="$REPORTS_DIR/frontend-update.log"
    
    echo "# 前端依赖更新日志" > "$update_log"
    echo "更新时间: $(date)" >> "$update_log"
    echo "" >> "$update_log"
    
    # 备份 package.json 和 package-lock.json
    cp package.json package.json.backup
    [[ -f package-lock.json ]] && cp package-lock.json package-lock.json.backup
    [[ -f yarn.lock ]] && cp yarn.lock yarn.lock.backup
    
    print_info "备份 package.json 和锁文件"
    echo "已备份 package.json 和锁文件" >> "$update_log"
    
    # 使用 npm-check-updates 更新依赖
    print_info "使用 ncu 更新前端依赖..."
    echo "## 使用 ncu 更新依赖" >> "$update_log"
    
    if ncu -u >> "$update_log" 2>&1; then
        print_success "package.json 更新成功"
        echo "package.json 更新成功" >> "$update_log"
    else
        print_error "package.json 更新失败"
        echo "package.json 更新失败" >> "$update_log"
        
        # 恢复备份
        mv package.json.backup package.json
        return 1
    fi
    
    # 安装更新的依赖
    print_info "安装更新的前端依赖..."
    echo "## 安装更新的依赖" >> "$update_log"
    
    if npm install >> "$update_log" 2>&1; then
        print_success "前端依赖安装成功"
        echo "依赖安装成功" >> "$update_log"
        
        # 删除备份文件
        rm -f package.json.backup package-lock.json.backup yarn.lock.backup
    else
        print_error "前端依赖安装失败"
        echo "依赖安装失败，恢复备份文件" >> "$update_log"
        
        # 恢复备份
        mv package.json.backup package.json
        [[ -f package-lock.json.backup ]] && mv package-lock.json.backup package-lock.json
        [[ -f yarn.lock.backup ]] && mv yarn.lock.backup yarn.lock
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    print_info "更新日志已保存: $update_log"
}

# 函数：生成依赖更新建议
generate_dependency_recommendations() {
    print_info "生成依赖更新建议..."
    
    local recommendations_file="$REPORTS_DIR/dependency-recommendations.md"
    
    cat > "$recommendations_file" << 'EOF'
# 依赖库更新和维护建议

## 📦 依赖管理策略

### 🔄 更新频率
1. **安全更新** - 立即更新（包含安全修复的版本）
2. **主要版本** - 每季度评估一次
3. **次要版本** - 每月更新一次
4. **补丁版本** - 每周更新一次

### 🎯 更新优先级
1. **高优先级** - 安全漏洞修复、关键 bug 修复
2. **中优先级** - 性能改进、新功能
3. **低优先级** - 文档更新、代码清理

## 🛠️ Go 依赖管理

### 更新策略
```bash
# 1. 检查过时的依赖
go list -u -m all

# 2. 更新特定依赖
go get -u github.com/gin-gonic/gin

# 3. 更新所有依赖
go get -u ./...

# 4. 清理和验证
go mod tidy
go mod verify
```

### 版本管理
```bash
# 1. 使用语义化版本
go get github.com/gin-gonic/gin@v1.9.1

# 2. 使用版本范围
go get github.com/gin-gonic/gin@>=v1.9.0

# 3. 固定主要版本
go get github.com/gin-gonic/gin@v1
```

### 安全检查
```bash
# 1. 漏洞扫描
govulncheck ./...

# 2. 依赖审计
go list -json -deps ./... | nancy sleuth

# 3. 许可证检查
go-licenses check ./...
```

## 🌐 前端依赖管理

### 更新策略
```bash
# 1. 检查过时的依赖
npm outdated

# 2. 使用 ncu 检查更新
ncu

# 3. 更新依赖
ncu -u && npm install

# 4. 安全审计
npm audit
npm audit fix
```

### 版本管理
```json
{
  "dependencies": {
    "react": "^18.0.0",
    "lodash": "~4.17.21"
  },
  "devDependencies": {
    "typescript": "^4.9.0"
  }
}
```

### 安全检查
```bash
# 1. 安全审计
npm audit

# 2. 自动修复
npm audit fix

# 3. 强制修复
npm audit fix --force
```

## 🐳 Docker 依赖管理

### 镜像更新策略
```dockerfile
# 1. 使用具体版本标签
FROM node:18.17.0-alpine

# 2. 避免使用 latest
# FROM node:latest  # 不推荐

# 3. 使用多阶段构建
FROM node:18.17.0-alpine AS builder
FROM nginx:1.25.1-alpine AS runtime
```

### 安全扫描
```bash
# 1. 镜像漏洞扫描
docker scan myapp:latest

# 2. 使用 Trivy 扫描
trivy image myapp:latest

# 3. 基础镜像更新
docker pull node:18.17.0-alpine
```

## 📊 依赖监控

### 自动化工具
1. **Dependabot** - GitHub 自动依赖更新
2. **Renovate** - 多平台依赖更新
3. **Snyk** - 安全漏洞监控
4. **WhiteSource** - 许可证和安全监控

### 监控指标
- 过时依赖数量
- 安全漏洞数量
- 许可证合规性
- 依赖更新频率

## 🔧 最佳实践

### 更新流程
1. **备份当前状态** - 备份依赖文件
2. **增量更新** - 逐个或分批更新
3. **测试验证** - 运行完整测试套件
4. **回滚准备** - 准备快速回滚方案
5. **文档更新** - 更新变更日志

### 风险控制
1. **测试环境验证** - 先在测试环境更新
2. **渐进式部署** - 分阶段部署到生产环境
3. **监控告警** - 监控更新后的系统状态
4. **快速回滚** - 出现问题时快速回滚

### 团队协作
1. **更新通知** - 及时通知团队成员
2. **变更记录** - 记录重要的依赖变更
3. **知识分享** - 分享更新经验和问题
4. **定期评估** - 定期评估依赖策略

## ⚠️ 注意事项

### 更新风险
1. **破坏性变更** - 主要版本可能包含破坏性变更
2. **兼容性问题** - 新版本可能不兼容现有代码
3. **性能影响** - 新版本可能影响性能
4. **安全风险** - 新版本可能引入新的安全问题

### 缓解措施
1. **充分测试** - 更新前后进行充分测试
2. **分阶段更新** - 避免一次性更新所有依赖
3. **版本锁定** - 对关键依赖进行版本锁定
4. **回滚计划** - 准备详细的回滚计划

## 📅 维护计划

### 日常维护
- 每日检查安全告警
- 每周检查补丁更新
- 每月进行依赖审计

### 定期维护
- 每季度进行主要版本评估
- 每半年进行依赖清理
- 每年进行技术栈评估

### 应急维护
- 安全漏洞立即修复
- 关键 bug 紧急更新
- 生产问题快速响应

EOF
    
    print_success "依赖更新建议已生成: $recommendations_file"
}

# 函数：生成依赖报告
generate_dependency_report() {
    print_info "生成依赖报告..."
    
    local report_file="$REPORTS_DIR/dependency-summary.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 统计依赖信息
    local go_outdated=0
    local frontend_outdated=0
    local security_issues=0
    
    if [[ -f "$REPORTS_DIR/go-dependencies.txt" ]]; then
        go_outdated=$(grep -c '\[' "$REPORTS_DIR/go-dependencies.txt" 2>/dev/null || echo "0")
    fi
    
    if [[ -f "$REPORTS_DIR/frontend-dependencies.txt" ]]; then
        frontend_outdated=$(grep -c 'MAJOR\|MINOR\|PATCH' "$REPORTS_DIR/frontend-dependencies.txt" 2>/dev/null || echo "0")
    fi
    
    # 生成 JSON 报告
    cat > "$report_file" << EOF
{
  "dependency_summary": {
    "timestamp": "$timestamp",
    "analysis_results": {
      "go_outdated_dependencies": $go_outdated,
      "frontend_outdated_dependencies": $frontend_outdated,
      "security_issues": $security_issues,
      "total_issues": $((go_outdated + frontend_outdated + security_issues))
    },
    "reports": {
      "go_dependencies": "$REPORTS_DIR/go-dependencies.txt",
      "frontend_dependencies": "$REPORTS_DIR/frontend-dependencies.txt",
      "docker_dependencies": "$REPORTS_DIR/docker-dependencies.txt",
      "dependency_recommendations": "$REPORTS_DIR/dependency-recommendations.md"
    },
    "maintenance_score": $(( 100 - (go_outdated * 5) - (frontend_outdated * 3) - (security_issues * 10) )),
    "recommendations": [
      "定期更新依赖库",
      "监控安全漏洞",
      "使用自动化工具",
      "建立更新流程",
      "进行充分测试"
    ]
  }
}
EOF
    
    print_success "依赖报告已生成: $report_file"
}

# 函数：显示更新结果
show_update_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 依赖分析和更新完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 分析报告："
    echo "  • Go 依赖:      $REPORTS_DIR/go-dependencies.txt"
    echo "  • 前端依赖:     $REPORTS_DIR/frontend-dependencies.txt"
    echo "  • Docker 依赖:  $REPORTS_DIR/docker-dependencies.txt"
    echo "  • 更新建议:     $REPORTS_DIR/dependency-recommendations.md"
    echo "  • 总结报告:     $REPORTS_DIR/dependency-summary.json"
    echo ""
    
    if [[ -f "$REPORTS_DIR/go-update.log" ]]; then
        echo "📝 更新日志："
        echo "  • Go 更新日志:  $REPORTS_DIR/go-update.log"
    fi
    
    if [[ -f "$REPORTS_DIR/frontend-update.log" ]]; then
        echo "  • 前端更新日志: $REPORTS_DIR/frontend-update.log"
    fi
    
    echo ""
    echo "🔧 下一步操作："
    echo "  1. 查看分析报告，了解依赖状态"
    echo "  2. 按建议更新过时的依赖"
    echo "  3. 运行测试验证更新结果"
    echo "  4. 建立定期依赖维护流程"
    echo ""
}

# 主函数
main() {
    local auto_update=false
    local update_go=false
    local update_frontend=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto-update)
                auto_update=true
                shift
                ;;
            --update-go)
                update_go=true
                shift
                ;;
            --update-frontend)
                update_frontend=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --auto-update      自动更新所有依赖"
                echo "  --update-go        只更新 Go 依赖"
                echo "  --update-frontend  只更新前端依赖"
                echo "  -h, --help         显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行依赖分析和更新流程
    check_dependency_tools
    setup_reports_directory
    
    # 分析阶段
    analyze_go_dependencies
    analyze_frontend_dependencies
    analyze_docker_dependencies
    generate_dependency_recommendations
    
    # 更新阶段
    if [[ "$auto_update" == "true" ]] || [[ "$update_go" == "true" ]]; then
        update_go_dependencies
    fi
    
    if [[ "$auto_update" == "true" ]] || [[ "$update_frontend" == "true" ]]; then
        update_frontend_dependencies
    fi
    
    # 报告阶段
    generate_dependency_report
    show_update_results
    
    if [[ "$auto_update" == "true" ]] || [[ "$update_go" == "true" ]] || [[ "$update_frontend" == "true" ]]; then
        print_success "依赖更新完成，请运行测试验证结果"
    else
        print_info "分析完成，使用 --auto-update 参数执行自动更新"
    fi
}

# 脚本入口
main "$@"
