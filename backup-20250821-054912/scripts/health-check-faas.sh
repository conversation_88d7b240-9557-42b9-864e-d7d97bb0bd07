#!/bin/bash

# FaaS 服务健康检查脚本
# 用于检查 Function as a Service 执行器服务的健康状态

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_NAME="faas-service"
SERVICE_URL="http://localhost:8087"
TIMEOUT=30
VERBOSE=false

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
FaaS 服务健康检查脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -u, --url URL       指定服务 URL (默认: $SERVICE_URL)
    -t, --timeout SEC   设置超时时间 (默认: $TIMEOUT 秒)
    -v, --verbose       详细输出
    --basic             仅执行基本健康检查
    --full              执行完整健康检查（包括功能测试）
    --metrics           显示服务指标
    --container-pool    显示容器池状态

示例:
    $0                  # 执行基本健康检查
    $0 --full           # 执行完整健康检查
    $0 --metrics        # 显示服务指标
    $0 -v --full        # 详细输出的完整检查

EOF
}

# 检查服务是否可达
check_service_reachable() {
    log_info "检查服务可达性..."
    
    if curl -s -f --max-time "$TIMEOUT" "$SERVICE_URL/health" > /dev/null; then
        log_success "服务可达"
        return 0
    else
        log_error "服务不可达"
        return 1
    fi
}

# 基本健康检查
basic_health_check() {
    log_info "执行基本健康检查..."
    
    local response
    response=$(curl -s --max-time "$TIMEOUT" "$SERVICE_URL/health" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "健康检查请求失败"
        return 1
    fi
    
    log_debug "健康检查响应: $response"
    
    # 解析响应
    local status
    status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    
    if [[ "$status" == "healthy" ]]; then
        log_success "基本健康检查通过"
        
        if [[ "$VERBOSE" == "true" ]]; then
            local service_name version timestamp
            service_name=$(echo "$response" | jq -r '.service' 2>/dev/null)
            version=$(echo "$response" | jq -r '.version' 2>/dev/null)
            timestamp=$(echo "$response" | jq -r '.timestamp' 2>/dev/null)
            
            log_info "服务名称: $service_name"
            log_info "服务版本: $version"
            log_info "检查时间: $timestamp"
        fi
        
        return 0
    else
        log_error "基本健康检查失败: 状态为 $status"
        return 1
    fi
}

# 检查就绪状态
check_readiness() {
    log_info "检查服务就绪状态..."
    
    local response
    response=$(curl -s --max-time "$TIMEOUT" "$SERVICE_URL/ready" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "就绪检查请求失败"
        return 1
    fi
    
    log_debug "就绪检查响应: $response"
    
    local ready
    ready=$(echo "$response" | jq -r '.ready' 2>/dev/null)
    
    if [[ "$ready" == "true" ]]; then
        log_success "服务就绪检查通过"
        return 0
    else
        log_error "服务就绪检查失败"
        return 1
    fi
}

# 获取服务指标
get_metrics() {
    log_info "获取服务指标..."
    
    local response
    response=$(curl -s --max-time "$TIMEOUT" "$SERVICE_URL/api/v1/faas/functions/metrics" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "指标获取请求失败"
        return 1
    fi
    
    log_debug "指标响应: $response"
    
    # 解析并显示指标
    local metrics
    metrics=$(echo "$response" | jq -r '.metrics' 2>/dev/null)
    
    if [[ "$metrics" != "null" && "$metrics" != "" ]]; then
        log_success "服务指标获取成功"
        
        echo "=== FaaS 服务指标 ==="
        echo "$metrics" | jq '.' 2>/dev/null || echo "$metrics"
        echo "===================="
        
        return 0
    else
        log_error "服务指标获取失败"
        return 1
    fi
}

# 获取容器池状态
get_container_pool_stats() {
    log_info "获取容器池状态..."
    
    local response
    response=$(curl -s --max-time "$TIMEOUT" "$SERVICE_URL/api/v1/faas/functions/pool/stats" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "容器池状态获取请求失败"
        return 1
    fi
    
    log_debug "容器池状态响应: $response"
    
    # 解析并显示容器池状态
    local pool_stats
    pool_stats=$(echo "$response" | jq -r '.pool_stats' 2>/dev/null)
    
    if [[ "$pool_stats" != "null" && "$pool_stats" != "" ]]; then
        log_success "容器池状态获取成功"
        
        echo "=== 容器池状态 ==="
        echo "$pool_stats" | jq '.' 2>/dev/null || echo "$pool_stats"
        echo "================="
        
        return 0
    else
        log_error "容器池状态获取失败"
        return 1
    fi
}

# 功能测试 - 执行简单函数
test_function_execution() {
    log_info "测试函数执行功能..."
    
    # 准备测试函数请求
    local test_request
    test_request=$(cat << 'EOF'
{
    "function_id": "health-check-test",
    "function_name": "Health Check Test Function",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { return { message: 'Health check test successful', timestamp: new Date().toISOString(), input: params }; }",
    "parameters": {
        "test": true,
        "source": "health-check"
    },
    "timeout": "10s"
}
EOF
    )
    
    log_debug "测试请求: $test_request"
    
    # 发送测试请求
    local response
    response=$(curl -s --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -d "$test_request" \
        "$SERVICE_URL/api/v1/faas/functions/execute" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "函数执行测试请求失败"
        return 1
    fi
    
    log_debug "函数执行响应: $response"
    
    # 解析响应
    local status result
    status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    result=$(echo "$response" | jq -r '.result' 2>/dev/null)
    
    if [[ "$status" == "success" ]]; then
        log_success "函数执行测试通过"
        
        if [[ "$VERBOSE" == "true" ]]; then
            local execution_time
            execution_time=$(echo "$response" | jq -r '.execution_time' 2>/dev/null)
            log_info "执行时间: $execution_time"
            log_info "执行结果: $result"
        fi
        
        return 0
    else
        log_error "函数执行测试失败: 状态为 $status"
        if [[ "$VERBOSE" == "true" ]]; then
            local error_msg
            error_msg=$(echo "$response" | jq -r '.error // .message' 2>/dev/null)
            log_error "错误信息: $error_msg"
        fi
        return 1
    fi
}

# 完整健康检查
full_health_check() {
    log_info "执行完整健康检查..."
    
    local checks_passed=0
    local total_checks=5
    
    # 1. 服务可达性检查
    if check_service_reachable; then
        ((checks_passed++))
    fi
    
    # 2. 基本健康检查
    if basic_health_check; then
        ((checks_passed++))
    fi
    
    # 3. 就绪状态检查
    if check_readiness; then
        ((checks_passed++))
    fi
    
    # 4. 指标获取检查
    if get_metrics > /dev/null 2>&1; then
        ((checks_passed++))
        log_success "指标获取检查通过"
    else
        log_error "指标获取检查失败"
    fi
    
    # 5. 功能测试
    if test_function_execution; then
        ((checks_passed++))
    fi
    
    # 总结
    echo ""
    log_info "健康检查总结: $checks_passed/$total_checks 项检查通过"
    
    if [[ $checks_passed -eq $total_checks ]]; then
        log_success "所有健康检查通过，服务状态良好"
        return 0
    else
        log_error "部分健康检查失败，服务可能存在问题"
        return 1
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                SERVICE_URL="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            --basic)
                CHECK_TYPE="basic"
                shift
                ;;
            --full)
                CHECK_TYPE="full"
                shift
                ;;
            --metrics)
                CHECK_TYPE="metrics"
                shift
                ;;
            --container-pool)
                CHECK_TYPE="container-pool"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 默认值
    CHECK_TYPE="basic"
    
    # 解析参数
    parse_args "$@"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq 未安装，JSON 解析功能受限"
    fi
    
    # 显示检查信息
    log_info "FaaS 服务健康检查"
    log_info "服务 URL: $SERVICE_URL"
    log_info "超时时间: $TIMEOUT 秒"
    log_info "检查类型: $CHECK_TYPE"
    echo ""
    
    # 执行检查
    case $CHECK_TYPE in
        basic)
            if check_service_reachable && basic_health_check; then
                exit 0
            else
                exit 1
            fi
            ;;
        full)
            if full_health_check; then
                exit 0
            else
                exit 1
            fi
            ;;
        metrics)
            if get_metrics; then
                exit 0
            else
                exit 1
            fi
            ;;
        container-pool)
            if get_container_pool_stats; then
                exit 0
            else
                exit 1
            fi
            ;;
        *)
            log_error "未知检查类型: $CHECK_TYPE"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
