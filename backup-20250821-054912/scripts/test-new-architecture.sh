#!/bin/bash

# PaaS平台新认证架构测试脚本
# 测试API Gateway + User Service的完整认证流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 显示测试说明
show_test_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                  新认证架构测试                            ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🎯 测试目标: 验证API Gateway + User Service架构          ║${NC}"
    echo -e "${CYAN}║  🔀 请求流程: 前端 -> API Gateway -> User Service         ║${NC}"
    echo -e "${CYAN}║  📊 测试范围: 认证API、代理转发、错误处理                  ║${NC}"
    echo -e "${CYAN}║  ⚡ 预期结果: 所有认证功能正常工作                        ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查服务状态
check_services_status() {
    log_test "检查新架构服务状态..."
    
    local required_services=(
        "API Gateway:8080:/health"
        "User Service:8085:/health"
        "App Manager:8081:/health"
    )
    
    local all_running=true
    
    for service_info in "${required_services[@]}"; do
        IFS=':' read -r service_name port health_path <<< "$service_info"
        
        local url="http://localhost:${port}${health_path}"
        
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "✅ ${service_name} (${port}) 运行正常"
            
            # 获取服务详细信息
            local service_info=$(curl -s "$url" 2>/dev/null || echo "{}")
            local service_status=$(echo "$service_info" | jq -r '.status // "unknown"' 2>/dev/null || echo "unknown")
            local dev_mode=$(echo "$service_info" | jq -r '.dev_mode // false' 2>/dev/null || echo "false")
            
            echo "    状态: $service_status, 开发模式: $dev_mode"
        else
            log_error "❌ ${service_name} (${port}) 未运行"
            echo "    请确保服务已启动: ./scripts/migrate-to-user-service.sh migrate"
            all_running=false
        fi
    done
    
    if [ "$all_running" = false ]; then
        log_error "部分服务未运行，无法继续测试"
        return 1
    fi
    
    echo
    return 0
}

# 测试API Gateway代理功能
test_api_gateway_proxy() {
    log_test "测试API Gateway代理功能..."
    
    # 1. 测试认证路由代理
    log_test "1. 测试认证路由代理 (/api/v1/auth/login -> User Service)"
    
    local auth_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d '{"username":"gateway-test","password":"test123"}' \
        http://localhost:8080/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
    
    local auth_code=$(echo "$auth_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local auth_body=$(echo "$auth_response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$auth_code" = "200" ]; then
        log_success "✅ API Gateway认证代理成功 (HTTP $auth_code)"
        
        # 检查响应格式
        local access_token=$(echo "$auth_body" | jq -r '.access_token // empty' 2>/dev/null || echo "")
        local user_id=$(echo "$auth_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
        
        if [ -n "$access_token" ] && [ -n "$user_id" ]; then
            log_success "认证响应格式正确"
            echo "    用户ID: $user_id"
            echo "    Token: ${access_token:0:30}..."
            
            # 保存token用于后续测试
            echo "$access_token" > /tmp/test_token.txt
        else
            log_warn "认证响应格式可能有问题"
        fi
    elif [ "$auth_code" = "401" ]; then
        log_success "✅ API Gateway认证代理正常 (HTTP $auth_code - 认证失败是预期的)"
        echo "    这表明代理功能正常，只是凭据无效"
    elif [ "$auth_code" = "000" ]; then
        log_error "❌ API Gateway连接失败"
        return 1
    else
        log_warn "⚠️  API Gateway认证代理返回 HTTP $auth_code"
        echo "    响应: $auth_body"
    fi
    
    echo
    
    # 2. 测试应用管理路由代理
    log_test "2. 测试应用管理路由代理 (/api/v1/apps -> App Manager)"
    
    local apps_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        http://localhost:8080/api/v1/apps 2>/dev/null || echo "HTTP_CODE:000")
    
    local apps_code=$(echo "$apps_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$apps_code" = "200" ] || [ "$apps_code" = "401" ]; then
        log_success "✅ API Gateway应用管理代理正常 (HTTP $apps_code)"
    elif [ "$apps_code" = "000" ]; then
        log_error "❌ API Gateway应用管理代理失败"
        return 1
    else
        log_warn "⚠️  API Gateway应用管理代理返回 HTTP $apps_code"
    fi
    
    echo
    return 0
}

# 测试直接访问User Service
test_user_service_direct() {
    log_test "测试直接访问User Service..."
    
    local direct_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d '{"username":"direct-test","password":"test123"}' \
        http://localhost:8085/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
    
    local direct_code=$(echo "$direct_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local direct_body=$(echo "$direct_response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$direct_code" = "200" ]; then
        log_success "✅ User Service直接访问成功 (HTTP $direct_code)"
        
        # 验证响应结构
        local user_service_token=$(echo "$direct_body" | jq -r '.access_token // empty' 2>/dev/null || echo "")
        if [ -n "$user_service_token" ]; then
            log_success "User Service响应格式正确"
        fi
    elif [ "$direct_code" = "401" ]; then
        log_success "✅ User Service直接访问正常 (HTTP $direct_code - 认证失败是预期的)"
    elif [ "$direct_code" = "000" ]; then
        log_error "❌ User Service连接失败"
        return 1
    else
        log_warn "⚠️  User Service返回 HTTP $direct_code"
        echo "    响应: $direct_body"
    fi
    
    echo
    return 0
}

# 测试Token验证
test_token_validation() {
    log_test "测试Token验证功能..."
    
    # 检查是否有测试token
    if [ ! -f "/tmp/test_token.txt" ]; then
        log_warn "没有可用的测试Token，跳过Token验证测试"
        return 0
    fi
    
    local test_token=$(cat /tmp/test_token.txt)
    
    # 使用Token访问需要认证的API
    local protected_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Authorization: Bearer $test_token" \
        http://localhost:8080/api/v1/apps 2>/dev/null || echo "HTTP_CODE:000")
    
    local protected_code=$(echo "$protected_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$protected_code" = "200" ]; then
        log_success "✅ Token验证成功 (HTTP $protected_code)"
        echo "    认证中间件正常工作"
    elif [ "$protected_code" = "401" ]; then
        log_warn "⚠️  Token验证失败 (HTTP $protected_code)"
        echo "    可能是开发模式配置问题或Token格式问题"
    elif [ "$protected_code" = "000" ]; then
        log_error "❌ Token验证请求失败"
        return 1
    else
        log_warn "⚠️  Token验证返回 HTTP $protected_code"
    fi
    
    echo
    return 0
}

# 测试服务间通信
test_service_communication() {
    log_test "测试服务间通信..."
    
    # 测试API Gateway到User Service的通信
    log_test "1. API Gateway -> User Service通信测试"
    
    # 通过API Gateway访问User Service的健康检查
    # 注意：这需要API Gateway支持健康检查代理
    local gateway_to_user=$(curl -s -w "HTTP_CODE:%{http_code}" \
        http://localhost:8080/api/v1/auth/health 2>/dev/null || echo "HTTP_CODE:000")
    
    local comm_code=$(echo "$gateway_to_user" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$comm_code" = "200" ] || [ "$comm_code" = "404" ]; then
        log_success "✅ API Gateway到User Service通信正常"
    else
        log_warn "⚠️  API Gateway到User Service通信可能有问题 (HTTP $comm_code)"
    fi
    
    echo
    return 0
}

# 性能基准测试
test_performance_benchmark() {
    log_test "性能基准测试..."
    
    # 测试API Gateway响应时间
    log_test "1. API Gateway响应时间测试"
    
    local start_time=$(date +%s%N)
    curl -s http://localhost:8080/health > /dev/null 2>&1
    local end_time=$(date +%s%N)
    local gateway_latency=$(( (end_time - start_time) / 1000000 ))
    
    echo "    API Gateway延迟: ${gateway_latency}ms"
    
    # 测试User Service响应时间
    log_test "2. User Service响应时间测试"
    
    start_time=$(date +%s%N)
    curl -s http://localhost:8085/health > /dev/null 2>&1
    end_time=$(date +%s%N)
    local user_service_latency=$(( (end_time - start_time) / 1000000 ))
    
    echo "    User Service延迟: ${user_service_latency}ms"
    
    # 计算总延迟
    local total_latency=$((gateway_latency + user_service_latency))
    echo "    预估总延迟: ${total_latency}ms"
    
    if [ $total_latency -lt 100 ]; then
        log_success "✅ 性能表现良好 (总延迟 < 100ms)"
    elif [ $total_latency -lt 500 ]; then
        log_info "✅ 性能可接受 (总延迟 < 500ms)"
    else
        log_warn "⚠️  性能可能需要优化 (总延迟 > 500ms)"
    fi
    
    echo
}

# 运行完整测试套件
run_complete_tests() {
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    新架构完整测试                          ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    # 1. 服务状态检查
    total_tests=$((total_tests + 1))
    if check_services_status; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 2. API Gateway代理测试
    total_tests=$((total_tests + 1))
    if test_api_gateway_proxy; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 3. User Service直接访问测试
    total_tests=$((total_tests + 1))
    if test_user_service_direct; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 4. Token验证测试
    total_tests=$((total_tests + 1))
    if test_token_validation; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 5. 服务间通信测试
    total_tests=$((total_tests + 1))
    if test_service_communication; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 6. 性能基准测试
    total_tests=$((total_tests + 1))
    if test_performance_benchmark; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 显示测试结果
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        测试结果                            ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║  总测试数: $total_tests                                           ║${NC}"
    echo -e "${BLUE}║  通过: ${GREEN}$passed_tests${BLUE}                                              ║${NC}"
    echo -e "${BLUE}║  失败: ${RED}$failed_tests${BLUE}                                              ║${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${BLUE}║  状态: ${GREEN}✅ 所有测试通过${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${GREEN}新认证架构工作正常${BLUE}                            ║${NC}"
        echo -e "${BLUE}║  建议: ${GREEN}可以切换前端到新架构${BLUE}                          ║${NC}"
    elif [ $failed_tests -le 2 ]; then
        echo -e "${BLUE}║  状态: ${YELLOW}⚠️  部分测试失败${BLUE}                                ║${NC}"
        echo -e "${BLUE}║  结论: ${YELLOW}新架构基本可用，需要调优${BLUE}                      ║${NC}"
        echo -e "${BLUE}║  建议: ${YELLOW}修复问题后再切换前端${BLUE}                          ║${NC}"
    else
        echo -e "${BLUE}║  状态: ${RED}❌ 多项测试失败${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${RED}新架构存在问题${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  建议: ${RED}修复问题或回滚到原架构${BLUE}                          ║${NC}"
    fi
    
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    return $failed_tests
}

# 显示架构对比
show_architecture_comparison() {
    echo -e "${CYAN}📊 架构对比分析${NC}"
    echo
    echo -e "${YELLOW}当前架构 (修复前):${NC}"
    echo "  前端 (3000) -> App Manager (8081) -> 认证处理"
    echo "  问题: 职责混乱、强耦合、扩展困难"
    echo
    echo -e "${GREEN}新架构 (推荐):${NC}"
    echo "  前端 (3000) -> API Gateway (8080) -> User Service (8085) -> 认证处理"
    echo "                      ↓"
    echo "                 其他业务服务"
    echo "  优势: 职责清晰、松耦合、可独立扩展"
    echo
}

# 显示下一步建议
show_next_steps() {
    echo -e "${CYAN}🚀 下一步建议${NC}"
    echo
    echo -e "${GREEN}如果测试通过:${NC}"
    echo "1. 更新前端代理配置指向API Gateway (8080端口)"
    echo "2. 启动前端开发服务器测试登录功能"
    echo "3. 逐步移除App Manager中的认证功能"
    echo
    echo -e "${YELLOW}如果测试失败:${NC}"
    echo "1. 分析失败原因和错误日志"
    echo "2. 修复相关问题后重新测试"
    echo "3. 如果问题复杂，可以回滚到原架构"
    echo
    echo -e "${BLUE}命令参考:${NC}"
    echo "  # 回滚到原架构"
    echo "  ./scripts/migrate-to-user-service.sh rollback"
    echo
    echo "  # 重新测试"
    echo "  ./scripts/test-new-architecture.sh"
    echo
    echo "  # 启动前端测试"
    echo "  ./scripts/start-frontend-dev.sh"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🧪 PaaS平台新认证架构测试${NC}"
    echo
    
    # 显示测试说明
    show_test_info
    
    # 显示架构对比
    show_architecture_comparison
    
    # 检查jq工具
    if ! command -v jq &> /dev/null; then
        log_warn "jq工具未安装，JSON解析功能受限"
        echo "安装命令: sudo apt-get install jq 或 brew install jq"
        echo
    fi
    
    # 运行完整测试
    if run_complete_tests; then
        log_success "🎉 新认证架构测试全部通过！"
        echo
        echo -e "${GREEN}✅ 架构验证结果:${NC}"
        echo -e "${GREEN}• API Gateway正常工作并能正确代理请求${NC}"
        echo -e "${GREEN}• User Service独立运行并处理认证请求${NC}"
        echo -e "${GREEN}• 服务间通信正常${NC}"
        echo -e "${GREEN}• 性能表现良好${NC}"
        echo
        echo -e "${CYAN}🎯 建议: 立即切换前端到新架构${NC}"
    else
        log_error "❌ 新认证架构测试失败"
        echo
        echo -e "${RED}❌ 架构验证结果:${NC}"
        echo -e "${RED}• 部分功能存在问题${NC}"
        echo -e "${RED}• 需要进一步调试和修复${NC}"
        echo
        echo -e "${YELLOW}🔧 建议: 分析问题原因，修复后重新测试${NC}"
    fi
    
    # 显示下一步建议
    show_next_steps
    
    # 清理临时文件
    rm -f /tmp/test_token.txt
}

# 执行主函数
main "$@"
