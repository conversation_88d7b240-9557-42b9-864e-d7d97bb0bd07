#!/bin/bash
# 代码库优化结果验证脚本
# 功能：验证优化后的文档链接、脚本功能、配置文件等
# 作者：PaaS平台开发团队
# 版本：1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VERIFICATION_LOG="$PROJECT_ROOT/verification-$(date +%Y%m%d-%H%M%S).log"

# 统计变量
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$VERIFICATION_LOG"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1" | tee -a "$VERIFICATION_LOG"
    ((PASSED_CHECKS++))
}

log_failure() {
    echo -e "${RED}[❌ FAIL]${NC} $1" | tee -a "$VERIFICATION_LOG"
    ((FAILED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠️ WARN]${NC} $1" | tee -a "$VERIFICATION_LOG"
}

# 增加检查计数
increment_check() {
    ((TOTAL_CHECKS++))
}

# 检查文件是否存在
check_file_exists() {
    local file="$1"
    local description="$2"
    
    increment_check
    if [[ -f "$file" ]]; then
        log_success "$description: $file 存在"
        return 0
    else
        log_failure "$description: $file 不存在"
        return 1
    fi
}

# 检查目录是否存在
check_directory_exists() {
    local dir="$1"
    local description="$2"
    
    increment_check
    if [[ -d "$dir" ]]; then
        log_success "$description: $dir 目录存在"
        return 0
    else
        log_failure "$description: $dir 目录不存在"
        return 1
    fi
}

# 检查文件是否已删除
check_file_removed() {
    local file="$1"
    local description="$2"
    
    increment_check
    if [[ ! -f "$file" ]]; then
        log_success "$description: $file 已正确删除"
        return 0
    else
        log_failure "$description: $file 仍然存在（应该已删除）"
        return 1
    fi
}

# 检查脚本语法
check_script_syntax() {
    local script="$1"
    
    increment_check
    if [[ -f "$script" ]]; then
        if bash -n "$script" 2>/dev/null; then
            log_success "脚本语法检查: $(basename "$script") 语法正确"
            return 0
        else
            log_failure "脚本语法检查: $(basename "$script") 语法错误"
            return 1
        fi
    else
        log_warning "脚本语法检查: $script 文件不存在，跳过检查"
        return 0
    fi
}

# 检查Docker Compose配置
check_docker_compose() {
    local compose_file="$1"
    
    increment_check
    if [[ -f "$compose_file" ]]; then
        if command -v docker-compose &> /dev/null; then
            if docker-compose -f "$compose_file" config > /dev/null 2>&1; then
                log_success "Docker Compose配置: $(basename "$compose_file") 配置正确"
                return 0
            else
                log_failure "Docker Compose配置: $(basename "$compose_file") 配置错误"
                return 1
            fi
        else
            log_warning "Docker Compose配置: docker-compose 命令不可用，跳过检查"
            return 0
        fi
    else
        log_warning "Docker Compose配置: $compose_file 文件不存在，跳过检查"
        return 0
    fi
}

# 检查Markdown文档链接
check_markdown_links() {
    local md_file="$1"
    
    increment_check
    if [[ -f "$md_file" ]]; then
        local broken_links=0
        
        # 检查相对路径链接
        while IFS= read -r line; do
            if [[ "$line" =~ \[.*\]\(([^)]+)\) ]]; then
                local link="${BASH_REMATCH[1]}"
                
                # 跳过外部链接和锚点链接
                if [[ "$link" =~ ^https?:// ]] || [[ "$link" =~ ^# ]]; then
                    continue
                fi
                
                # 检查相对路径文件是否存在
                local full_path="$(dirname "$md_file")/$link"
                if [[ ! -f "$full_path" ]] && [[ ! -d "$full_path" ]]; then
                    log_failure "文档链接检查: $md_file 中的链接 $link 指向不存在的文件"
                    ((broken_links++))
                fi
            fi
        done < "$md_file"
        
        if [[ $broken_links -eq 0 ]]; then
            log_success "文档链接检查: $(basename "$md_file") 所有链接有效"
            return 0
        else
            log_failure "文档链接检查: $(basename "$md_file") 发现 $broken_links 个无效链接"
            return 1
        fi
    else
        log_warning "文档链接检查: $md_file 文件不存在，跳过检查"
        return 0
    fi
}

# 验证新目录结构
verify_directory_structure() {
    log_info "验证新目录结构..."
    
    local expected_dirs=(
        "docs/guides"
        "docs/历史修复记录"
        "scripts/archived"
    )
    
    for dir in "${expected_dirs[@]}"; do
        check_directory_exists "$dir" "新目录结构"
    done
}

# 验证README文件整合
verify_readme_integration() {
    log_info "验证README文件整合..."
    
    # 检查新的指南文件是否存在
    check_file_exists "docs/guides/cicd-service-guide.md" "CI/CD服务指南"
    check_file_exists "docs/guides/development-auth-guide.md" "开发环境认证指南"
    check_file_exists "docs/guides/idp-integration-guide.md" "身份提供商集成指南"
    
    # 检查原README文件是否已删除
    check_file_removed "README-CICD.md" "原CI/CD README"
    check_file_removed "README-DEV-AUTH.md" "原开发认证README"
    check_file_removed "README-IDP.md" "原IDP README"
    
    # 检查主README文件是否存在
    check_file_exists "README.md" "主README文件"
}

# 验证修复报告归档
verify_fix_reports_archive() {
    log_info "验证修复报告归档..."
    
    local archived_reports=(
        "docs/历史修复记录/前端Token解析循环错误修复报告.md"
        "docs/历史修复记录/前端登录页面修复报告.md"
        "docs/历史修复记录/前端加载遮罩层修复报告.md"
        "docs/历史修复记录/架构重构实施总结.md"
        "docs/历史修复记录/架构重构实施计划.md"
    )
    
    for report in "${archived_reports[@]}"; do
        if [[ -f "$report" ]]; then
            log_success "修复报告归档: $(basename "$report") 已正确归档"
            ((PASSED_CHECKS++))
        else
            log_warning "修复报告归档: $(basename "$report") 未找到（可能原本不存在）"
        fi
        ((TOTAL_CHECKS++))
    done
    
    # 检查原位置是否已清理
    local original_reports=(
        "前端Token解析循环错误修复报告.md"
        "前端登录页面修复报告.md"
        "前端加载遮罩层修复报告.md"
        "架构重构实施总结.md"
        "架构重构实施计划.md"
    )
    
    for report in "${original_reports[@]}"; do
        check_file_removed "$report" "原修复报告"
    done
}

# 验证脚本归档
verify_script_archive() {
    log_info "验证脚本归档..."
    
    local archived_scripts=(
        "scripts/archived/docker-image-optimizer.sh"
        "scripts/archived/docker-image-analyzer.sh"
        "scripts/archived/performance-optimizer.sh"
        "scripts/archived/performance-analyzer.sh"
    )
    
    for script in "${archived_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            log_success "脚本归档: $(basename "$script") 已正确归档"
            ((PASSED_CHECKS++))
        else
            log_warning "脚本归档: $(basename "$script") 未找到（可能原本不存在）"
        fi
        ((TOTAL_CHECKS++))
    done
    
    # 检查原位置是否已清理
    local original_scripts=(
        "scripts/docker-image-optimizer.sh"
        "scripts/docker-image-analyzer.sh"
        "scripts/performance-optimizer.sh"
        "scripts/performance-analyzer.sh"
    )
    
    for script in "${original_scripts[@]}"; do
        check_file_removed "$script" "原重复脚本"
    done
}

# 验证保留脚本的功能
verify_remaining_scripts() {
    log_info "验证保留脚本的语法..."
    
    local important_scripts=(
        "scripts/docker-optimize.sh"
        "scripts/performance-optimization.sh"
        "scripts/start-dev-mode.sh"
        "scripts/deploy.sh"
        "scripts/optimize-codebase.sh"
    )
    
    for script in "${important_scripts[@]}"; do
        check_script_syntax "$script"
    done
}

# 验证Docker Compose配置
verify_docker_compose_configs() {
    log_info "验证Docker Compose配置..."
    
    local compose_files=(
        "docker-compose.dev.yml"
        "docker-compose.local.yml"
        "docker-compose.flexible.yml"
        "docker-compose.logging.yml"
        "docker-compose.monitoring.yml"
    )
    
    for compose_file in "${compose_files[@]}"; do
        check_docker_compose "$compose_file"
    done
}

# 验证文档链接
verify_documentation_links() {
    log_info "验证文档链接..."
    
    local important_docs=(
        "README.md"
        "PROJECT_SUMMARY.md"
        "docs/guides/cicd-service-guide.md"
        "docs/guides/development-auth-guide.md"
        "docs/guides/idp-integration-guide.md"
    )
    
    for doc in "${important_docs[@]}"; do
        check_markdown_links "$doc"
    done
}

# 生成验证报告
generate_verification_report() {
    log_info "生成验证报告..."
    
    local success_rate=0
    if [[ $TOTAL_CHECKS -gt 0 ]]; then
        success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    fi
    
    cat >> "$VERIFICATION_LOG" << EOF

========================================
验证结果总结
========================================

总检查项目: $TOTAL_CHECKS
通过检查: $PASSED_CHECKS
失败检查: $FAILED_CHECKS
成功率: ${success_rate}%

验证状态: $([[ $FAILED_CHECKS -eq 0 ]] && echo "✅ 全部通过" || echo "❌ 存在问题")

建议操作:
$([[ $FAILED_CHECKS -eq 0 ]] && echo "- 优化验证成功，可以继续使用" || echo "- 请检查失败的项目并进行修复")
$([[ $FAILED_CHECKS -gt 0 ]] && echo "- 如有需要，可以使用备份进行回滚")

========================================
EOF
    
    echo -e "\n${CYAN}📊 验证结果总结${NC}"
    echo -e "${CYAN}==================${NC}"
    echo -e "总检查项目: ${BLUE}$TOTAL_CHECKS${NC}"
    echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
    echo -e "成功率: ${YELLOW}${success_rate}%${NC}"
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 验证成功！优化结果符合预期${NC}"
    else
        echo -e "\n${RED}⚠️ 验证发现问题，请检查日志文件${NC}"
    fi
    
    echo -e "\n${BLUE}📝 详细日志: $VERIFICATION_LOG${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}🔍 PaaS平台代码库优化验证工具${NC}"
    echo -e "${CYAN}=====================================${NC}"
    
    # 初始化日志
    echo "验证开始时间: $(date)" > "$VERIFICATION_LOG"
    echo "项目根目录: $PROJECT_ROOT" >> "$VERIFICATION_LOG"
    echo "" >> "$VERIFICATION_LOG"
    
    # 执行各项验证
    verify_directory_structure
    verify_readme_integration
    verify_fix_reports_archive
    verify_script_archive
    verify_remaining_scripts
    verify_docker_compose_configs
    verify_documentation_links
    
    # 生成验证报告
    generate_verification_report
    
    # 返回适当的退出码
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
