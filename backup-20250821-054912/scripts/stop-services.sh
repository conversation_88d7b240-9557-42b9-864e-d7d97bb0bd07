#!/bin/bash

# PaaS 平台服务停止脚本
# 用于安全停止 IDP 混合认证相关服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务函数
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if ps -p $pid > /dev/null 2>&1; then
            log_info "停止 $service_name (PID: $pid)..."
            
            # 发送 TERM 信号
            kill -TERM $pid
            
            # 等待进程优雅退出
            local count=0
            while ps -p $pid > /dev/null 2>&1 && [ $count -lt 30 ]; do
                sleep 1
                ((count++))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                log_warning "$service_name 未能优雅退出，强制停止..."
                kill -KILL $pid
                sleep 1
            fi
            
            if ! ps -p $pid > /dev/null 2>&1; then
                log_success "$service_name 已停止"
                rm -f "$pid_file"
            else
                log_error "无法停止 $service_name"
                return 1
            fi
        else
            log_warning "$service_name PID 文件存在但进程不在运行"
            rm -f "$pid_file"
        fi
    else
        log_info "$service_name PID 文件不存在，尝试按名称停止..."
        
        # 按进程名称查找并停止
        local pids=$(pgrep -f "$service_name" || true)
        if [ -n "$pids" ]; then
            log_info "找到 $service_name 进程: $pids"
            echo "$pids" | xargs kill -TERM
            sleep 2
            
            # 检查是否还有进程在运行
            local remaining_pids=$(pgrep -f "$service_name" || true)
            if [ -n "$remaining_pids" ]; then
                log_warning "强制停止剩余的 $service_name 进程: $remaining_pids"
                echo "$remaining_pids" | xargs kill -KILL
            fi
            
            log_success "$service_name 已停止"
        else
            log_info "$service_name 未在运行"
        fi
    fi
}

# 主函数
main() {
    log_info "开始停止 PaaS 平台服务..."
    
    # 停止 API 网关
    stop_service "api-gateway" "api-gateway.pid"
    
    # 停止用户服务
    stop_service "user-service" "user-service.pid"
    
    # 清理其他可能的进程
    log_info "清理其他相关进程..."
    
    # 停止可能的 Go 进程
    local go_pids=$(pgrep -f "go run" || true)
    if [ -n "$go_pids" ]; then
        log_info "停止 Go 开发进程: $go_pids"
        echo "$go_pids" | xargs kill -TERM || true
        sleep 1
    fi
    
    # 检查端口占用
    log_info "检查端口占用情况..."
    
    local port_8080=$(lsof -ti:8080 || true)
    if [ -n "$port_8080" ]; then
        log_warning "端口 8080 仍被占用，进程 PID: $port_8080"
        echo "$port_8080" | xargs kill -TERM || true
    fi
    
    local port_8083=$(lsof -ti:8083 || true)
    if [ -n "$port_8083" ]; then
        log_warning "端口 8083 仍被占用，进程 PID: $port_8083"
        echo "$port_8083" | xargs kill -TERM || true
    fi
    
    # 等待端口释放
    sleep 2
    
    # 最终检查
    log_info "最终检查..."
    
    if ! lsof -ti:8080 >/dev/null 2>&1 && ! lsof -ti:8083 >/dev/null 2>&1; then
        log_success "所有服务已成功停止，端口已释放"
    else
        log_warning "某些端口可能仍被占用，请手动检查"
    fi
    
    # 显示状态
    echo
    echo "服务状态："
    echo "  端口 8080 (API 网关): $(lsof -ti:8080 >/dev/null 2>&1 && echo '占用' || echo '空闲')"
    echo "  端口 8083 (用户服务): $(lsof -ti:8083 >/dev/null 2>&1 && echo '占用' || echo '空闲')"
    
    # 显示日志文件位置
    echo
    echo "日志文件位置："
    if [ -f "logs/api-gateway.log" ]; then
        echo "  API 网关日志: logs/api-gateway.log"
    fi
    if [ -f "logs/user-service.log" ]; then
        echo "  用户服务日志: logs/user-service.log"
    fi
    
    echo
    log_success "服务停止完成"
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
