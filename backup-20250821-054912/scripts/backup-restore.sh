#!/bin/bash

# 备份和恢复脚本
# 支持数据库、配置、持久卷的备份和恢复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backups"
NAMESPACE=${NAMESPACE:-"paas-prod"}
BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
S3_BUCKET=${S3_BUCKET:-""}
S3_REGION=${S3_REGION:-"us-west-2"}

echo -e "${PURPLE}💾 PaaS 平台备份恢复工具${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：创建备份目录
create_backup_directory() {
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/$backup_timestamp"
    
    mkdir -p "$backup_path"/{database,configs,volumes,manifests}
    echo "$backup_path"
}

# 函数：备份数据库
backup_database() {
    local backup_path=$1
    print_title "备份数据库"
    
    # 获取数据库连接信息
    local db_host=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.host}' | base64 -d)
    local db_user=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.username}' | base64 -d)
    local db_password=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.password}' | base64 -d)
    local db_name=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.database}' | base64 -d)
    
    print_info "连接到数据库: $db_host"
    
    # 创建临时 Pod 进行数据库备份
    local backup_pod="db-backup-$(date +%s)"
    
    kubectl run "$backup_pod" -n "$NAMESPACE" \
        --image=postgres:15 \
        --rm -i --restart=Never \
        --env="PGPASSWORD=$db_password" \
        -- pg_dump -h "$db_host" -U "$db_user" -d "$db_name" \
        --verbose --no-owner --no-privileges \
        > "$backup_path/database/postgres_dump.sql"
    
    if [[ $? -eq 0 ]]; then
        print_success "数据库备份完成: postgres_dump.sql"
        
        # 压缩备份文件
        gzip "$backup_path/database/postgres_dump.sql"
        print_info "备份文件已压缩"
    else
        print_error "数据库备份失败"
        return 1
    fi
}

# 函数：备份 Redis 数据
backup_redis() {
    local backup_path=$1
    print_title "备份 Redis 数据"
    
    # 获取 Redis 连接信息
    local redis_host=$(kubectl get configmap redis-config -n "$NAMESPACE" -o jsonpath='{.data.host}')
    local redis_port=$(kubectl get configmap redis-config -n "$NAMESPACE" -o jsonpath='{.data.port}')
    
    print_info "连接到 Redis: $redis_host:$redis_port"
    
    # 创建临时 Pod 进行 Redis 备份
    local backup_pod="redis-backup-$(date +%s)"
    
    kubectl run "$backup_pod" -n "$NAMESPACE" \
        --image=redis:7 \
        --rm -i --restart=Never \
        -- redis-cli -h "$redis_host" -p "$redis_port" --rdb /tmp/dump.rdb \
        > "$backup_path/database/redis_dump.rdb"
    
    if [[ $? -eq 0 ]]; then
        print_success "Redis 备份完成: redis_dump.rdb"
    else
        print_warning "Redis 备份失败或无数据"
    fi
}

# 函数：备份配置
backup_configs() {
    local backup_path=$1
    print_title "备份配置"
    
    # 备份 ConfigMaps
    print_info "备份 ConfigMaps..."
    kubectl get configmaps -n "$NAMESPACE" -o yaml > "$backup_path/configs/configmaps.yaml"
    
    # 备份 Secrets（结构，不包含敏感数据）
    print_info "备份 Secrets 结构..."
    kubectl get secrets -n "$NAMESPACE" -o yaml | \
        sed 's/data:/data: # REDACTED FOR SECURITY/g' > "$backup_path/configs/secrets-structure.yaml"
    
    # 备份 Secrets 列表（用于恢复时参考）
    kubectl get secrets -n "$NAMESPACE" --no-headers | awk '{print $1}' > "$backup_path/configs/secrets-list.txt"
    
    print_success "配置备份完成"
}

# 函数：备份 Kubernetes 清单
backup_manifests() {
    local backup_path=$1
    print_title "备份 Kubernetes 清单"
    
    # 备份所有资源
    local resources=("deployments" "services" "ingresses" "persistentvolumeclaims" "horizontalpodautoscalers")
    
    for resource in "${resources[@]}"; do
        print_info "备份 $resource..."
        kubectl get "$resource" -n "$NAMESPACE" -o yaml > "$backup_path/manifests/$resource.yaml"
    done
    
    # 备份命名空间配置
    kubectl get namespace "$NAMESPACE" -o yaml > "$backup_path/manifests/namespace.yaml"
    
    print_success "Kubernetes 清单备份完成"
}

# 函数：备份持久卷数据
backup_volumes() {
    local backup_path=$1
    print_title "备份持久卷数据"
    
    # 获取所有 PVC
    local pvcs=$(kubectl get pvc -n "$NAMESPACE" --no-headers | awk '{print $1}')
    
    if [[ -z "$pvcs" ]]; then
        print_info "没有找到持久卷声明"
        return 0
    fi
    
    for pvc in $pvcs; do
        print_info "备份 PVC: $pvc"
        
        # 创建备份 Pod
        local backup_pod="volume-backup-$(date +%s)"
        
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: $backup_pod
  namespace: $NAMESPACE
spec:
  containers:
  - name: backup
    image: busybox:1.35
    command: ['sh', '-c', 'tar czf /backup/$pvc.tar.gz -C /data . && sleep 3600']
    volumeMounts:
    - name: data
      mountPath: /data
    - name: backup
      mountPath: /backup
  volumes:
  - name: data
    persistentVolumeClaim:
      claimName: $pvc
  - name: backup
    hostPath:
      path: $backup_path/volumes
      type: DirectoryOrCreate
  restartPolicy: Never
EOF
        
        # 等待 Pod 启动
        kubectl wait --for=condition=Ready pod/"$backup_pod" -n "$NAMESPACE" --timeout=300s
        
        # 等待备份完成
        sleep 10
        
        # 清理 Pod
        kubectl delete pod "$backup_pod" -n "$NAMESPACE"
        
        print_success "PVC $pvc 备份完成"
    done
}

# 函数：上传到 S3
upload_to_s3() {
    local backup_path=$1
    
    if [[ -z "$S3_BUCKET" ]]; then
        print_info "未配置 S3 存储桶，跳过上传"
        return 0
    fi
    
    print_title "上传备份到 S3"
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI 未安装，无法上传到 S3"
        return 1
    fi
    
    local backup_name=$(basename "$backup_path")
    local s3_path="s3://$S3_BUCKET/paas-backups/$backup_name.tar.gz"
    
    # 压缩备份目录
    print_info "压缩备份目录..."
    tar czf "$backup_path.tar.gz" -C "$(dirname "$backup_path")" "$backup_name"
    
    # 上传到 S3
    print_info "上传到 S3: $s3_path"
    aws s3 cp "$backup_path.tar.gz" "$s3_path" --region "$S3_REGION"
    
    if [[ $? -eq 0 ]]; then
        print_success "备份已上传到 S3"
        rm -f "$backup_path.tar.gz"
    else
        print_error "S3 上传失败"
        return 1
    fi
}

# 函数：执行完整备份
perform_backup() {
    print_title "开始完整备份"
    
    local backup_path=$(create_backup_directory)
    print_info "备份目录: $backup_path"
    
    # 创建备份元数据
    cat > "$backup_path/metadata.json" <<EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "namespace": "$NAMESPACE",
  "version": "$(kubectl version --client --short)",
  "cluster": "$(kubectl config current-context)"
}
EOF
    
    # 执行各项备份
    backup_database "$backup_path" || print_warning "数据库备份失败"
    backup_redis "$backup_path" || print_warning "Redis 备份失败"
    backup_configs "$backup_path"
    backup_manifests "$backup_path"
    backup_volumes "$backup_path" || print_warning "持久卷备份失败"
    
    # 上传到 S3（如果配置）
    upload_to_s3 "$backup_path" || print_warning "S3 上传失败"
    
    print_success "备份完成: $backup_path"
    
    # 清理旧备份
    cleanup_old_backups
}

# 函数：恢复数据库
restore_database() {
    local backup_path=$1
    print_title "恢复数据库"
    
    local db_dump="$backup_path/database/postgres_dump.sql.gz"
    
    if [[ ! -f "$db_dump" ]]; then
        print_error "数据库备份文件不存在: $db_dump"
        return 1
    fi
    
    # 获取数据库连接信息
    local db_host=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.host}' | base64 -d)
    local db_user=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.username}' | base64 -d)
    local db_password=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.password}' | base64 -d)
    local db_name=$(kubectl get secret database-secret -n "$NAMESPACE" -o jsonpath='{.data.database}' | base64 -d)
    
    print_warning "这将覆盖现有数据库数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "数据库恢复已取消"
        return 0
    fi
    
    # 创建临时 Pod 进行数据库恢复
    local restore_pod="db-restore-$(date +%s)"
    
    zcat "$db_dump" | kubectl run "$restore_pod" -n "$NAMESPACE" \
        --image=postgres:15 \
        --rm -i --restart=Never \
        --env="PGPASSWORD=$db_password" \
        -- psql -h "$db_host" -U "$db_user" -d "$db_name"
    
    if [[ $? -eq 0 ]]; then
        print_success "数据库恢复完成"
    else
        print_error "数据库恢复失败"
        return 1
    fi
}

# 函数：恢复配置
restore_configs() {
    local backup_path=$1
    print_title "恢复配置"
    
    local configmaps_file="$backup_path/configs/configmaps.yaml"
    
    if [[ -f "$configmaps_file" ]]; then
        print_info "恢复 ConfigMaps..."
        kubectl apply -f "$configmaps_file"
        print_success "ConfigMaps 恢复完成"
    else
        print_warning "ConfigMaps 备份文件不存在"
    fi
    
    print_warning "Secrets 需要手动恢复（安全考虑）"
    print_info "请参考: $backup_path/configs/secrets-list.txt"
}

# 函数：从 S3 下载备份
download_from_s3() {
    local backup_name=$1
    
    if [[ -z "$S3_BUCKET" ]]; then
        print_error "未配置 S3 存储桶"
        return 1
    fi
    
    print_title "从 S3 下载备份"
    
    local s3_path="s3://$S3_BUCKET/paas-backups/$backup_name.tar.gz"
    local local_path="$BACKUP_DIR/$backup_name.tar.gz"
    
    print_info "从 S3 下载: $s3_path"
    aws s3 cp "$s3_path" "$local_path" --region "$S3_REGION"
    
    if [[ $? -eq 0 ]]; then
        print_info "解压备份文件..."
        tar xzf "$local_path" -C "$BACKUP_DIR"
        rm -f "$local_path"
        print_success "备份下载完成: $BACKUP_DIR/$backup_name"
        echo "$BACKUP_DIR/$backup_name"
    else
        print_error "S3 下载失败"
        return 1
    fi
}

# 函数：执行恢复
perform_restore() {
    local backup_path=$1
    
    if [[ -z "$backup_path" ]]; then
        print_error "请指定备份路径"
        return 1
    fi
    
    if [[ ! -d "$backup_path" ]]; then
        print_error "备份路径不存在: $backup_path"
        return 1
    fi
    
    print_title "开始恢复"
    print_info "备份路径: $backup_path"
    
    # 显示备份信息
    if [[ -f "$backup_path/metadata.json" ]]; then
        print_info "备份信息:"
        cat "$backup_path/metadata.json"
        echo ""
    fi
    
    print_warning "这将覆盖现有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "恢复已取消"
        return 0
    fi
    
    # 执行恢复
    restore_configs "$backup_path"
    restore_database "$backup_path" || print_warning "数据库恢复失败"
    
    print_success "恢复完成"
    print_info "请重启相关服务以确保配置生效"
}

# 函数：清理旧备份
cleanup_old_backups() {
    print_title "清理旧备份"
    
    local old_backups=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" -mtime +$BACKUP_RETENTION_DAYS)
    
    if [[ -z "$old_backups" ]]; then
        print_info "没有需要清理的旧备份"
        return 0
    fi
    
    print_info "清理 $BACKUP_RETENTION_DAYS 天前的备份:"
    echo "$old_backups"
    
    echo "$old_backups" | xargs rm -rf
    print_success "旧备份清理完成"
}

# 函数：列出备份
list_backups() {
    print_title "本地备份列表"
    
    local backups=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" | sort -r)
    
    if [[ -z "$backups" ]]; then
        print_info "没有找到本地备份"
    else
        echo "$backups" | while read -r backup; do
            local backup_name=$(basename "$backup")
            local backup_size=$(du -sh "$backup" | cut -f1)
            echo "  $backup_name ($backup_size)"
        done
    fi
    
    # 列出 S3 备份
    if [[ -n "$S3_BUCKET" ]] && command -v aws &> /dev/null; then
        echo ""
        print_title "S3 备份列表"
        aws s3 ls "s3://$S3_BUCKET/paas-backups/" --region "$S3_REGION" | grep "\.tar\.gz$" || print_info "没有找到 S3 备份"
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台备份恢复工具"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  backup                   执行完整备份"
    echo "  restore <backup_path>    从指定路径恢复"
    echo "  restore-s3 <backup_name> 从 S3 下载并恢复"
    echo "  list                     列出所有备份"
    echo "  cleanup                  清理旧备份"
    echo ""
    echo "选项:"
    echo "  -n, --namespace NS       指定 Kubernetes 命名空间"
    echo "  -r, --retention DAYS     备份保留天数 (默认: 30)"
    echo "  --s3-bucket BUCKET       S3 存储桶名称"
    echo "  --s3-region REGION       S3 区域 (默认: us-west-2)"
    echo "  --help                   显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  NAMESPACE                Kubernetes 命名空间"
    echo "  BACKUP_RETENTION_DAYS    备份保留天数"
    echo "  S3_BUCKET                S3 存储桶名称"
    echo "  S3_REGION                S3 区域"
    echo ""
    echo "示例:"
    echo "  $0 backup                           # 执行完整备份"
    echo "  $0 restore /path/to/backup          # 从本地路径恢复"
    echo "  $0 restore-s3 20231201_120000       # 从 S3 恢复"
    echo "  $0 list                             # 列出所有备份"
    echo "  $0 cleanup                          # 清理旧备份"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            backup)
                perform_backup
                exit $?
                ;;
            restore)
                if [[ -z "$2" ]]; then
                    print_error "请指定备份路径"
                    exit 1
                fi
                perform_restore "$2"
                exit $?
                ;;
            restore-s3)
                if [[ -z "$2" ]]; then
                    print_error "请指定备份名称"
                    exit 1
                fi
                local backup_path=$(download_from_s3 "$2")
                if [[ $? -eq 0 ]]; then
                    perform_restore "$backup_path"
                fi
                exit $?
                ;;
            list)
                list_backups
                exit 0
                ;;
            cleanup)
                cleanup_old_backups
                exit 0
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -r|--retention)
                BACKUP_RETENTION_DAYS="$2"
                shift 2
                ;;
            --s3-bucket)
                S3_BUCKET="$2"
                shift 2
                ;;
            --s3-region)
                S3_REGION="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知命令或选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定命令，显示帮助
    show_help
}

# 脚本入口
main "$@"
