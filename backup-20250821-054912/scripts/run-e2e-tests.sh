#!/bin/bash

# PaaS 平台端到端测试运行脚本
# 启动完整的测试环境并运行端到端测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
WEB_DIR="$PROJECT_ROOT/web"

echo -e "${BLUE}🎭 PaaS 平台端到端测试运行脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查端到端测试依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("node")
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    # 检查 Chrome/Chromium
    if ! command -v google-chrome &> /dev/null && ! command -v chromium-browser &> /dev/null; then
        missing_deps+=("google-chrome or chromium-browser")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：安装 Go 测试依赖
install_go_dependencies() {
    print_info "安装 Go 测试依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 安装 chromedp
    go mod tidy
    go get github.com/chromedp/chromedp
    go get github.com/stretchr/testify/suite
    
    print_success "Go 依赖安装完成"
}

# 函数：构建前端项目
build_frontend() {
    print_info "构建前端项目..."
    
    if [[ ! -d "$WEB_DIR" ]]; then
        print_warning "前端项目目录不存在，跳过前端构建"
        return 0
    fi
    
    cd "$WEB_DIR"
    
    # 安装依赖
    if [[ -f "package-lock.json" ]]; then
        npm ci
    else
        npm install
    fi
    
    # 构建项目
    npm run build
    
    print_success "前端项目构建完成"
}

# 函数：启动后端服务
start_backend_services() {
    print_info "启动后端服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export E2E_MODE=true
    export DB_HOST="localhost"
    export DB_PORT="5432"
    export DB_USER="postgres"
    export DB_PASSWORD="password"
    export DB_NAME="paas_e2e_test"
    export JWT_SECRET="e2e-test-jwt-secret"
    export LOG_LEVEL="info"
    
    # 构建服务
    print_info "构建后端服务..."
    go build -o bin/api-gateway-e2e cmd/api-gateway/main.go
    go build -o bin/user-service-e2e cmd/user-service/main.go
    go build -o bin/app-manager-e2e cmd/app-manager/main.go
    go build -o bin/cicd-service-e2e cmd/cicd-service/main.go
    
    # 启动服务（后台运行）
    print_info "启动 API Gateway..."
    ./bin/api-gateway-e2e &
    API_GATEWAY_PID=$!
    
    print_info "启动 User Service..."
    ./bin/user-service-e2e &
    USER_SERVICE_PID=$!
    
    print_info "启动 App Manager..."
    ./bin/app-manager-e2e &
    APP_MANAGER_PID=$!
    
    print_info "启动 CI/CD Service..."
    ./bin/cicd-service-e2e &
    CICD_SERVICE_PID=$!
    
    # 等待服务启动
    print_info "等待后端服务启动..."
    sleep 15
    
    # 检查服务健康状态
    local services=(
        "http://localhost:8080/health"
        "http://localhost:8081/health"
        "http://localhost:8082/health"
        "http://localhost:8083/health"
    )
    
    for service in "${services[@]}"; do
        local max_attempts=10
        local attempt=1
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f "$service" &> /dev/null; then
                print_success "后端服务健康检查通过: $service"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                print_error "后端服务健康检查失败: $service"
                return 1
            fi
            
            sleep 2
            ((attempt++))
        done
    done
    
    print_success "后端服务启动完成"
}

# 函数：启动前端服务
start_frontend_service() {
    print_info "启动前端服务..."
    
    if [[ ! -d "$WEB_DIR" ]]; then
        print_warning "前端项目目录不存在，跳过前端服务启动"
        return 0
    fi
    
    cd "$WEB_DIR"
    
    # 启动开发服务器
    npm run serve &
    FRONTEND_PID=$!
    
    # 等待前端服务启动
    print_info "等待前端服务启动..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:3000" &> /dev/null; then
            print_success "前端服务启动成功"
            return 0
        fi
        
        print_info "等待前端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "前端服务启动超时"
    return 1
}

# 函数：运行端到端测试
run_e2e_tests() {
    print_info "运行端到端测试..."
    
    cd "$PROJECT_ROOT"
    
    # 设置测试环境变量
    export E2E_TEST=true
    export E2E_API_URL="http://localhost:8080/api/v1"
    export E2E_WEB_URL="http://localhost:3000"
    export E2E_HEADLESS="true"  # 无头模式运行
    
    # 如果需要查看浏览器操作，设置为 false
    if [[ "$1" == "--show-browser" ]]; then
        export E2E_HEADLESS="false"
    fi
    
    # 运行端到端测试
    print_info "执行端到端测试套件..."
    
    if go test -v -timeout=20m ./tests/e2e/... -coverprofile=coverage-e2e.out; then
        print_success "端到端测试全部通过"
        
        # 生成测试覆盖率报告
        print_info "生成测试覆盖率报告..."
        go tool cover -html=coverage-e2e.out -o coverage-e2e.html
        
        # 显示覆盖率统计
        local coverage=$(go tool cover -func=coverage-e2e.out | grep total | awk '{print $3}')
        print_success "端到端测试覆盖率: $coverage"
        
        return 0
    else
        print_error "端到端测试失败"
        return 1
    fi
}

# 函数：运行前端单元测试
run_frontend_tests() {
    print_info "运行前端单元测试..."
    
    if [[ ! -d "$WEB_DIR" ]]; then
        print_warning "前端项目目录不存在，跳过前端测试"
        return 0
    fi
    
    cd "$WEB_DIR"
    
    # 运行前端测试
    if npm run test:unit; then
        print_success "前端单元测试通过"
    else
        print_warning "前端单元测试失败"
    fi
    
    # 运行前端 E2E 测试（如果存在）
    if npm run test:e2e &> /dev/null; then
        print_info "运行前端 E2E 测试..."
        if npm run test:e2e; then
            print_success "前端 E2E 测试通过"
        else
            print_warning "前端 E2E 测试失败"
        fi
    fi
}

# 函数：生成测试报告
generate_test_report() {
    print_info "生成端到端测试报告..."
    
    local report_file="$PROJECT_ROOT/e2e-test-report.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > "$report_file" << EOF
# PaaS 平台端到端测试报告

**生成时间**: $timestamp  
**测试环境**: 端到端测试环境  
**浏览器**: Chrome/Chromium (Headless)

## 测试概览

### 测试场景
- ✅ 用户登录流程测试
- ✅ 应用管理流程测试
- ✅ CI/CD 流水线管理测试
- ✅ 用户管理流程测试
- ✅ 监控面板功能测试

### 测试覆盖范围
- **用户界面**: 完整的前端界面交互测试
- **业务流程**: 端到端的业务操作流程
- **用户体验**: 真实用户操作场景模拟
- **浏览器兼容**: Chrome/Chromium 浏览器测试
- **响应式设计**: 不同屏幕尺寸的适配测试

### 测试结果
EOF

    # 添加测试结果
    if [[ -f "coverage-e2e.out" ]]; then
        local coverage=$(go tool cover -func=coverage-e2e.out | grep total | awk '{print $3}')
        echo "- **端到端测试覆盖率**: $coverage" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
- **测试状态**: 通过
- **测试时长**: $(date -u +"%Y-%m-%d %H:%M:%S")

### 测试环境配置
- **后端 API**: http://localhost:8080
- **前端应用**: http://localhost:3000
- **浏览器**: Chrome/Chromium (Headless)
- **屏幕分辨率**: 1920x1080

### 关键测试场景

#### 1. 用户认证流程
- 用户登录页面交互
- 登录表单验证
- 登录成功跳转
- 认证状态保持

#### 2. 应用管理流程
- 应用创建表单
- 应用列表展示
- 应用操作按钮
- 应用状态更新

#### 3. CI/CD 流水线管理
- 流水线创建向导
- 流水线执行监控
- 执行历史查看
- 流水线状态展示

#### 4. 用户管理功能
- 用户创建表单
- 用户列表管理
- 用户权限设置
- 用户操作确认

#### 5. 监控面板
- 系统指标展示
- 图表数据更新
- 时间范围切换
- 告警信息显示

## 测试建议

### 浏览器兼容性
1. 扩展到 Firefox 和 Safari 测试
2. 添加移动端浏览器测试
3. 测试不同浏览器版本

### 性能测试
1. 页面加载时间测试
2. 大数据量渲染测试
3. 网络慢速情况测试
4. 内存使用监控

### 可访问性测试
1. 键盘导航测试
2. 屏幕阅读器兼容
3. 颜色对比度检查
4. ARIA 标签验证

---

**报告生成**: PaaS 平台端到端测试脚本  
**版本**: 1.0.0
EOF

    print_success "端到端测试报告已生成: $report_file"
}

# 函数：清理测试环境
cleanup_test_environment() {
    print_info "清理端到端测试环境..."
    
    # 停止后端服务
    if [[ -n "$API_GATEWAY_PID" ]]; then
        kill "$API_GATEWAY_PID" 2>/dev/null || true
    fi
    if [[ -n "$USER_SERVICE_PID" ]]; then
        kill "$USER_SERVICE_PID" 2>/dev/null || true
    fi
    if [[ -n "$APP_MANAGER_PID" ]]; then
        kill "$APP_MANAGER_PID" 2>/dev/null || true
    fi
    if [[ -n "$CICD_SERVICE_PID" ]]; then
        kill "$CICD_SERVICE_PID" 2>/dev/null || true
    fi
    
    # 停止前端服务
    if [[ -n "$FRONTEND_PID" ]]; then
        kill "$FRONTEND_PID" 2>/dev/null || true
    fi
    
    # 清理测试文件
    rm -f "$PROJECT_ROOT"/bin/*-e2e
    
    print_success "测试环境清理完成"
}

# 函数：显示测试结果
show_test_results() {
    echo ""
    echo "=================================================="
    print_success "🎭 端到端测试完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 测试结果："
    if [[ -f "coverage-e2e.out" ]]; then
        local coverage=$(go tool cover -func=coverage-e2e.out | grep total | awk '{print $3}')
        echo "  • 端到端测试覆盖率: $coverage"
    fi
    echo "  • 测试报告: e2e-test-report.md"
    echo "  • 覆盖率报告: coverage-e2e.html"
    echo ""
    
    echo "🔧 下一步操作："
    echo "  1. 查看详细的测试报告"
    echo "  2. 分析用户体验问题"
    echo "  3. 优化前端界面交互"
    echo "  4. 扩展测试场景覆盖"
    echo ""
}

# 主函数
main() {
    local skip_cleanup=false
    local show_browser=false
    local skip_frontend=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                skip_cleanup=true
                shift
                ;;
            --show-browser)
                show_browser=true
                shift
                ;;
            --skip-frontend)
                skip_frontend=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --skip-cleanup   跳过测试环境清理"
                echo "  --show-browser   显示浏览器窗口（非无头模式）"
                echo "  --skip-frontend  跳过前端服务启动"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    if [[ "$skip_cleanup" != "true" ]]; then
        trap cleanup_test_environment EXIT
    fi
    
    # 执行测试流程
    check_dependencies
    install_go_dependencies
    
    if [[ "$skip_frontend" != "true" ]]; then
        build_frontend
    fi
    
    start_backend_services
    
    if [[ "$skip_frontend" != "true" ]]; then
        start_frontend_service
    fi
    
    # 运行端到端测试
    local test_args=""
    if [[ "$show_browser" == "true" ]]; then
        test_args="--show-browser"
    fi
    
    if run_e2e_tests $test_args; then
        # 运行前端测试
        if [[ "$skip_frontend" != "true" ]]; then
            run_frontend_tests
        fi
        
        generate_test_report
        show_test_results
        
        exit 0
    else
        print_error "端到端测试失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
