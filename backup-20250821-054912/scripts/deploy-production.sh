#!/bin/bash

# 生产环境部署脚本
# 支持蓝绿部署、金丝雀发布和滚动更新

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_TYPE=${DEPLOYMENT_TYPE:-"rolling"}
ENVIRONMENT=${ENVIRONMENT:-"production"}
NAMESPACE=${NAMESPACE:-"paas-prod"}
REGISTRY=${DOCKER_REGISTRY:-"registry.example.com"}
VERSION=${VERSION:-"latest"}
REPLICAS=${REPLICAS:-3}
HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-300}
ROLLBACK_ON_FAILURE=${ROLLBACK_ON_FAILURE:-true}

echo -e "${PURPLE}🚀 PaaS 平台生产环境部署脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：检查前置条件
check_prerequisites() {
    print_title "检查部署前置条件"
    
    # 检查必要的工具
    local tools=("kubectl" "docker" "helm" "jq")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            print_error "$tool 未安装或不在 PATH 中"
            exit 1
        fi
    done
    print_success "所有必要工具已安装"
    
    # 检查 Kubernetes 连接
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    print_success "Kubernetes 集群连接正常"
    
    # 检查命名空间
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        print_info "创建命名空间: $NAMESPACE"
        kubectl create namespace "$NAMESPACE"
    fi
    print_success "命名空间 $NAMESPACE 已准备就绪"
    
    # 检查镜像是否存在
    print_info "检查 Docker 镜像..."
    local services=("api-gateway" "user-service" "app-manager" "ci-cd-service" "monitoring-service" "notification-service" "load-balancer" "web")
    for service in "${services[@]}"; do
        local image="$REGISTRY/paas/$service:$VERSION"
        if ! docker manifest inspect "$image" &> /dev/null; then
            print_warning "镜像 $image 不存在，将尝试构建"
            build_missing_images
            break
        fi
    done
    print_success "所有镜像已准备就绪"
}

# 函数：构建缺失的镜像
build_missing_images() {
    print_title "构建缺失的镜像"
    
    cd "$PROJECT_ROOT"
    
    # 运行镜像构建脚本
    if [[ -f "scripts/build-images.sh" ]]; then
        bash scripts/build-images.sh all
        bash scripts/build-images.sh push
    else
        print_error "镜像构建脚本不存在"
        exit 1
    fi
    
    print_success "镜像构建完成"
}

# 函数：备份当前部署
backup_current_deployment() {
    print_title "备份当前部署"
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份 Kubernetes 资源
    kubectl get all -n "$NAMESPACE" -o yaml > "$backup_dir/kubernetes-resources.yaml"
    
    # 备份 ConfigMaps 和 Secrets
    kubectl get configmaps -n "$NAMESPACE" -o yaml > "$backup_dir/configmaps.yaml"
    kubectl get secrets -n "$NAMESPACE" -o yaml > "$backup_dir/secrets.yaml"
    
    # 备份 Helm 发布信息
    if helm list -n "$NAMESPACE" | grep -q paas; then
        helm get values paas -n "$NAMESPACE" > "$backup_dir/helm-values.yaml"
        helm get manifest paas -n "$NAMESPACE" > "$backup_dir/helm-manifest.yaml"
    fi
    
    echo "$backup_dir" > "$PROJECT_ROOT/.last-backup"
    print_success "部署备份已保存到: $backup_dir"
}

# 函数：滚动更新部署
deploy_rolling_update() {
    print_title "执行滚动更新部署"
    
    # 使用 Helm 进行滚动更新
    helm upgrade --install paas "$PROJECT_ROOT/helm/paas" \
        --namespace "$NAMESPACE" \
        --set image.tag="$VERSION" \
        --set image.registry="$REGISTRY" \
        --set replicaCount="$REPLICAS" \
        --set environment="$ENVIRONMENT" \
        --wait \
        --timeout=10m
    
    print_success "滚动更新部署完成"
}

# 函数：蓝绿部署
deploy_blue_green() {
    print_title "执行蓝绿部署"
    
    # 获取当前活跃环境
    local current_env=$(kubectl get service paas-web -n "$NAMESPACE" -o jsonpath='{.spec.selector.environment}' 2>/dev/null || echo "blue")
    local new_env="green"
    if [[ "$current_env" == "green" ]]; then
        new_env="blue"
    fi
    
    print_info "当前环境: $current_env, 新环境: $new_env"
    
    # 部署到新环境
    helm upgrade --install "paas-$new_env" "$PROJECT_ROOT/helm/paas" \
        --namespace "$NAMESPACE" \
        --set image.tag="$VERSION" \
        --set image.registry="$REGISTRY" \
        --set replicaCount="$REPLICAS" \
        --set environment="$new_env" \
        --set nameOverride="paas-$new_env" \
        --wait \
        --timeout=10m
    
    # 健康检查
    if perform_health_check "paas-$new_env"; then
        # 切换流量
        print_info "切换流量到新环境: $new_env"
        kubectl patch service paas-web -n "$NAMESPACE" -p "{\"spec\":{\"selector\":{\"environment\":\"$new_env\"}}}"
        
        # 等待一段时间确保稳定
        sleep 30
        
        # 再次健康检查
        if perform_health_check "paas-web"; then
            # 清理旧环境
            print_info "清理旧环境: $current_env"
            helm uninstall "paas-$current_env" -n "$NAMESPACE" || true
            print_success "蓝绿部署完成"
        else
            # 回滚
            print_error "新环境健康检查失败，回滚到旧环境"
            kubectl patch service paas-web -n "$NAMESPACE" -p "{\"spec\":{\"selector\":{\"environment\":\"$current_env\"}}}"
            helm uninstall "paas-$new_env" -n "$NAMESPACE"
            exit 1
        fi
    else
        print_error "新环境部署失败"
        helm uninstall "paas-$new_env" -n "$NAMESPACE"
        exit 1
    fi
}

# 函数：金丝雀发布
deploy_canary() {
    print_title "执行金丝雀发布"
    
    local canary_percentage=${CANARY_PERCENTAGE:-10}
    print_info "金丝雀流量比例: $canary_percentage%"
    
    # 部署金丝雀版本
    helm upgrade --install paas-canary "$PROJECT_ROOT/helm/paas" \
        --namespace "$NAMESPACE" \
        --set image.tag="$VERSION" \
        --set image.registry="$REGISTRY" \
        --set replicaCount=1 \
        --set environment="canary" \
        --set nameOverride="paas-canary" \
        --wait \
        --timeout=10m
    
    # 配置流量分割（使用 Istio 或 NGINX Ingress）
    if kubectl get virtualservice paas-vs -n "$NAMESPACE" &> /dev/null; then
        # 使用 Istio
        configure_istio_canary "$canary_percentage"
    else
        # 使用 NGINX Ingress
        configure_nginx_canary "$canary_percentage"
    fi
    
    # 监控金丝雀版本
    monitor_canary_deployment
    
    # 根据监控结果决定是否继续
    if [[ $? -eq 0 ]]; then
        print_info "金丝雀版本表现良好，开始全量部署"
        deploy_rolling_update
        
        # 清理金丝雀版本
        helm uninstall paas-canary -n "$NAMESPACE"
        cleanup_canary_config
        
        print_success "金丝雀发布完成"
    else
        print_error "金丝雀版本表现异常，回滚"
        helm uninstall paas-canary -n "$NAMESPACE"
        cleanup_canary_config
        exit 1
    fi
}

# 函数：配置 Istio 金丝雀
configure_istio_canary() {
    local percentage=$1
    local stable_percentage=$((100 - percentage))
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: paas-vs
  namespace: $NAMESPACE
spec:
  hosts:
  - paas.example.com
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: paas-canary
        port:
          number: 80
  - route:
    - destination:
        host: paas-web
        port:
          number: 80
      weight: $stable_percentage
    - destination:
        host: paas-canary
        port:
          number: 80
      weight: $percentage
EOF
}

# 函数：配置 NGINX 金丝雀
configure_nginx_canary() {
    local percentage=$1
    
    kubectl annotate ingress paas-ingress -n "$NAMESPACE" \
        nginx.ingress.kubernetes.io/canary="true" \
        nginx.ingress.kubernetes.io/canary-weight="$percentage" \
        --overwrite
}

# 函数：监控金丝雀部署
monitor_canary_deployment() {
    print_info "监控金丝雀部署 (持续 5 分钟)..."
    
    local monitoring_duration=300  # 5 分钟
    local check_interval=30        # 30 秒检查一次
    local error_threshold=5        # 错误阈值 5%
    
    for ((i=0; i<monitoring_duration; i+=check_interval)); do
        sleep $check_interval
        
        # 检查错误率
        local error_rate=$(get_error_rate "paas-canary")
        if (( $(echo "$error_rate > $error_threshold" | bc -l) )); then
            print_error "金丝雀版本错误率过高: $error_rate%"
            return 1
        fi
        
        # 检查响应时间
        local response_time=$(get_response_time "paas-canary")
        if (( $(echo "$response_time > 1000" | bc -l) )); then
            print_error "金丝雀版本响应时间过长: ${response_time}ms"
            return 1
        fi
        
        print_info "金丝雀监控 - 错误率: $error_rate%, 响应时间: ${response_time}ms"
    done
    
    print_success "金丝雀监控完成，指标正常"
    return 0
}

# 函数：获取错误率
get_error_rate() {
    local service=$1
    # 这里应该从监控系统（如 Prometheus）获取实际的错误率
    # 示例返回随机值
    echo "scale=2; $(shuf -i 0-3 -n 1) + $(shuf -i 0-99 -n 1)/100" | bc
}

# 函数：获取响应时间
get_response_time() {
    local service=$1
    # 这里应该从监控系统获取实际的响应时间
    # 示例返回随机值
    shuf -i 200-800 -n 1
}

# 函数：清理金丝雀配置
cleanup_canary_config() {
    # 清理 Istio 配置
    kubectl delete virtualservice paas-vs -n "$NAMESPACE" 2>/dev/null || true
    
    # 清理 NGINX 配置
    kubectl annotate ingress paas-ingress -n "$NAMESPACE" \
        nginx.ingress.kubernetes.io/canary- \
        nginx.ingress.kubernetes.io/canary-weight- \
        2>/dev/null || true
}

# 函数：健康检查
perform_health_check() {
    local service=$1
    print_info "执行健康检查: $service"
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        # 检查 Pod 状态
        local ready_pods=$(kubectl get pods -n "$NAMESPACE" -l app="$service" -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -o "True" | wc -l)
        local total_pods=$(kubectl get pods -n "$NAMESPACE" -l app="$service" --no-headers | wc -l)
        
        if [[ $ready_pods -eq $total_pods ]] && [[ $total_pods -gt 0 ]]; then
            # 检查服务端点
            if kubectl get endpoints "$service" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}' | grep -q .; then
                print_success "健康检查通过: $service"
                return 0
            fi
        fi
        
        print_info "健康检查进行中... ($attempt/$max_attempts) - 就绪 Pod: $ready_pods/$total_pods"
        sleep 10
        ((attempt++))
    done
    
    print_error "健康检查失败: $service"
    return 1
}

# 函数：回滚部署
rollback_deployment() {
    print_title "回滚部署"
    
    if [[ -f "$PROJECT_ROOT/.last-backup" ]]; then
        local backup_dir=$(cat "$PROJECT_ROOT/.last-backup")
        
        if [[ -d "$backup_dir" ]]; then
            print_info "从备份恢复: $backup_dir"
            
            # 回滚 Helm 发布
            if [[ -f "$backup_dir/helm-values.yaml" ]]; then
                helm rollback paas -n "$NAMESPACE"
            fi
            
            print_success "回滚完成"
        else
            print_error "备份目录不存在: $backup_dir"
            exit 1
        fi
    else
        print_error "未找到备份信息"
        exit 1
    fi
}

# 函数：部署后验证
post_deployment_verification() {
    print_title "部署后验证"
    
    # 检查所有服务状态
    local services=("api-gateway" "user-service" "app-manager" "ci-cd-service" "monitoring-service" "notification-service" "load-balancer" "web")
    
    for service in "${services[@]}"; do
        if ! perform_health_check "paas-$service"; then
            print_error "服务验证失败: $service"
            if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
                rollback_deployment
            fi
            exit 1
        fi
    done
    
    # 执行端到端测试
    if [[ -f "$PROJECT_ROOT/tests/e2e/production-tests.sh" ]]; then
        print_info "执行端到端测试..."
        bash "$PROJECT_ROOT/tests/e2e/production-tests.sh"
        if [[ $? -ne 0 ]]; then
            print_error "端到端测试失败"
            if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
                rollback_deployment
            fi
            exit 1
        fi
    fi
    
    print_success "部署后验证通过"
}

# 函数：发送部署通知
send_deployment_notification() {
    print_title "发送部署通知"
    
    local status=$1
    local message="PaaS 平台 $ENVIRONMENT 环境部署 $status"
    local details="版本: $VERSION, 部署类型: $DEPLOYMENT_TYPE, 时间: $(date)"
    
    # 发送 Slack 通知
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\",\"attachments\":[{\"text\":\"$details\"}]}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    # 发送邮件通知
    if [[ -n "$EMAIL_RECIPIENTS" ]]; then
        echo "$details" | mail -s "$message" "$EMAIL_RECIPIENTS"
    fi
    
    print_success "部署通知已发送"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台生产环境部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE          部署类型 (rolling|blue-green|canary)"
    echo "  -v, --version VERSION    部署版本"
    echo "  -e, --environment ENV    环境名称"
    echo "  -n, --namespace NS       Kubernetes 命名空间"
    echo "  -r, --replicas NUM       副本数量"
    echo "  --rollback              回滚到上一个版本"
    echo "  --dry-run               模拟运行（不执行实际部署）"
    echo "  --help                  显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DEPLOYMENT_TYPE         部署类型"
    echo "  VERSION                 部署版本"
    echo "  ENVIRONMENT             环境名称"
    echo "  NAMESPACE               Kubernetes 命名空间"
    echo "  DOCKER_REGISTRY         Docker 镜像仓库"
    echo "  REPLICAS                副本数量"
    echo "  CANARY_PERCENTAGE       金丝雀流量比例"
    echo "  ROLLBACK_ON_FAILURE     失败时自动回滚"
    echo "  SLACK_WEBHOOK_URL       Slack 通知 URL"
    echo "  EMAIL_RECIPIENTS        邮件通知收件人"
    echo ""
    echo "示例:"
    echo "  $0 -t rolling -v v1.2.3              # 滚动更新部署"
    echo "  $0 -t blue-green -v v1.2.3           # 蓝绿部署"
    echo "  $0 -t canary -v v1.2.3               # 金丝雀发布"
    echo "  $0 --rollback                        # 回滚部署"
}

# 主函数
main() {
    local dry_run=false
    local rollback=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOYMENT_TYPE="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -r|--replicas)
                REPLICAS="$2"
                shift 2
                ;;
            --rollback)
                rollback=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示配置信息
    print_info "部署配置:"
    echo "  环境: $ENVIRONMENT"
    echo "  命名空间: $NAMESPACE"
    echo "  部署类型: $DEPLOYMENT_TYPE"
    echo "  版本: $VERSION"
    echo "  副本数: $REPLICAS"
    echo "  镜像仓库: $REGISTRY"
    
    if [[ "$dry_run" == "true" ]]; then
        print_warning "模拟运行模式，不会执行实际部署"
        exit 0
    fi
    
    # 执行回滚
    if [[ "$rollback" == "true" ]]; then
        rollback_deployment
        send_deployment_notification "回滚成功"
        exit 0
    fi
    
    # 开始部署流程
    print_info "开始部署流程..."
    
    # 检查前置条件
    check_prerequisites
    
    # 备份当前部署
    backup_current_deployment
    
    # 根据部署类型执行部署
    case $DEPLOYMENT_TYPE in
        "rolling")
            deploy_rolling_update
            ;;
        "blue-green")
            deploy_blue_green
            ;;
        "canary")
            deploy_canary
            ;;
        *)
            print_error "不支持的部署类型: $DEPLOYMENT_TYPE"
            exit 1
            ;;
    esac
    
    # 部署后验证
    post_deployment_verification
    
    # 发送成功通知
    send_deployment_notification "成功"
    
    print_success "🎉 部署完成！"
    print_info "部署摘要:"
    echo "  ✅ 环境: $ENVIRONMENT"
    echo "  ✅ 版本: $VERSION"
    echo "  ✅ 部署类型: $DEPLOYMENT_TYPE"
    echo "  ✅ 命名空间: $NAMESPACE"
    echo "  ✅ 副本数: $REPLICAS"
    echo ""
    print_info "访问地址:"
    kubectl get ingress -n "$NAMESPACE" -o custom-columns=NAME:.metadata.name,HOSTS:.spec.rules[*].host,ADDRESS:.status.loadBalancer.ingress[*].ip
}

# 错误处理
trap 'print_error "部署过程中发生错误"; send_deployment_notification "失败"; exit 1' ERR

# 脚本入口
main "$@"
