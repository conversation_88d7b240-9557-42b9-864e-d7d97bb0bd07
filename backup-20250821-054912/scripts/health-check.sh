#!/bin/bash

# 系统健康检查脚本
# 检查 PaaS 平台各个组件的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE=${NAMESPACE:-"paas-prod"}
TIMEOUT=${TIMEOUT:-30}
VERBOSE=${VERBOSE:-false}

echo -e "${PURPLE}🏥 PaaS 平台健康检查工具${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：检查 Kubernetes 连接
check_kubernetes_connection() {
    print_title "检查 Kubernetes 连接"
    
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl 未安装"
        return 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        return 1
    fi
    
    local cluster_info=$(kubectl cluster-info | head -1)
    print_success "Kubernetes 集群连接正常: $cluster_info"
    return 0
}

# 函数：检查命名空间
check_namespace() {
    print_title "检查命名空间"
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        print_error "命名空间 $NAMESPACE 不存在"
        return 1
    fi
    
    print_success "命名空间 $NAMESPACE 存在"
    return 0
}

# 函数：检查 Pod 状态
check_pods() {
    print_title "检查 Pod 状态"
    
    local failed_pods=0
    local services=("api-gateway" "user-service" "app-manager" "ci-cd-service" "monitoring-service" "notification-service" "load-balancer" "web")
    
    for service in "${services[@]}"; do
        local pod_status=$(kubectl get pods -n "$NAMESPACE" -l app="$service" -o jsonpath='{.items[*].status.phase}' 2>/dev/null)
        
        if [[ -z "$pod_status" ]]; then
            print_warning "服务 $service 没有运行的 Pod"
            ((failed_pods++))
            continue
        fi
        
        local running_pods=0
        local total_pods=0
        
        for status in $pod_status; do
            ((total_pods++))
            if [[ "$status" == "Running" ]]; then
                ((running_pods++))
            fi
        done
        
        if [[ $running_pods -eq $total_pods ]] && [[ $total_pods -gt 0 ]]; then
            print_success "服务 $service: $running_pods/$total_pods Pod 运行正常"
        else
            print_error "服务 $service: $running_pods/$total_pods Pod 运行正常"
            ((failed_pods++))
        fi
        
        # 详细信息（如果启用详细模式）
        if [[ "$VERBOSE" == "true" ]]; then
            kubectl get pods -n "$NAMESPACE" -l app="$service" --no-headers | while read -r line; do
                echo "  $line"
            done
        fi
    done
    
    if [[ $failed_pods -eq 0 ]]; then
        print_success "所有服务 Pod 状态正常"
        return 0
    else
        print_error "$failed_pods 个服务存在 Pod 问题"
        return 1
    fi
}

# 函数：检查服务状态
check_services() {
    print_title "检查服务状态"
    
    local failed_services=0
    local services=("api-gateway" "user-service" "app-manager" "ci-cd-service" "monitoring-service" "notification-service" "load-balancer" "web")
    
    for service in "${services[@]}"; do
        if kubectl get service "$service" -n "$NAMESPACE" &> /dev/null; then
            local endpoints=$(kubectl get endpoints "$service" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null)
            
            if [[ -n "$endpoints" ]]; then
                local endpoint_count=$(echo "$endpoints" | wc -w)
                print_success "服务 $service: $endpoint_count 个端点可用"
            else
                print_error "服务 $service: 没有可用端点"
                ((failed_services++))
            fi
        else
            print_error "服务 $service 不存在"
            ((failed_services++))
        fi
    done
    
    if [[ $failed_services -eq 0 ]]; then
        print_success "所有服务状态正常"
        return 0
    else
        print_error "$failed_services 个服务存在问题"
        return 1
    fi
}

# 函数：检查 Ingress 状态
check_ingress() {
    print_title "检查 Ingress 状态"
    
    local ingresses=$(kubectl get ingress -n "$NAMESPACE" --no-headers 2>/dev/null | wc -l)
    
    if [[ $ingresses -eq 0 ]]; then
        print_warning "没有找到 Ingress 资源"
        return 1
    fi
    
    kubectl get ingress -n "$NAMESPACE" --no-headers | while read -r line; do
        local ingress_name=$(echo "$line" | awk '{print $1}')
        local hosts=$(echo "$line" | awk '{print $3}')
        local address=$(echo "$line" | awk '{print $4}')
        
        if [[ -n "$address" && "$address" != "<none>" ]]; then
            print_success "Ingress $ingress_name: $hosts -> $address"
        else
            print_warning "Ingress $ingress_name: $hosts (地址未分配)"
        fi
    done
    
    return 0
}

# 函数：检查持久卷状态
check_persistent_volumes() {
    print_title "检查持久卷状态"
    
    local pvcs=$(kubectl get pvc -n "$NAMESPACE" --no-headers 2>/dev/null)
    
    if [[ -z "$pvcs" ]]; then
        print_info "没有找到持久卷声明"
        return 0
    fi
    
    local failed_pvcs=0
    
    echo "$pvcs" | while read -r line; do
        local pvc_name=$(echo "$line" | awk '{print $1}')
        local status=$(echo "$line" | awk '{print $2}')
        local capacity=$(echo "$line" | awk '{print $4}')
        
        if [[ "$status" == "Bound" ]]; then
            print_success "PVC $pvc_name: $status ($capacity)"
        else
            print_error "PVC $pvc_name: $status"
            ((failed_pvcs++))
        fi
    done
    
    return 0
}

# 函数：检查资源使用情况
check_resource_usage() {
    print_title "检查资源使用情况"
    
    # 检查节点资源
    print_info "节点资源使用情况:"
    kubectl top nodes 2>/dev/null || print_warning "无法获取节点资源使用情况（需要 metrics-server）"
    
    echo ""
    
    # 检查 Pod 资源
    print_info "Pod 资源使用情况:"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || print_warning "无法获取 Pod 资源使用情况（需要 metrics-server）"
    
    return 0
}

# 函数：检查网络连通性
check_network_connectivity() {
    print_title "检查网络连通性"
    
    # 检查 DNS 解析
    local test_pod="network-test-$(date +%s)"
    
    print_info "创建测试 Pod 进行网络检查..."
    
    kubectl run "$test_pod" -n "$NAMESPACE" --image=busybox:1.35 --rm -i --restart=Never --timeout="$TIMEOUT"s -- sh -c "
        echo '检查 DNS 解析...'
        nslookup kubernetes.default.svc.cluster.local
        echo ''
        echo '检查服务连通性...'
        nc -z api-gateway 80 && echo 'api-gateway: 可达' || echo 'api-gateway: 不可达'
        nc -z user-service 80 && echo 'user-service: 可达' || echo 'user-service: 不可达'
    " 2>/dev/null && print_success "网络连通性检查通过" || print_error "网络连通性检查失败"
    
    return 0
}

# 函数：检查应用健康端点
check_application_health() {
    print_title "检查应用健康端点"
    
    local services=("api-gateway" "user-service" "app-manager" "monitoring-service")
    local failed_health_checks=0
    
    for service in "${services[@]}"; do
        print_info "检查 $service 健康端点..."
        
        # 使用 kubectl port-forward 进行健康检查
        local port_forward_pid=""
        local local_port=$((8000 + RANDOM % 1000))
        
        # 启动端口转发
        kubectl port-forward -n "$NAMESPACE" "service/$service" "$local_port:80" &>/dev/null &
        port_forward_pid=$!
        
        # 等待端口转发建立
        sleep 2
        
        # 执行健康检查
        if curl -f -s --max-time 5 "http://localhost:$local_port/health" &>/dev/null; then
            print_success "$service 健康检查通过"
        else
            print_error "$service 健康检查失败"
            ((failed_health_checks++))
        fi
        
        # 清理端口转发
        if [[ -n "$port_forward_pid" ]]; then
            kill "$port_forward_pid" 2>/dev/null || true
        fi
    done
    
    if [[ $failed_health_checks -eq 0 ]]; then
        print_success "所有应用健康检查通过"
        return 0
    else
        print_error "$failed_health_checks 个应用健康检查失败"
        return 1
    fi
}

# 函数：检查配置和密钥
check_configs_and_secrets() {
    print_title "检查配置和密钥"
    
    local required_configmaps=("database-config" "redis-config" "app-config")
    local required_secrets=("database-secret" "redis-secret" "jwt-secret")
    local failed_configs=0
    
    # 检查 ConfigMaps
    for cm in "${required_configmaps[@]}"; do
        if kubectl get configmap "$cm" -n "$NAMESPACE" &>/dev/null; then
            print_success "ConfigMap $cm 存在"
        else
            print_error "ConfigMap $cm 不存在"
            ((failed_configs++))
        fi
    done
    
    # 检查 Secrets
    for secret in "${required_secrets[@]}"; do
        if kubectl get secret "$secret" -n "$NAMESPACE" &>/dev/null; then
            print_success "Secret $secret 存在"
        else
            print_error "Secret $secret 不存在"
            ((failed_configs++))
        fi
    done
    
    if [[ $failed_configs -eq 0 ]]; then
        print_success "所有配置和密钥检查通过"
        return 0
    else
        print_error "$failed_configs 个配置或密钥缺失"
        return 1
    fi
}

# 函数：生成健康报告
generate_health_report() {
    print_title "生成健康报告"
    
    local report_file="$PROJECT_ROOT/health-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "PaaS 平台健康检查报告"
        echo "生成时间: $(date)"
        echo "命名空间: $NAMESPACE"
        echo "========================================"
        echo ""
        
        echo "Pod 状态:"
        kubectl get pods -n "$NAMESPACE" -o wide
        echo ""
        
        echo "服务状态:"
        kubectl get services -n "$NAMESPACE"
        echo ""
        
        echo "Ingress 状态:"
        kubectl get ingress -n "$NAMESPACE"
        echo ""
        
        echo "持久卷状态:"
        kubectl get pvc -n "$NAMESPACE"
        echo ""
        
        echo "事件日志:"
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -20
        
    } > "$report_file"
    
    print_success "健康报告已生成: $report_file"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台健康检查工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -n, --namespace NS    指定 Kubernetes 命名空间 (默认: paas-prod)"
    echo "  -t, --timeout SEC     设置超时时间 (默认: 30 秒)"
    echo "  -v, --verbose         启用详细输出"
    echo "  -r, --report          生成详细的健康报告"
    echo "  --quick               快速检查（跳过网络和应用健康检查）"
    echo "  --help                显示帮助信息"
    echo ""
    echo "检查项目:"
    echo "  - Kubernetes 连接"
    echo "  - 命名空间状态"
    echo "  - Pod 状态"
    echo "  - 服务状态"
    echo "  - Ingress 状态"
    echo "  - 持久卷状态"
    echo "  - 资源使用情况"
    echo "  - 网络连通性"
    echo "  - 应用健康端点"
    echo "  - 配置和密钥"
    echo ""
    echo "示例:"
    echo "  $0                           # 完整健康检查"
    echo "  $0 -n paas-dev               # 检查开发环境"
    echo "  $0 --quick                   # 快速检查"
    echo "  $0 -v -r                     # 详细输出并生成报告"
}

# 主函数
main() {
    local quick_check=false
    local generate_report=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -r|--report)
                generate_report=true
                shift
                ;;
            --quick)
                quick_check=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始健康检查..."
    print_info "命名空间: $NAMESPACE"
    print_info "超时时间: $TIMEOUT 秒"
    echo ""
    
    local total_checks=0
    local failed_checks=0
    
    # 基础检查
    ((total_checks++))
    check_kubernetes_connection || ((failed_checks++))
    
    ((total_checks++))
    check_namespace || ((failed_checks++))
    
    ((total_checks++))
    check_pods || ((failed_checks++))
    
    ((total_checks++))
    check_services || ((failed_checks++))
    
    ((total_checks++))
    check_ingress || ((failed_checks++))
    
    ((total_checks++))
    check_persistent_volumes || ((failed_checks++))
    
    ((total_checks++))
    check_resource_usage || ((failed_checks++))
    
    ((total_checks++))
    check_configs_and_secrets || ((failed_checks++))
    
    # 扩展检查（非快速模式）
    if [[ "$quick_check" != "true" ]]; then
        ((total_checks++))
        check_network_connectivity || ((failed_checks++))
        
        ((total_checks++))
        check_application_health || ((failed_checks++))
    fi
    
    # 生成报告
    if [[ "$generate_report" == "true" ]]; then
        generate_health_report
    fi
    
    # 总结
    echo ""
    print_title "健康检查总结"
    
    local success_rate=$(( (total_checks - failed_checks) * 100 / total_checks ))
    
    if [[ $failed_checks -eq 0 ]]; then
        print_success "所有检查通过 ($total_checks/$total_checks) - 系统健康状态良好 ✨"
        exit 0
    elif [[ $success_rate -ge 80 ]]; then
        print_warning "大部分检查通过 ($((total_checks - failed_checks))/$total_checks) - 系统基本正常，但有 $failed_checks 个问题需要关注"
        exit 1
    else
        print_error "多项检查失败 ($((total_checks - failed_checks))/$total_checks) - 系统存在严重问题，需要立即处理"
        exit 2
    fi
}

# 脚本入口
main "$@"
