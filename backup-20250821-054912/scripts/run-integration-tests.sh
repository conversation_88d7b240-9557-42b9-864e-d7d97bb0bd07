#!/bin/bash

# PaaS 平台集成测试运行脚本
# 启动测试环境并运行完整的集成测试套件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_DB_NAME="paas_integration_test"
TEST_DB_USER="postgres"
TEST_DB_PASSWORD="password"
TEST_DB_HOST="localhost"
TEST_DB_PORT="5432"

echo -e "${BLUE}🧪 PaaS 平台集成测试运行脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查测试依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi
    
    # 检查 PostgreSQL 客户端
    if ! command -v psql &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：启动测试数据库
start_test_database() {
    print_info "启动测试数据库..."
    
    # 检查是否已有测试数据库容器在运行
    if docker ps --format "table {{.Names}}" | grep -q "paas-test-db"; then
        print_info "测试数据库容器已在运行，停止并重新创建..."
        docker stop paas-test-db || true
        docker rm paas-test-db || true
    fi
    
    # 启动 PostgreSQL 测试数据库
    docker run -d \
        --name paas-test-db \
        -e POSTGRES_DB="$TEST_DB_NAME" \
        -e POSTGRES_USER="$TEST_DB_USER" \
        -e POSTGRES_PASSWORD="$TEST_DB_PASSWORD" \
        -p "$TEST_DB_PORT:5432" \
        postgres:13-alpine
    
    # 等待数据库启动
    print_info "等待数据库启动..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker exec paas-test-db pg_isready -U "$TEST_DB_USER" -d "$TEST_DB_NAME" &> /dev/null; then
            print_success "测试数据库启动成功"
            return 0
        fi
        
        print_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "测试数据库启动超时"
    return 1
}

# 函数：启动测试服务
start_test_services() {
    print_info "启动测试服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置测试环境变量
    export TEST_MODE=true
    export DB_HOST="$TEST_DB_HOST"
    export DB_PORT="$TEST_DB_PORT"
    export DB_USER="$TEST_DB_USER"
    export DB_PASSWORD="$TEST_DB_PASSWORD"
    export DB_NAME="$TEST_DB_NAME"
    export JWT_SECRET="test-jwt-secret-for-integration-tests"
    export LOG_LEVEL="debug"
    
    # 构建测试版本
    print_info "构建测试版本..."
    go build -o bin/api-gateway-test cmd/api-gateway/main.go
    go build -o bin/user-service-test cmd/user-service/main.go
    go build -o bin/app-manager-test cmd/app-manager/main.go
    go build -o bin/cicd-service-test cmd/cicd-service/main.go
    
    # 启动服务（后台运行）
    print_info "启动 API Gateway..."
    ./bin/api-gateway-test &
    API_GATEWAY_PID=$!
    
    print_info "启动 User Service..."
    ./bin/user-service-test &
    USER_SERVICE_PID=$!
    
    print_info "启动 App Manager..."
    ./bin/app-manager-test &
    APP_MANAGER_PID=$!
    
    print_info "启动 CI/CD Service..."
    ./bin/cicd-service-test &
    CICD_SERVICE_PID=$!
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    local services=(
        "http://localhost:8080/health"
        "http://localhost:8081/health"
        "http://localhost:8082/health"
        "http://localhost:8083/health"
    )
    
    for service in "${services[@]}"; do
        if curl -f "$service" &> /dev/null; then
            print_success "服务健康检查通过: $service"
        else
            print_warning "服务健康检查失败: $service"
        fi
    done
    
    print_success "测试服务启动完成"
}

# 函数：运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    
    cd "$PROJECT_ROOT"
    
    # 设置测试环境变量
    export INTEGRATION_TEST=true
    export TEST_DB_HOST="$TEST_DB_HOST"
    export TEST_DB_PORT="$TEST_DB_PORT"
    export TEST_DB_USER="$TEST_DB_USER"
    export TEST_DB_PASSWORD="$TEST_DB_PASSWORD"
    export TEST_DB_NAME="$TEST_DB_NAME"
    export TEST_BASE_URL="http://localhost:8080/api/v1"
    
    # 运行集成测试
    print_info "执行集成测试套件..."
    
    if go test -v -timeout=10m ./tests/integration/... -coverprofile=coverage-integration.out; then
        print_success "集成测试全部通过"
        
        # 生成测试覆盖率报告
        print_info "生成测试覆盖率报告..."
        go tool cover -html=coverage-integration.out -o coverage-integration.html
        
        # 显示覆盖率统计
        local coverage=$(go tool cover -func=coverage-integration.out | grep total | awk '{print $3}')
        print_success "集成测试覆盖率: $coverage"
        
        return 0
    else
        print_error "集成测试失败"
        return 1
    fi
}

# 函数：运行端到端测试
run_e2e_tests() {
    print_info "运行端到端测试..."
    
    # 检查是否有前端测试
    if [[ -f "$PROJECT_ROOT/web/package.json" ]]; then
        cd "$PROJECT_ROOT/web"
        
        # 安装依赖
        if [[ -f "package-lock.json" ]]; then
            npm ci
        else
            npm install
        fi
        
        # 运行 E2E 测试
        if npm run test:e2e; then
            print_success "端到端测试通过"
        else
            print_warning "端到端测试失败"
        fi
    else
        print_info "未找到前端项目，跳过端到端测试"
    fi
}

# 函数：生成测试报告
generate_test_report() {
    print_info "生成测试报告..."
    
    local report_file="$PROJECT_ROOT/integration-test-report.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > "$report_file" << EOF
# PaaS 平台集成测试报告

**生成时间**: $timestamp  
**测试环境**: 集成测试环境  
**数据库**: PostgreSQL (测试实例)

## 测试概览

### 测试套件
- ✅ 用户认证流程测试
- ✅ 应用生命周期测试
- ✅ CI/CD 流水线集成测试
- ✅ 用户管理集成测试
- ✅ 跨服务数据一致性测试

### 测试覆盖范围
- **微服务间通信**: API Gateway ↔ 各业务服务
- **数据库操作**: CRUD 操作和事务一致性
- **认证授权**: JWT Token 验证和权限控制
- **业务流程**: 完整的业务操作流程
- **错误处理**: 异常情况和错误恢复

### 测试结果
EOF

    # 添加测试结果
    if [[ -f "coverage-integration.out" ]]; then
        local coverage=$(go tool cover -func=coverage-integration.out | grep total | awk '{print $3}')
        echo "- **集成测试覆盖率**: $coverage" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
- **测试状态**: 通过
- **测试时长**: $(date -u +"%Y-%m-%d %H:%M:%S")

### 测试环境配置
- **API Gateway**: http://localhost:8080
- **User Service**: http://localhost:8081
- **App Manager**: http://localhost:8082
- **CI/CD Service**: http://localhost:8083
- **测试数据库**: PostgreSQL (localhost:5432)

### 关键测试场景

#### 1. 用户认证流程
- 用户登录和 Token 获取
- 认证 Token 验证
- 用户信息获取
- 权限验证

#### 2. 应用管理流程
- 应用创建和配置
- 应用状态管理 (启动/停止)
- 应用更新和删除
- 应用权限控制

#### 3. CI/CD 流水线
- 流水线创建和配置
- 流水线执行和监控
- 流水线状态管理
- 执行历史查询

#### 4. 数据一致性
- 跨服务数据同步
- 事务完整性验证
- 并发操作处理
- 数据完整性检查

## 测试建议

### 持续改进
1. 增加更多边界条件测试
2. 添加性能基准测试
3. 扩展错误场景覆盖
4. 增强并发测试

### 监控指标
1. 响应时间监控
2. 错误率统计
3. 资源使用监控
4. 数据库性能监控

---

**报告生成**: PaaS 平台集成测试脚本  
**版本**: 1.0.0
EOF

    print_success "测试报告已生成: $report_file"
}

# 函数：清理测试环境
cleanup_test_environment() {
    print_info "清理测试环境..."
    
    # 停止测试服务
    if [[ -n "$API_GATEWAY_PID" ]]; then
        kill "$API_GATEWAY_PID" 2>/dev/null || true
    fi
    if [[ -n "$USER_SERVICE_PID" ]]; then
        kill "$USER_SERVICE_PID" 2>/dev/null || true
    fi
    if [[ -n "$APP_MANAGER_PID" ]]; then
        kill "$APP_MANAGER_PID" 2>/dev/null || true
    fi
    if [[ -n "$CICD_SERVICE_PID" ]]; then
        kill "$CICD_SERVICE_PID" 2>/dev/null || true
    fi
    
    # 停止测试数据库
    if docker ps --format "table {{.Names}}" | grep -q "paas-test-db"; then
        print_info "停止测试数据库..."
        docker stop paas-test-db
        docker rm paas-test-db
    fi
    
    # 清理测试文件
    rm -f "$PROJECT_ROOT"/bin/*-test
    
    print_success "测试环境清理完成"
}

# 函数：显示测试结果
show_test_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 集成测试完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 测试结果："
    if [[ -f "coverage-integration.out" ]]; then
        local coverage=$(go tool cover -func=coverage-integration.out | grep total | awk '{print $3}')
        echo "  • 集成测试覆盖率: $coverage"
    fi
    echo "  • 测试报告: integration-test-report.md"
    echo "  • 覆盖率报告: coverage-integration.html"
    echo ""
    
    echo "🔧 下一步操作："
    echo "  1. 查看详细的测试报告"
    echo "  2. 分析测试覆盖率"
    echo "  3. 修复发现的问题"
    echo "  4. 持续改进测试用例"
    echo ""
}

# 主函数
main() {
    local skip_cleanup=false
    local run_e2e=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                skip_cleanup=true
                shift
                ;;
            --with-e2e)
                run_e2e=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --skip-cleanup   跳过测试环境清理"
                echo "  --with-e2e       运行端到端测试"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    if [[ "$skip_cleanup" != "true" ]]; then
        trap cleanup_test_environment EXIT
    fi
    
    # 执行测试流程
    check_dependencies
    start_test_database
    start_test_services
    
    # 运行集成测试
    if run_integration_tests; then
        # 可选运行端到端测试
        if [[ "$run_e2e" == "true" ]]; then
            run_e2e_tests
        fi
        
        generate_test_report
        show_test_results
        
        exit 0
    else
        print_error "集成测试失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
