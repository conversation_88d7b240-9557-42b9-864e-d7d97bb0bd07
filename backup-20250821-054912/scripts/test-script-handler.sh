#!/bin/bash

# 脚本执行 Handler 功能测试脚本
# 用于测试脚本执行服务的各项功能

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
SERVICE_URL="http://localhost:8084"
API_BASE="${SERVICE_URL}/api/v1"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 测试结果函数
test_pass() {
    ((TOTAL_TESTS++))
    ((PASSED_TESTS++))
    echo -e "${GREEN}✓ PASS${NC} $1"
}

test_fail() {
    ((TOTAL_TESTS++))
    ((FAILED_TESTS++))
    echo -e "${RED}✗ FAIL${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
脚本执行 Handler 功能测试脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -u, --url URL       指定服务URL (默认: http://localhost:8084)
    -t, --token TOKEN   指定认证令牌
    --skip-auth         跳过认证测试
    --only TEST_NAME    只运行指定的测试

测试类型:
    health              健康检查测试
    auth                认证测试
    script              脚本执行测试
    template            模板管理测试
    schedule            任务调度测试
    stats               统计功能测试
    all                 运行所有测试 (默认)

示例:
    $0                          # 运行所有测试
    $0 --only health            # 只运行健康检查测试
    $0 -u http://localhost:9000 # 指定服务URL
    $0 -t "Bearer your-token"   # 指定认证令牌

环境变量:
    SERVICE_URL         服务URL
    AUTH_TOKEN          认证令牌
EOF
}

# 发送HTTP请求
http_request() {
    local method=$1
    local endpoint=$2
    local data=${3:-""}
    local headers=${4:-""}
    
    local url="${API_BASE}${endpoint}"
    local curl_cmd="curl -s -w '%{http_code}' -X ${method}"
    
    # 添加认证头
    if [[ -n "${AUTH_TOKEN:-}" ]]; then
        curl_cmd="${curl_cmd} -H 'Authorization: ${AUTH_TOKEN}'"
    fi
    
    # 添加内容类型头
    if [[ -n "$data" ]]; then
        curl_cmd="${curl_cmd} -H 'Content-Type: application/json' -d '${data}'"
    fi
    
    # 添加额外头部
    if [[ -n "$headers" ]]; then
        curl_cmd="${curl_cmd} ${headers}"
    fi
    
    curl_cmd="${curl_cmd} '${url}'"
    
    # 执行请求
    eval "$curl_cmd"
}

# 健康检查测试
test_health_check() {
    log_test "开始健康检查测试..."
    
    # 测试健康检查接口
    local response=$(curl -s "${SERVICE_URL}/health")
    local status_code=$(curl -s -w '%{http_code}' -o /dev/null "${SERVICE_URL}/health")
    
    if [[ "$status_code" == "200" ]]; then
        test_pass "健康检查接口响应正常"
    else
        test_fail "健康检查接口响应异常，状态码: $status_code"
        return 1
    fi
    
    # 检查响应内容
    if echo "$response" | jq -e '.status == "healthy"' > /dev/null 2>&1; then
        test_pass "健康检查状态正确"
    else
        test_fail "健康检查状态错误"
    fi
    
    # 测试就绪检查接口
    local ready_status=$(curl -s -w '%{http_code}' -o /dev/null "${SERVICE_URL}/ready")
    if [[ "$ready_status" == "200" ]]; then
        test_pass "就绪检查接口响应正常"
    else
        test_fail "就绪检查接口响应异常，状态码: $ready_status"
    fi
}

# 认证测试
test_authentication() {
    log_test "开始认证测试..."
    
    # 测试无认证访问
    local no_auth_status=$(curl -s -w '%{http_code}' -o /dev/null "${API_BASE}/scripts/tasks")
    if [[ "$no_auth_status" == "401" ]]; then
        test_pass "无认证访问正确返回401"
    else
        test_fail "无认证访问未返回401，状态码: $no_auth_status"
    fi
    
    # 测试错误认证
    local bad_auth_status=$(curl -s -w '%{http_code}' -o /dev/null -H "Authorization: Bearer invalid-token" "${API_BASE}/scripts/tasks")
    if [[ "$bad_auth_status" == "401" ]]; then
        test_pass "错误认证正确返回401"
    else
        test_fail "错误认证未返回401，状态码: $bad_auth_status"
    fi
}

# 脚本执行测试
test_script_execution() {
    log_test "开始脚本执行测试..."
    
    # 测试提交脚本执行任务
    local execute_data='{
        "app_id": "test-app-001",
        "script_path": "test.py",
        "runtime_type": "python",
        "parameters": {
            "input": "test data",
            "output_format": "json"
        },
        "environment": {
            "TEST_ENV": "true"
        },
        "timeout": 300,
        "priority": 5
    }'
    
    local execute_response=$(http_request "POST" "/scripts/execute" "$execute_data")
    local execute_status="${execute_response: -3}"
    local execute_body="${execute_response%???}"
    
    if [[ "$execute_status" == "201" ]]; then
        test_pass "脚本执行任务提交成功"
        
        # 提取任务ID
        local task_id=$(echo "$execute_body" | jq -r '.task_id')
        if [[ "$task_id" != "null" && -n "$task_id" ]]; then
            test_pass "获取任务ID成功: $task_id"
            
            # 测试获取任务状态
            local status_response=$(http_request "GET" "/scripts/tasks/${task_id}")
            local status_code="${status_response: -3}"
            if [[ "$status_code" == "200" ]]; then
                test_pass "获取任务状态成功"
            else
                test_fail "获取任务状态失败，状态码: $status_code"
            fi
            
            # 测试获取任务日志
            local logs_response=$(http_request "GET" "/scripts/tasks/${task_id}/logs")
            local logs_code="${logs_response: -3}"
            if [[ "$logs_code" == "200" ]]; then
                test_pass "获取任务日志成功"
            else
                test_fail "获取任务日志失败，状态码: $logs_code"
            fi
            
        else
            test_fail "获取任务ID失败"
        fi
    else
        test_fail "脚本执行任务提交失败，状态码: $execute_status"
    fi
    
    # 测试获取任务列表
    local list_response=$(http_request "GET" "/scripts/tasks?page=1&page_size=10")
    local list_status="${list_response: -3}"
    if [[ "$list_status" == "200" ]]; then
        test_pass "获取任务列表成功"
    else
        test_fail "获取任务列表失败，状态码: $list_status"
    fi
}

# 模板管理测试
test_template_management() {
    log_test "开始模板管理测试..."
    
    # 测试创建模板
    local template_data='{
        "name": "测试模板",
        "description": "用于测试的Python脚本模板",
        "category": "test",
        "language": "python",
        "version": "1.0.0",
        "content": "print(\"Hello, World!\")",
        "parameters": {
            "message": {
                "type": "string",
                "required": true
            }
        },
        "tags": ["test", "demo"]
    }'
    
    local create_response=$(http_request "POST" "/script-templates" "$template_data")
    local create_status="${create_response: -3}"
    local create_body="${create_response%???}"
    
    if [[ "$create_status" == "201" ]]; then
        test_pass "创建模板成功"
        
        # 提取模板ID
        local template_id=$(echo "$create_body" | jq -r '.id')
        if [[ "$template_id" != "null" && -n "$template_id" ]]; then
            test_pass "获取模板ID成功: $template_id"
            
            # 测试获取模板详情
            local get_response=$(http_request "GET" "/script-templates/${template_id}")
            local get_status="${get_response: -3}"
            if [[ "$get_status" == "200" ]]; then
                test_pass "获取模板详情成功"
            else
                test_fail "获取模板详情失败，状态码: $get_status"
            fi
            
            # 测试更新模板
            local update_data='{
                "name": "更新的测试模板",
                "description": "更新后的模板描述",
                "status": "active"
            }'
            local update_response=$(http_request "PUT" "/script-templates/${template_id}" "$update_data")
            local update_status="${update_response: -3}"
            if [[ "$update_status" == "200" ]]; then
                test_pass "更新模板成功"
            else
                test_fail "更新模板失败，状态码: $update_status"
            fi
            
            # 测试删除模板
            local delete_response=$(http_request "DELETE" "/script-templates/${template_id}")
            local delete_status="${delete_response: -3}"
            if [[ "$delete_status" == "200" ]]; then
                test_pass "删除模板成功"
            else
                test_fail "删除模板失败，状态码: $delete_status"
            fi
            
        else
            test_fail "获取模板ID失败"
        fi
    else
        test_fail "创建模板失败，状态码: $create_status"
    fi
    
    # 测试获取模板列表
    local list_response=$(http_request "GET" "/script-templates?page=1&page_size=10")
    local list_status="${list_response: -3}"
    if [[ "$list_status" == "200" ]]; then
        test_pass "获取模板列表成功"
    else
        test_fail "获取模板列表失败，状态码: $list_status"
    fi
}

# 任务调度测试
test_task_scheduling() {
    log_test "开始任务调度测试..."
    
    # 测试创建调度
    local schedule_data='{
        "name": "测试调度",
        "description": "用于测试的定时任务",
        "app_id": "test-app-001",
        "script_path": "scheduled_task.py",
        "parameters": {
            "mode": "test"
        },
        "cron_expr": "0 0 * * *",
        "timezone": "Asia/Shanghai"
    }'
    
    local create_response=$(http_request "POST" "/script-schedules" "$schedule_data")
    local create_status="${create_response: -3}"
    local create_body="${create_response%???}"
    
    if [[ "$create_status" == "201" ]]; then
        test_pass "创建调度成功"
        
        # 提取调度ID
        local schedule_id=$(echo "$create_body" | jq -r '.id')
        if [[ "$schedule_id" != "null" && -n "$schedule_id" ]]; then
            test_pass "获取调度ID成功: $schedule_id"
            
            # 测试获取调度详情
            local get_response=$(http_request "GET" "/script-schedules/${schedule_id}")
            local get_status="${get_response: -3}"
            if [[ "$get_status" == "200" ]]; then
                test_pass "获取调度详情成功"
            else
                test_fail "获取调度详情失败，状态码: $get_status"
            fi
            
            # 测试手动触发调度
            local trigger_data='{
                "parameters": {
                    "manual": true
                }
            }'
            local trigger_response=$(http_request "POST" "/script-schedules/${schedule_id}/trigger" "$trigger_data")
            local trigger_status="${trigger_response: -3}"
            if [[ "$trigger_status" == "200" ]]; then
                test_pass "手动触发调度成功"
            else
                test_fail "手动触发调度失败，状态码: $trigger_status"
            fi
            
            # 测试删除调度
            local delete_response=$(http_request "DELETE" "/script-schedules/${schedule_id}")
            local delete_status="${delete_response: -3}"
            if [[ "$delete_status" == "200" ]]; then
                test_pass "删除调度成功"
            else
                test_fail "删除调度失败，状态码: $delete_status"
            fi
            
        else
            test_fail "获取调度ID失败"
        fi
    else
        test_fail "创建调度失败，状态码: $create_status"
    fi
    
    # 测试获取调度列表
    local list_response=$(http_request "GET" "/script-schedules?page=1&page_size=10")
    local list_status="${list_response: -3}"
    if [[ "$list_status" == "200" ]]; then
        test_pass "获取调度列表成功"
    else
        test_fail "获取调度列表失败，状态码: $list_status"
    fi
}

# 统计功能测试
test_statistics() {
    log_test "开始统计功能测试..."
    
    # 测试获取统计概览
    local overview_response=$(http_request "GET" "/script-stats/overview")
    local overview_status="${overview_response: -3}"
    if [[ "$overview_status" == "200" ]]; then
        test_pass "获取统计概览成功"
    else
        test_fail "获取统计概览失败，状态码: $overview_status"
    fi
    
    # 测试获取指标数据
    local metrics_response=$(http_request "GET" "/script-stats/metrics?time_range=24h&interval=1h")
    local metrics_status="${metrics_response: -3}"
    if [[ "$metrics_status" == "200" ]]; then
        test_pass "获取指标数据成功"
    else
        test_fail "获取指标数据失败，状态码: $metrics_status"
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "开始运行所有测试..."
    
    test_health_check
    
    if [[ "${SKIP_AUTH:-false}" != "true" ]]; then
        test_authentication
    fi
    
    # 注意：以下测试需要有效的认证令牌
    if [[ -n "${AUTH_TOKEN:-}" ]]; then
        test_script_execution
        test_template_management
        test_task_scheduling
        test_statistics
    else
        log_warn "未提供认证令牌，跳过需要认证的测试"
    fi
}

# 显示测试结果
show_test_results() {
    echo
    echo "========================================="
    echo "测试结果汇总"
    echo "========================================="
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}所有测试通过！${NC}"
        exit 0
    else
        echo -e "${RED}有测试失败！${NC}"
        exit 1
    fi
}

# 主函数
main() {
    local service_url="http://localhost:8084"
    local auth_token=""
    local skip_auth=false
    local only_test=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                service_url="$2"
                shift 2
                ;;
            -t|--token)
                auth_token="$2"
                shift 2
                ;;
            --skip-auth)
                skip_auth=true
                shift
                ;;
            --only)
                only_test="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    SERVICE_URL="${SERVICE_URL:-$service_url}"
    API_BASE="${SERVICE_URL}/api/v1"
    AUTH_TOKEN="${AUTH_TOKEN:-$auth_token}"
    SKIP_AUTH="$skip_auth"
    
    log_info "开始测试脚本执行 Handler 功能..."
    log_info "服务URL: $SERVICE_URL"
    
    # 检查服务是否可用
    if ! curl -s "$SERVICE_URL/health" > /dev/null; then
        log_error "服务不可用，请确保服务已启动: $SERVICE_URL"
        exit 1
    fi
    
    # 运行指定测试或所有测试
    case "${only_test:-all}" in
        health)
            test_health_check
            ;;
        auth)
            test_authentication
            ;;
        script)
            test_script_execution
            ;;
        template)
            test_template_management
            ;;
        schedule)
            test_task_scheduling
            ;;
        stats)
            test_statistics
            ;;
        all)
            run_all_tests
            ;;
        *)
            log_error "未知的测试类型: $only_test"
            show_help
            exit 1
            ;;
    esac
    
    # 显示测试结果
    show_test_results
}

# 执行主函数
main "$@"
