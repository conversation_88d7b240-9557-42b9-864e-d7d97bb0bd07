-- PaaS 平台数据库初始化脚本

-- 创建 Gitea 数据库
CREATE DATABASE IF NOT EXISTS gitea;

-- 创建扩展 (PostgreSQL)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建默认租户
INSERT INTO tenants (id, name, description, status, created_at, updated_at) 
VALUES (
    'default-tenant-123', 
    '默认租户', 
    '系统默认租户，用于开发和测试', 
    'active', 
    NOW(), 
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- 创建默认管理员用户
INSERT INTO users (id, username, email, password_hash, role, tenant_id, status, created_at, updated_at)
VALUES (
    'admin-user-123',
    'admin',
    '<EMAIL>',
    '$2a$10$N9qo8uLOickgx2ZMRZoMye1J3YJQ.1234567890abcdef', -- 密码: admin123
    'admin',
    'default-tenant-123',
    'active',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- 创建默认角色
INSERT INTO roles (id, name, description, permissions, tenant_id, created_at, updated_at)
VALUES 
    (
        'role-admin-123',
        'admin',
        '管理员角色',
        '["app:*", "user:*", "config:*", "monitor:*"]',
        'default-tenant-123',
        NOW(),
        NOW()
    ),
    (
        'role-developer-123',
        'developer',
        '开发者角色',
        '["app:read", "app:create", "app:update", "app:deploy"]',
        'default-tenant-123',
        NOW(),
        NOW()
    ),
    (
        'role-viewer-123',
        'viewer',
        '查看者角色',
        '["app:read", "monitor:read"]',
        'default-tenant-123',
        NOW(),
        NOW()
    )
ON CONFLICT (id) DO NOTHING;

-- 创建用户角色关联
INSERT INTO user_roles (user_id, role_id, created_at)
VALUES (
    'admin-user-123',
    'role-admin-123',
    NOW()
) ON CONFLICT (user_id, role_id) DO NOTHING;

-- 创建示例应用配置模板
INSERT INTO app_templates (id, name, description, language, framework, config, created_at, updated_at)
VALUES 
    (
        'template-nodejs-express',
        'Node.js Express 应用',
        'Node.js Express 框架应用模板',
        'nodejs',
        'express',
        '{
            "runtime": {
                "language": "nodejs",
                "version": "18",
                "framework": "express"
            },
            "build": {
                "dockerfile": "Dockerfile",
                "buildArgs": {
                    "NODE_ENV": "production"
                },
                "excludes": ["node_modules", ".git", "*.log"]
            },
            "deploy": {
                "port": 3000,
                "instances": 1,
                "resources": {
                    "requests": {
                        "cpu": "100m",
                        "memory": "128Mi"
                    },
                    "limits": {
                        "cpu": "500m",
                        "memory": "512Mi"
                    }
                },
                "healthCheck": {
                    "path": "/health",
                    "port": 3000,
                    "initialDelaySeconds": 30,
                    "periodSeconds": 10,
                    "timeoutSeconds": 5,
                    "failureThreshold": 3
                }
            }
        }',
        NOW(),
        NOW()
    ),
    (
        'template-python-flask',
        'Python Flask 应用',
        'Python Flask 框架应用模板',
        'python',
        'flask',
        '{
            "runtime": {
                "language": "python",
                "version": "3.11",
                "framework": "flask"
            },
            "build": {
                "requirements": "requirements.txt",
                "buildArgs": {
                    "PYTHON_ENV": "production"
                }
            },
            "deploy": {
                "port": 5000,
                "instances": 1,
                "command": ["python", "app.py"],
                "resources": {
                    "requests": {
                        "cpu": "100m",
                        "memory": "128Mi"
                    },
                    "limits": {
                        "cpu": "300m",
                        "memory": "256Mi"
                    }
                },
                "healthCheck": {
                    "path": "/health",
                    "port": 5000,
                    "initialDelaySeconds": 20,
                    "periodSeconds": 10
                }
            }
        }',
        NOW(),
        NOW()
    )
ON CONFLICT (id) DO NOTHING;

-- 创建系统配置
INSERT INTO system_configs (key, value, description, created_at, updated_at)
VALUES 
    ('platform.name', 'PaaS 平台', '平台名称', NOW(), NOW()),
    ('platform.version', '1.0.0', '平台版本', NOW(), NOW()),
    ('platform.description', '企业级 PaaS 平台', '平台描述', NOW(), NOW()),
    ('docker.registry', 'localhost:5000', 'Docker 镜像仓库地址', NOW(), NOW()),
    ('gitea.url', 'http://localhost:3000', 'Gitea 服务地址', NOW(), NOW()),
    ('build.timeout', '1800', '构建超时时间(秒)', NOW(), NOW()),
    ('deploy.timeout', '300', '部署超时时间(秒)', NOW(), NOW())
ON CONFLICT (key) DO NOTHING;
