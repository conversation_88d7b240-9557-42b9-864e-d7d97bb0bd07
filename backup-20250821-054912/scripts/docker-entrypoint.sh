#!/bin/sh

# PaaS 平台 Docker 容器入口脚本
# 处理容器启动、配置初始化和优雅关闭

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME=${SERVICE_NAME:-"unknown"}
CONFIG_PATH=${CONFIG_PATH:-"/app/configs"}
LOG_PATH=${LOG_PATH:-"/app/logs"}
DATA_PATH=${DATA_PATH:-"/app/data"}
PORT=${PORT:-8080}

# 函数：打印日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 函数：检查必要目录
check_directories() {
    log_info "检查必要目录..."
    
    for dir in "$CONFIG_PATH" "$LOG_PATH" "$DATA_PATH"; do
        if [ ! -d "$dir" ]; then
            log_warn "目录不存在，正在创建: $dir"
            mkdir -p "$dir"
        fi
    done
    
    log_success "目录检查完成"
}

# 函数：初始化配置
init_config() {
    log_info "初始化配置..."
    
    # 设置默认配置文件
    local config_file="$CONFIG_PATH/${SERVICE_NAME}.yaml"
    
    if [ ! -f "$config_file" ]; then
        log_warn "配置文件不存在，创建默认配置: $config_file"
        create_default_config "$config_file"
    fi
    
    # 处理环境变量替换
    if command -v envsubst >/dev/null 2>&1; then
        log_info "处理配置文件中的环境变量..."
        envsubst < "$config_file" > "${config_file}.tmp" && mv "${config_file}.tmp" "$config_file"
    fi
    
    log_success "配置初始化完成"
}

# 函数：创建默认配置
create_default_config() {
    local config_file="$1"
    
    cat > "$config_file" << EOF
# PaaS 平台 ${SERVICE_NAME} 服务配置

server:
  port: ${PORT}
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

logging:
  level: "\${LOG_LEVEL:-info}"
  format: "json"
  output: "stdout"
  file: "${LOG_PATH}/${SERVICE_NAME}.log"

database:
  host: "\${DB_HOST:-localhost}"
  port: \${DB_PORT:-5432}
  user: "\${DB_USER:-postgres}"
  password: "\${DB_PASSWORD:-password}"
  dbname: "\${DB_NAME:-paas_platform}"
  sslmode: "\${DB_SSLMODE:-disable}"

security:
  jwt_secret: "\${JWT_SECRET:-default-secret}"
  api_key: "\${API_KEY:-}"

monitoring:
  enabled: true
  metrics_port: \${METRICS_PORT:-9090}
  health_check_interval: 30s
EOF
    
    log_info "默认配置文件已创建: $config_file"
}

# 函数：等待依赖服务
wait_for_dependencies() {
    log_info "等待依赖服务..."
    
    # 等待数据库
    if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
        log_info "等待数据库连接: $DB_HOST:$DB_PORT"
        wait_for_service "$DB_HOST" "$DB_PORT" 60
    fi
    
    # 等待 Redis
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        log_info "等待 Redis 连接: $REDIS_HOST:$REDIS_PORT"
        wait_for_service "$REDIS_HOST" "$REDIS_PORT" 30
    fi
    
    # 等待其他服务
    if [ -n "$WAIT_FOR_SERVICES" ]; then
        IFS=',' read -ra SERVICES <<< "$WAIT_FOR_SERVICES"
        for service in "${SERVICES[@]}"; do
            IFS=':' read -ra SERVICE_PARTS <<< "$service"
            local host="${SERVICE_PARTS[0]}"
            local port="${SERVICE_PARTS[1]}"
            log_info "等待服务连接: $host:$port"
            wait_for_service "$host" "$port" 30
        done
    fi
    
    log_success "依赖服务检查完成"
}

# 函数：等待单个服务
wait_for_service() {
    local host="$1"
    local port="$2"
    local timeout="${3:-30}"
    local count=0
    
    while [ $count -lt $timeout ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_success "服务可用: $host:$port"
            return 0
        fi
        
        count=$((count + 1))
        log_info "等待服务 $host:$port... ($count/$timeout)"
        sleep 1
    done
    
    log_error "服务连接超时: $host:$port"
    return 1
}

# 函数：运行数据库迁移
run_migrations() {
    if [ "$RUN_MIGRATIONS" = "true" ] && [ -f "/app/bin/migrate" ]; then
        log_info "运行数据库迁移..."
        
        if /app/bin/migrate -path /app/migrations -database "$DATABASE_URL" up; then
            log_success "数据库迁移完成"
        else
            log_error "数据库迁移失败"
            exit 1
        fi
    fi
}

# 函数：设置信号处理
setup_signal_handlers() {
    log_info "设置信号处理器..."
    
    # 优雅关闭处理
    trap 'graceful_shutdown' TERM INT
    
    log_success "信号处理器设置完成"
}

# 函数：优雅关闭
graceful_shutdown() {
    log_info "接收到关闭信号，开始优雅关闭..."
    
    # 如果有主进程 PID，发送 TERM 信号
    if [ -n "$MAIN_PID" ]; then
        log_info "向主进程发送 TERM 信号: $MAIN_PID"
        kill -TERM "$MAIN_PID" 2>/dev/null || true
        
        # 等待进程结束
        local count=0
        while [ $count -lt 30 ] && kill -0 "$MAIN_PID" 2>/dev/null; do
            count=$((count + 1))
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if kill -0 "$MAIN_PID" 2>/dev/null; then
            log_warn "强制终止进程: $MAIN_PID"
            kill -KILL "$MAIN_PID" 2>/dev/null || true
        fi
    fi
    
    log_success "优雅关闭完成"
    exit 0
}

# 函数：健康检查
health_check() {
    local url="http://localhost:${PORT}/health"
    local max_attempts=30
    local attempt=1
    
    log_info "等待服务健康检查..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$url" >/dev/null 2>&1; then
            log_success "服务健康检查通过"
            return 0
        fi
        
        attempt=$((attempt + 1))
        sleep 2
    done
    
    log_error "服务健康检查失败"
    return 1
}

# 函数：显示启动信息
show_startup_info() {
    log_info "=========================================="
    log_info "PaaS 平台 ${SERVICE_NAME} 服务启动"
    log_info "=========================================="
    log_info "服务名称: $SERVICE_NAME"
    log_info "监听端口: $PORT"
    log_info "配置路径: $CONFIG_PATH"
    log_info "日志路径: $LOG_PATH"
    log_info "数据路径: $DATA_PATH"
    log_info "环境变量: $ENV"
    log_info "版本信息: ${VERSION:-unknown}"
    log_info "构建时间: ${BUILD_TIME:-unknown}"
    log_info "Git 提交: ${GIT_COMMIT:-unknown}"
    log_info "=========================================="
}

# 函数：启动服务
start_service() {
    local binary_path="/app/bin/${SERVICE_NAME}"
    
    if [ ! -f "$binary_path" ]; then
        log_error "服务二进制文件不存在: $binary_path"
        exit 1
    fi
    
    if [ ! -x "$binary_path" ]; then
        log_error "服务二进制文件不可执行: $binary_path"
        exit 1
    fi
    
    log_info "启动服务: $binary_path"
    
    # 启动服务并获取 PID
    "$binary_path" "$@" &
    MAIN_PID=$!
    
    log_info "服务已启动，PID: $MAIN_PID"
    
    # 等待进程结束
    wait $MAIN_PID
    local exit_code=$?
    
    log_info "服务进程结束，退出码: $exit_code"
    exit $exit_code
}

# 函数：开发模式
dev_mode() {
    log_info "启动开发模式..."
    
    # 安装开发工具
    if command -v go >/dev/null 2>&1; then
        log_info "安装 air 热重载工具..."
        go install github.com/cosmtrek/air@latest
        
        # 使用 air 启动
        if [ -f "/app/.air.toml" ]; then
            air -c /app/.air.toml
        else
            air
        fi
    else
        log_warn "Go 未安装，使用普通模式启动"
        start_service "$@"
    fi
}

# 主函数
main() {
    # 显示启动信息
    show_startup_info
    
    # 检查目录
    check_directories
    
    # 初始化配置
    init_config
    
    # 设置信号处理
    setup_signal_handlers
    
    # 等待依赖服务
    if [ "$SKIP_WAIT" != "true" ]; then
        wait_for_dependencies
    fi
    
    # 运行数据库迁移
    run_migrations
    
    # 根据模式启动服务
    if [ "$DEV_MODE" = "true" ]; then
        dev_mode "$@"
    else
        start_service "$@"
    fi
}

# 脚本入口
if [ "${1#-}" != "$1" ] || [ "$1" = "/app/bin/${SERVICE_NAME}" ]; then
    # 如果第一个参数是选项或者是服务二进制文件，执行主函数
    main "$@"
else
    # 否则直接执行传入的命令
    exec "$@"
fi
