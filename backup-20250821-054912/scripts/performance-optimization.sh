#!/bin/bash

# PaaS 平台性能优化脚本
# 分析和优化系统性能瓶颈

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$PROJECT_ROOT/performance-reports"

echo -e "${BLUE}⚡ PaaS 平台性能优化脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖工具
check_dependencies() {
    print_info "检查性能分析工具..."
    
    local missing_tools=()
    
    # 检查基础工具
    for tool in curl jq bc; do
        if ! command -v $tool &> /dev/null; then
            missing_tools+=($tool)
        fi
    done
    
    # 检查 Go 性能工具
    if ! command -v go &> /dev/null; then
        missing_tools+=("go")
    fi
    
    # 检查 pprof
    if ! go list -m github.com/google/pprof &> /dev/null; then
        print_info "安装 pprof..."
        go install github.com/google/pprof@latest
    fi
    
    # 检查 hey (HTTP 负载测试工具)
    if ! command -v hey &> /dev/null; then
        print_info "安装 hey..."
        go install github.com/rakyll/hey@latest
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "缺少必要工具: ${missing_tools[*]}"
        print_info "请安装缺少的工具后重新运行"
        exit 1
    fi
    
    print_success "性能分析工具检查完成"
}

# 函数：创建报告目录
setup_reports_directory() {
    print_info "创建报告目录..."
    
    mkdir -p "$REPORTS_DIR"
    rm -f "$REPORTS_DIR"/*.txt
    rm -f "$REPORTS_DIR"/*.json
    rm -f "$REPORTS_DIR"/*.html
    rm -f "$REPORTS_DIR"/*.svg
    
    print_success "报告目录准备完成"
}

# 函数：分析系统资源使用
analyze_system_resources() {
    print_info "分析系统资源使用..."
    
    local system_report="$REPORTS_DIR/system-resources.txt"
    
    echo "# 系统资源使用分析报告" > "$system_report"
    echo "生成时间: $(date)" >> "$system_report"
    echo "" >> "$system_report"
    
    # CPU 信息
    echo "## CPU 信息" >> "$system_report"
    if command -v lscpu &> /dev/null; then
        lscpu >> "$system_report"
    else
        sysctl -n machdep.cpu.brand_string >> "$system_report" 2>/dev/null || echo "CPU信息获取失败" >> "$system_report"
    fi
    echo "" >> "$system_report"
    
    # 内存信息
    echo "## 内存信息" >> "$system_report"
    if command -v free &> /dev/null; then
        free -h >> "$system_report"
    else
        vm_stat >> "$system_report" 2>/dev/null || echo "内存信息获取失败" >> "$system_report"
    fi
    echo "" >> "$system_report"
    
    # 磁盘使用
    echo "## 磁盘使用" >> "$system_report"
    df -h >> "$system_report"
    echo "" >> "$system_report"
    
    # 网络连接
    echo "## 网络连接统计" >> "$system_report"
    if command -v ss &> /dev/null; then
        ss -s >> "$system_report"
    elif command -v netstat &> /dev/null; then
        netstat -s >> "$system_report" 2>/dev/null | head -20
    fi
    echo "" >> "$system_report"
    
    # 负载平均值
    echo "## 系统负载" >> "$system_report"
    uptime >> "$system_report"
    echo "" >> "$system_report"
    
    print_info "报告已保存: $system_report"
}

# 函数：分析数据库性能
analyze_database_performance() {
    print_info "分析数据库性能..."
    
    local db_report="$REPORTS_DIR/database-performance.txt"
    
    echo "# 数据库性能分析报告" > "$db_report"
    echo "生成时间: $(date)" >> "$db_report"
    echo "" >> "$db_report"
    
    # 检查数据库连接
    local db_url="${DATABASE_URL:-postgresql://localhost:5432/paas_db}"
    
    if command -v psql &> /dev/null; then
        print_info "分析 PostgreSQL 性能..."
        
        echo "## PostgreSQL 连接统计" >> "$db_report"
        psql "$db_url" -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';" >> "$db_report" 2>/dev/null || echo "无法连接到数据库" >> "$db_report"
        
        echo "" >> "$db_report"
        echo "## 慢查询统计" >> "$db_report"
        psql "$db_url" -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;" >> "$db_report" 2>/dev/null || echo "pg_stat_statements 扩展未启用" >> "$db_report"
        
        echo "" >> "$db_report"
        echo "## 数据库大小" >> "$db_report"
        psql "$db_url" -c "SELECT pg_size_pretty(pg_database_size(current_database())) as database_size;" >> "$db_report" 2>/dev/null || echo "无法获取数据库大小" >> "$db_report"
        
        echo "" >> "$db_report"
        echo "## 表大小统计" >> "$db_report"
        psql "$db_url" -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC LIMIT 10;" >> "$db_report" 2>/dev/null || echo "无法获取表大小统计" >> "$db_report"
        
    else
        echo "PostgreSQL 客户端未安装，跳过数据库分析" >> "$db_report"
    fi
    
    print_info "报告已保存: $db_report"
}

# 函数：进行 HTTP 负载测试
perform_load_testing() {
    print_info "进行 HTTP 负载测试..."
    
    local load_test_report="$REPORTS_DIR/load-test.txt"
    local base_url="${BASE_URL:-http://localhost:8080}"
    
    echo "# HTTP 负载测试报告" > "$load_test_report"
    echo "生成时间: $(date)" >> "$load_test_report"
    echo "测试目标: $base_url" >> "$load_test_report"
    echo "" >> "$load_test_report"
    
    # 检查服务是否可用
    if ! curl -s -f "$base_url/health" > /dev/null; then
        echo "服务不可用，跳过负载测试" >> "$load_test_report"
        print_warning "服务 $base_url 不可用，跳过负载测试"
        return 0
    fi
    
    # 轻量级测试 (10个并发，持续10秒)
    print_info "执行轻量级负载测试..."
    echo "## 轻量级测试 (10并发, 10秒)" >> "$load_test_report"
    hey -n 100 -c 10 -t 30 "$base_url/health" >> "$load_test_report" 2>&1
    echo "" >> "$load_test_report"
    
    # 中等负载测试 (50个并发，持续30秒)
    print_info "执行中等负载测试..."
    echo "## 中等负载测试 (50并发, 30秒)" >> "$load_test_report"
    hey -n 500 -c 50 -t 30 "$base_url/health" >> "$load_test_report" 2>&1
    echo "" >> "$load_test_report"
    
    # API 端点测试
    local api_endpoints=(
        "/api/v1/users"
        "/api/v1/applications"
        "/api/v1/pipelines"
    )
    
    for endpoint in "${api_endpoints[@]}"; do
        print_info "测试 API 端点: $endpoint"
        echo "## API 端点测试: $endpoint" >> "$load_test_report"
        hey -n 50 -c 10 -t 30 "$base_url$endpoint" >> "$load_test_report" 2>&1 || echo "端点测试失败: $endpoint" >> "$load_test_report"
        echo "" >> "$load_test_report"
    done
    
    print_info "报告已保存: $load_test_report"
}

# 函数：分析 Go 应用性能
analyze_go_performance() {
    print_info "分析 Go 应用性能..."
    
    local go_report="$REPORTS_DIR/go-performance.txt"
    local pprof_dir="$REPORTS_DIR/pprof"
    
    mkdir -p "$pprof_dir"
    
    echo "# Go 应用性能分析报告" > "$go_report"
    echo "生成时间: $(date)" >> "$go_report"
    echo "" >> "$go_report"
    
    # 检查 pprof 端点是否可用
    local pprof_url="${PPROF_URL:-http://localhost:8080/debug/pprof}"
    
    if curl -s -f "$pprof_url/" > /dev/null; then
        print_info "收集 CPU 性能数据..."
        echo "## CPU 性能分析" >> "$go_report"
        
        # 收集 CPU profile (30秒)
        if curl -s "$pprof_url/profile?seconds=30" -o "$pprof_dir/cpu.prof"; then
            # 生成 CPU 分析报告
            go tool pprof -text "$pprof_dir/cpu.prof" | head -20 >> "$go_report" 2>/dev/null || echo "CPU 分析失败" >> "$go_report"
        else
            echo "无法收集 CPU 性能数据" >> "$go_report"
        fi
        
        echo "" >> "$go_report"
        
        print_info "收集内存性能数据..."
        echo "## 内存性能分析" >> "$go_report"
        
        # 收集内存 profile
        if curl -s "$pprof_url/heap" -o "$pprof_dir/heap.prof"; then
            # 生成内存分析报告
            go tool pprof -text "$pprof_dir/heap.prof" | head -20 >> "$go_report" 2>/dev/null || echo "内存分析失败" >> "$go_report"
        else
            echo "无法收集内存性能数据" >> "$go_report"
        fi
        
        echo "" >> "$go_report"
        
        print_info "收集 Goroutine 信息..."
        echo "## Goroutine 分析" >> "$go_report"
        
        # 收集 goroutine 信息
        curl -s "$pprof_url/goroutine?debug=1" | head -50 >> "$go_report" 2>/dev/null || echo "无法收集 Goroutine 信息" >> "$go_report"
        
    else
        echo "pprof 端点不可用，跳过 Go 性能分析" >> "$go_report"
        print_warning "pprof 端点 $pprof_url 不可用"
    fi
    
    print_info "报告已保存: $go_report"
}

# 函数：分析前端性能
analyze_frontend_performance() {
    print_info "分析前端性能..."
    
    local frontend_report="$REPORTS_DIR/frontend-performance.txt"
    
    echo "# 前端性能分析报告" > "$frontend_report"
    echo "生成时间: $(date)" >> "$frontend_report"
    echo "" >> "$frontend_report"
    
    if [[ -d "$PROJECT_ROOT/web" ]]; then
        cd "$PROJECT_ROOT/web"
        
        # 分析包大小
        echo "## 包大小分析" >> "$frontend_report"
        if [[ -f "package.json" ]]; then
            # 检查依赖大小
            if command -v npm &> /dev/null; then
                echo "### 依赖包大小" >> "$frontend_report"
                npm ls --depth=0 --json 2>/dev/null | jq -r '.dependencies | keys[]' | head -10 >> "$frontend_report" 2>/dev/null || echo "无法分析依赖包" >> "$frontend_report"
            fi
            
            # 检查构建产物大小
            if [[ -d "dist" ]] || [[ -d "build" ]]; then
                echo "### 构建产物大小" >> "$frontend_report"
                find dist build -name "*.js" -o -name "*.css" 2>/dev/null | xargs ls -lh >> "$frontend_report" 2>/dev/null || echo "无构建产物" >> "$frontend_report"
            fi
        fi
        
        echo "" >> "$frontend_report"
        
        # 分析代码质量
        echo "## 代码质量分析" >> "$frontend_report"
        
        # 统计文件数量
        echo "### 文件统计" >> "$frontend_report"
        echo "JavaScript/TypeScript 文件: $(find src -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" 2>/dev/null | wc -l)" >> "$frontend_report"
        echo "CSS/SCSS 文件: $(find src -name "*.css" -o -name "*.scss" -o -name "*.less" 2>/dev/null | wc -l)" >> "$frontend_report"
        echo "图片文件: $(find src -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" 2>/dev/null | wc -l)" >> "$frontend_report"
        
        cd "$PROJECT_ROOT"
    else
        echo "未找到前端项目目录" >> "$frontend_report"
    fi
    
    print_info "报告已保存: $frontend_report"
}

# 函数：生成性能优化建议
generate_optimization_recommendations() {
    print_info "生成性能优化建议..."
    
    local recommendations_file="$REPORTS_DIR/optimization-recommendations.md"
    
    cat > "$recommendations_file" << 'EOF'
# 性能优化建议报告

## 🎯 优化优先级

### 🔴 高优先级（立即处理）
1. **数据库查询优化** - 影响响应时间和用户体验
2. **内存泄漏修复** - 可能导致系统崩溃
3. **慢 API 优化** - 直接影响用户体验

### 🟡 中优先级（近期处理）
1. **缓存策略优化** - 减少数据库负载
2. **静态资源优化** - 提升页面加载速度
3. **并发控制优化** - 提升系统吞吐量

### 🟢 低优先级（有时间时处理）
1. **代码分割优化** - 减少初始加载时间
2. **图片压缩优化** - 减少带宽使用
3. **监控告警完善** - 提前发现性能问题

## 🛠️ 具体优化措施

### 后端优化
```bash
# 1. 数据库连接池优化
# 在配置文件中调整连接池参数
max_connections: 25
max_idle_connections: 5
connection_lifetime: 5m

# 2. 启用查询缓存
# 使用 Redis 缓存频繁查询的结果
redis_cache_ttl: 300s

# 3. 添加数据库索引
# 为经常查询的字段添加索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_apps_owner_id ON applications(owner_id);
```

### 前端优化
```bash
# 1. 启用代码分割
# 在 webpack 配置中启用动态导入
import(/* webpackChunkName: "feature" */ './feature');

# 2. 图片优化
# 使用 WebP 格式和响应式图片
<picture>
  <source srcset="image.webp" type="image/webp">
  <img src="image.jpg" alt="description">
</picture>

# 3. 启用 Gzip 压缩
# 在服务器配置中启用压缩
gzip on;
gzip_types text/css application/javascript;
```

### 系统优化
```bash
# 1. 调整系统参数
# 增加文件描述符限制
ulimit -n 65536

# 2. 优化内存使用
# 设置 Go 应用的内存限制
GOMEMLIMIT=1GiB

# 3. 启用 HTTP/2
# 在负载均衡器中启用 HTTP/2
```

## 📊 性能监控指标

### 关键指标
- **响应时间**: P95 < 500ms, P99 < 1s
- **吞吐量**: > 1000 RPS
- **错误率**: < 0.1%
- **可用性**: > 99.9%

### 资源使用
- **CPU 使用率**: < 70%
- **内存使用率**: < 80%
- **磁盘使用率**: < 85%
- **网络带宽**: < 80%

### 数据库指标
- **连接数**: < 80% 最大连接数
- **查询时间**: P95 < 100ms
- **慢查询**: < 10 个/小时
- **锁等待**: < 1%

## 🔧 实施计划

### 第一阶段（本周）
1. 优化最慢的 5 个 API 接口
2. 添加缺失的数据库索引
3. 启用基础缓存策略

### 第二阶段（下周）
1. 实施连接池优化
2. 优化前端资源加载
3. 完善性能监控

### 第三阶段（下个月）
1. 实施高级缓存策略
2. 优化系统架构
3. 建立性能基线和告警

## 📈 预期收益

### 性能提升
- 🚀 **响应时间**: 减少 30-50%
- 📈 **吞吐量**: 提升 50-100%
- 💾 **内存使用**: 减少 20-30%
- 🔋 **CPU 使用**: 减少 15-25%

### 用户体验
- ⚡ **页面加载**: 提升 40-60%
- 🎯 **交互响应**: 提升 30-50%
- 📱 **移动端性能**: 提升 25-40%

### 运维效益
- 💰 **资源成本**: 节省 20-30%
- 🛡️ **系统稳定性**: 提升 50%
- 🔍 **问题定位**: 提升 60%

## ⚠️ 注意事项

1. **渐进式优化**: 避免一次性大规模修改
2. **性能测试**: 每次优化后进行充分测试
3. **监控验证**: 通过监控数据验证优化效果
4. **回滚准备**: 准备快速回滚方案
5. **文档更新**: 及时更新相关文档

EOF
    
    print_success "优化建议已生成: $recommendations_file"
}

# 函数：生成性能报告
generate_performance_report() {
    print_info "生成性能报告..."
    
    local report_file="$REPORTS_DIR/performance-summary.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 收集基础指标
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//' 2>/dev/null || echo "0")
    local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}' 2>/dev/null || echo "0")
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//' 2>/dev/null || echo "0")
    
    # 生成 JSON 报告
    cat > "$report_file" << EOF
{
  "performance_summary": {
    "timestamp": "$timestamp",
    "system_metrics": {
      "cpu_usage_percent": $cpu_usage,
      "memory_usage_percent": $memory_usage,
      "disk_usage_percent": $disk_usage
    },
    "analysis_reports": {
      "system_resources": "$REPORTS_DIR/system-resources.txt",
      "database_performance": "$REPORTS_DIR/database-performance.txt",
      "load_testing": "$REPORTS_DIR/load-test.txt",
      "go_performance": "$REPORTS_DIR/go-performance.txt",
      "frontend_performance": "$REPORTS_DIR/frontend-performance.txt",
      "optimization_recommendations": "$REPORTS_DIR/optimization-recommendations.md"
    },
    "key_findings": [
      "系统资源使用情况已分析",
      "数据库性能指标已收集",
      "HTTP 负载测试已完成",
      "Go 应用性能已分析",
      "前端性能已评估"
    ],
    "next_actions": [
      "查看优化建议报告",
      "实施高优先级优化",
      "建立性能监控",
      "定期性能评估"
    ]
  }
}
EOF
    
    print_success "性能报告已生成: $report_file"
}

# 函数：显示优化结果
show_optimization_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 性能分析完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 分析报告："
    echo "  • 系统资源:     $REPORTS_DIR/system-resources.txt"
    echo "  • 数据库性能:   $REPORTS_DIR/database-performance.txt"
    echo "  • 负载测试:     $REPORTS_DIR/load-test.txt"
    echo "  • Go 应用性能:  $REPORTS_DIR/go-performance.txt"
    echo "  • 前端性能:     $REPORTS_DIR/frontend-performance.txt"
    echo "  • 优化建议:     $REPORTS_DIR/optimization-recommendations.md"
    echo "  • 总结报告:     $REPORTS_DIR/performance-summary.json"
    echo ""
    
    echo "🔧 下一步操作："
    echo "  1. 查看分析报告，了解性能瓶颈"
    echo "  2. 按优先级实施优化建议"
    echo "  3. 建立性能监控和告警"
    echo "  4. 定期进行性能评估"
    echo ""
    
    echo "⚡ 快速优化命令："
    echo "  $0 --quick-fix   # 执行快速优化"
    echo "  $0 --monitor     # 启动性能监控"
    echo ""
}

# 主函数
main() {
    local quick_fix=false
    local monitor=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --quick-fix)
                quick_fix=true
                shift
                ;;
            --monitor)
                monitor=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --quick-fix      执行快速性能优化"
                echo "  --monitor        启动性能监控"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行性能分析流程
    check_dependencies
    setup_reports_directory
    
    # 分析阶段
    analyze_system_resources
    analyze_database_performance
    perform_load_testing
    analyze_go_performance
    analyze_frontend_performance
    generate_optimization_recommendations
    
    # 报告阶段
    generate_performance_report
    show_optimization_results
    
    if [[ "$quick_fix" == "true" ]]; then
        print_info "快速优化功能开发中..."
    elif [[ "$monitor" == "true" ]]; then
        print_info "性能监控功能开发中..."
    else
        print_info "分析完成，查看报告并实施优化建议"
    fi
}

# 脚本入口
main "$@"
