# 归档脚本说明

本目录包含已归档的重复或过期脚本文件。

## Docker相关脚本
- **docker-image-optimizer.sh**: 功能已整合到docker-optimize.sh
  - 原功能：Docker镜像优化，减小镜像体积和提高构建效率
  - 归档原因：与docker-optimize.sh功能重叠90%
  
- **docker-image-analyzer.sh**: 功能已整合到docker-optimize.sh
  - 原功能：Docker镜像分析，生成镜像大小和层结构报告
  - 归档原因：与docker-optimize.sh功能重叠80%

## 性能相关脚本
- **performance-optimizer.sh**: 功能已整合到performance-optimization.sh
  - 原功能：自动分析和优化 PaaS 平台性能
  - 归档原因：与performance-optimization.sh功能重叠75%

- **performance-analyzer.sh**: 功能已整合到performance-optimization.sh
  - 原功能：深度性能分析，系统瓶颈识别和优化建议
  - 归档原因：与performance-optimization.sh功能重叠70%

## 使用说明

这些脚本已不再维护，如需相关功能请使用对应的主脚本：

- Docker优化功能 → 使用 `scripts/docker-optimize.sh`
- 性能优化功能 → 使用 `scripts/performance-optimization.sh`

## 恢复说明

如果需要恢复某个归档脚本，可以：
```bash
# 从归档目录复制回scripts目录
cp scripts/archived/<脚本名> scripts/

# 或者从git历史恢复
git checkout HEAD~1 -- scripts/<脚本名>
```

归档的脚本仍然保留完整功能，可以在特殊情况下独立使用。
