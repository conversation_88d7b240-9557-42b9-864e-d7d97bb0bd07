#!/bin/bash

# Docker镜像优化脚本
# 用于优化PaaS平台的Docker镜像构建，减小镜像体积和提高构建效率

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_DIR="$PROJECT_ROOT/logs"
readonly LOG_FILE="$LOG_DIR/docker-optimizer-$(date +%Y%m%d-%H%M%S).log"

# 优化配置
readonly OPTIMIZE_LAYERS=true
readonly USE_MULTI_STAGE=true
readonly COMPRESS_IMAGES=true
readonly USE_ALPINE_BASE=true
readonly ENABLE_BUILDKIT=true

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg" | tee -a "$LOG_FILE"
}

print_title() {
    local title="$1"
    echo -e "\n${CYAN}🐳 $title${NC}\n" | tee -a "$LOG_FILE"
}

# 初始化环境
init_environment() {
    mkdir -p "$LOG_DIR"
    log_info "Docker镜像优化脚本启动"
    log_info "日志文件: $LOG_FILE"
    
    # 启用BuildKit
    if [[ "$ENABLE_BUILDKIT" == "true" ]]; then
        export DOCKER_BUILDKIT=1
        log_info "已启用Docker BuildKit"
    fi
}

# 分析现有镜像
analyze_existing_images() {
    print_title "分析现有镜像"
    
    log_info "扫描项目中的Dockerfile..."
    
    local dockerfiles=$(find "$PROJECT_ROOT" -name "Dockerfile*" -type f)
    local total_size=0
    local image_count=0
    
    echo "📋 发现的Dockerfile文件:" | tee -a "$LOG_FILE"
    
    for dockerfile in $dockerfiles; do
        local relative_path=${dockerfile#$PROJECT_ROOT/}
        echo "  📄 $relative_path" | tee -a "$LOG_FILE"
        
        # 分析Dockerfile内容
        analyze_dockerfile "$dockerfile"
        
        ((image_count++))
    done
    
    log_info "共发现 $image_count 个Dockerfile文件"
    
    # 分析现有镜像大小
    log_info "分析现有镜像大小..."
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(paas-|^REPOSITORY)" | tee -a "$LOG_FILE"
}

# 分析单个Dockerfile
analyze_dockerfile() {
    local dockerfile="$1"
    local issues=()
    
    log_info "分析 $dockerfile"
    
    # 检查基础镜像
    local base_image=$(grep -E "^FROM" "$dockerfile" | head -1 | awk '{print $2}')
    if [[ "$base_image" != *"alpine"* ]] && [[ "$base_image" != "scratch" ]]; then
        issues+=("建议使用Alpine或scratch基础镜像以减小体积")
    fi
    
    # 检查是否使用多阶段构建
    local from_count=$(grep -c "^FROM" "$dockerfile" || echo "0")
    if [[ $from_count -eq 1 ]]; then
        issues+=("建议使用多阶段构建优化镜像大小")
    fi
    
    # 检查RUN指令合并
    local run_count=$(grep -c "^RUN" "$dockerfile" || echo "0")
    if [[ $run_count -gt 3 ]]; then
        issues+=("建议合并RUN指令以减少镜像层数")
    fi
    
    # 检查缓存清理
    if ! grep -q "rm -rf /var/cache\|rm -rf /tmp\|apt-get clean" "$dockerfile"; then
        issues+=("建议添加缓存清理命令")
    fi
    
    # 输出分析结果
    if [[ ${#issues[@]} -gt 0 ]]; then
        log_warning "发现优化建议:"
        for issue in "${issues[@]}"; do
            echo "    ⚠️  $issue" | tee -a "$LOG_FILE"
        done
    else
        log_success "Dockerfile已经过良好优化"
    fi
}

# 创建优化的Go服务Dockerfile模板
create_optimized_go_dockerfile() {
    local service_name="$1"
    local dockerfile_path="$2"
    
    log_info "创建优化的Go服务Dockerfile: $service_name"
    
    cat > "$dockerfile_path" << 'EOF'
# 优化的Go服务多阶段构建Dockerfile
# 使用最新的构建优化技术减小镜像体积

# 构建阶段
FROM golang:1.21-alpine AS builder

# 安装必要的构建工具
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    upx

# 设置工作目录
WORKDIR /app

# 设置Go环境变量优化构建
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct

# 复制go.mod和go.sum文件（利用Docker缓存层）
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用（启用所有优化选项）
RUN go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -trimpath \
    -o main ./cmd/SERVICE_NAME/main.go

# 使用UPX压缩二进制文件（可选，会增加启动时间但减小体积）
RUN upx --best --lzma main || true

# 最终运行阶段 - 使用scratch最小化镜像
FROM scratch AS runtime

# 从builder阶段复制必要文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /app/main /main

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户（在scratch中需要手动创建）
# 注意：scratch镜像中无法创建用户，如需要用户可以使用alpine:latest

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/main", "health"] || exit 1

# 运行应用
ENTRYPOINT ["/main"]
EOF
    
    # 替换服务名称
    sed -i "s/SERVICE_NAME/$service_name/g" "$dockerfile_path"
    
    log_success "优化的Dockerfile已创建: $dockerfile_path"
}

# 创建优化的前端Dockerfile模板
create_optimized_frontend_dockerfile() {
    local dockerfile_path="$1"
    
    log_info "创建优化的前端Dockerfile"
    
    cat > "$dockerfile_path" << 'EOF'
# 优化的前端应用多阶段构建Dockerfile
# 使用最新的构建优化技术和Nginx配置

# 构建阶段
FROM node:18-alpine AS builder

# 安装必要工具
RUN apk add --no-cache git python3 make g++

# 设置工作目录
WORKDIR /app

# 设置npm配置优化
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set cache /tmp/.npm && \
    npm config set prefer-offline true

# 复制package文件（利用Docker缓存层）
COPY package*.json ./

# 安装依赖（仅生产依赖）
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build && \
    # 清理不必要的文件
    rm -rf node_modules src public *.json *.js *.ts *.md

# 生产阶段 - 使用优化的Nginx镜像
FROM nginx:alpine AS runtime

# 安装必要工具
RUN apk add --no-cache curl && \
    # 清理apk缓存
    rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制优化的nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 设置正确的权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    # 创建nginx运行所需的目录
    mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
EOF
    
    log_success "优化的前端Dockerfile已创建: $dockerfile_path"
}

# 创建优化的nginx配置
create_optimized_nginx_config() {
    local config_path="$1"
    
    log_info "创建优化的nginx配置"
    
    cat > "$config_path" << 'EOF'
# 优化的Nginx配置文件
# 针对前端应用进行性能和安全优化

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx/nginx.pid;

# 优化worker连接数
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 隐藏nginx版本
    server_tokens off;
    
    server {
        listen 8080;
        server_name _;
        root /usr/share/nginx/html;
        index index.html index.htm;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
        
        # SPA路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API代理（如果需要）
        location /api/ {
            proxy_pass http://api-gateway:8080/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
EOF
    
    log_success "优化的nginx配置已创建: $config_path"
}

# 创建.dockerignore文件
create_dockerignore() {
    local service_dir="$1"
    local dockerignore_path="$service_dir/.dockerignore"
    
    log_info "创建.dockerignore文件: $dockerignore_path"
    
    cat > "$dockerignore_path" << 'EOF'
# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
CHANGELOG.md
LICENSE
docs/
*.md

# 开发环境文件
.env
.env.local
.env.development
.env.test
.env.production

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 构建产物
bin/
build/
dist/
target/
out/

# 依赖目录
node_modules/
vendor/

# 测试文件
tests/
test/
coverage/
*.test
*.test.js
*.spec.js

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
*.tmp

# Docker文件
Dockerfile*
docker-compose*
.dockerignore

# CI/CD文件
.github/
.gitlab-ci.yml
Jenkinsfile
.travis.yml

# 配置文件
configs/
*.yaml
*.yml
*.json
!package.json
!package-lock.json

# 证书和密钥
*.pem
*.key
*.crt
*.p12

# 备份文件
*.bak
*.backup
*.old
EOF
    
    log_success ".dockerignore文件已创建: $dockerignore_path"
}

# 优化现有Dockerfile
optimize_existing_dockerfiles() {
    print_title "优化现有Dockerfile"
    
    local services=("api-gateway" "user-service" "app-manager" "ci-cd-service" "monitoring-service" "notification-service" "load-balancer")
    
    for service in "${services[@]}"; do
        local service_dir="$PROJECT_ROOT/services/$service"
        local dockerfile="$service_dir/Dockerfile"
        
        if [[ -d "$service_dir" ]]; then
            log_info "优化服务: $service"
            
            # 备份原Dockerfile
            if [[ -f "$dockerfile" ]]; then
                cp "$dockerfile" "$dockerfile.backup.$(date +%Y%m%d-%H%M%S)"
                log_info "已备份原Dockerfile"
            fi
            
            # 创建优化的Dockerfile
            create_optimized_go_dockerfile "$service" "$dockerfile"
            
            # 创建.dockerignore
            create_dockerignore "$service_dir"
            
            log_success "服务 $service 优化完成"
        else
            log_warning "服务目录不存在: $service_dir"
        fi
    done
    
    # 优化前端Dockerfile
    local web_dir="$PROJECT_ROOT/web"
    if [[ -d "$web_dir" ]]; then
        log_info "优化前端应用"
        
        local frontend_dockerfile="$web_dir/Dockerfile"
        if [[ -f "$frontend_dockerfile" ]]; then
            cp "$frontend_dockerfile" "$frontend_dockerfile.backup.$(date +%Y%m%d-%H%M%S)"
        fi
        
        create_optimized_frontend_dockerfile "$frontend_dockerfile"
        create_optimized_nginx_config "$web_dir/nginx.conf"
        create_dockerignore "$web_dir"
        
        log_success "前端应用优化完成"
    fi
}

# 创建镜像构建优化脚本
create_build_optimization_script() {
    print_title "创建镜像构建优化脚本"
    
    local build_script="$SCRIPT_DIR/build-optimized-images.sh"
    
    cat > "$build_script" << 'EOF'
#!/bin/bash

# 优化的镜像构建脚本
# 使用最新的Docker构建技术和优化选项

set -euo pipefail

# 启用BuildKit
export DOCKER_BUILDKIT=1

# 配置变量
PROJECT_ROOT="$(dirname "$(dirname "$0")")"
REGISTRY="${REGISTRY:-localhost:5000}"
NAMESPACE="${NAMESPACE:-paas}"
VERSION="${VERSION:-latest}"
BUILD_ARGS="--no-cache --pull"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 构建单个服务镜像
build_service() {
    local service=$1
    local service_dir="$PROJECT_ROOT/services/$service"
    
    if [[ -d "$service_dir" ]]; then
        log_info "构建优化镜像: $service"
        
        # 记录构建前镜像大小
        local old_size=$(docker images "$REGISTRY/$NAMESPACE/$service:latest" --format "{{.Size}}" 2>/dev/null || echo "N/A")
        
        # 构建镜像
        docker build \
            --build-arg VERSION="$VERSION" \
            --build-arg BUILD_TIME="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            --build-arg GIT_COMMIT="$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
            $BUILD_ARGS \
            -t "$REGISTRY/$NAMESPACE/$service:$VERSION" \
            -t "$REGISTRY/$NAMESPACE/$service:latest" \
            "$service_dir"
        
        # 记录构建后镜像大小
        local new_size=$(docker images "$REGISTRY/$NAMESPACE/$service:latest" --format "{{.Size}}")
        
        log_success "$service 镜像构建完成 (大小: $old_size -> $new_size)"
        
        # 分析镜像层
        echo "镜像层分析:"
        docker history "$REGISTRY/$NAMESPACE/$service:latest" --no-trunc
        echo ""
        
    else
        log_error "服务目录不存在: $service_dir"
        return 1
    fi
}

# 构建前端镜像
build_frontend() {
    local web_dir="$PROJECT_ROOT/web"
    
    if [[ -d "$web_dir" ]]; then
        log_info "构建优化前端镜像"
        
        local old_size=$(docker images "$REGISTRY/$NAMESPACE/web:latest" --format "{{.Size}}" 2>/dev/null || echo "N/A")
        
        docker build \
            --build-arg VERSION="$VERSION" \
            --build-arg BUILD_TIME="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            --build-arg GIT_COMMIT="$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
            $BUILD_ARGS \
            -t "$REGISTRY/$NAMESPACE/web:$VERSION" \
            -t "$REGISTRY/$NAMESPACE/web:latest" \
            "$web_dir"
        
        local new_size=$(docker images "$REGISTRY/$NAMESPACE/web:latest" --format "{{.Size}}")
        
        log_success "前端镜像构建完成 (大小: $old_size -> $new_size)"
    fi
}

# 并行构建所有镜像
build_all() {
    log_info "开始并行构建所有优化镜像..."
    
    local services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "ci-cd-service"
        "monitoring-service"
        "notification-service"
        "load-balancer"
    )
    
    # 并行构建服务
    for service in "${services[@]}"; do
        build_service "$service" &
    done
    
    # 构建前端
    build_frontend &
    
    # 等待所有构建完成
    wait
    
    log_success "所有镜像构建完成"
    
    # 显示镜像大小统计
    echo ""
    log_info "镜像大小统计:"
    docker images "$REGISTRY/$NAMESPACE/*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# 镜像安全扫描
scan_images() {
    log_info "执行镜像安全扫描..."
    
    local services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "ci-cd-service"
        "monitoring-service"
        "notification-service"
        "load-balancer"
        "web"
    )
    
    for service in "${services[@]}"; do
        local image="$REGISTRY/$NAMESPACE/$service:latest"
        
        if docker images "$image" --format "{{.Repository}}" | grep -q "$REGISTRY/$NAMESPACE/$service"; then
            log_info "扫描镜像: $service"
            
            # 使用docker scan（如果可用）
            if command -v docker &> /dev/null && docker scan --help &> /dev/null; then
                docker scan "$image" || log_error "镜像扫描失败: $service"
            else
                log_info "Docker scan不可用，跳过安全扫描"
            fi
        fi
    done
}

# 主函数
main() {
    case "${1:-all}" in
        "all")
            build_all
            ;;
        "scan")
            scan_images
            ;;
        *)
            build_service "$1"
            ;;
    esac
}

main "$@"
EOF
    
    chmod +x "$build_script"
    log_success "镜像构建优化脚本已创建: $build_script"
}

# 显示优化建议
show_optimization_tips() {
    print_title "Docker镜像优化建议"
    
    cat << 'EOF'
🚀 Docker镜像优化最佳实践:

1. 📦 基础镜像选择:
   ✅ 使用Alpine Linux或scratch作为基础镜像
   ✅ 选择官方维护的镜像
   ✅ 使用特定版本标签而非latest

2. 🏗️ 多阶段构建:
   ✅ 分离构建环境和运行环境
   ✅ 只复制必要的文件到最终镜像
   ✅ 使用构建缓存优化构建速度

3. 📝 Dockerfile优化:
   ✅ 合并RUN指令减少镜像层
   ✅ 按变化频率排序指令
   ✅ 使用.dockerignore排除不必要文件

4. 🗜️ 镜像压缩:
   ✅ 清理包管理器缓存
   ✅ 删除临时文件和日志
   ✅ 使用UPX压缩二进制文件（可选）

5. 🔒 安全优化:
   ✅ 使用非root用户运行
   ✅ 定期更新基础镜像
   ✅ 扫描镜像漏洞

6. 📊 监控和维护:
   ✅ 定期清理未使用的镜像
   ✅ 监控镜像大小变化
   ✅ 设置镜像保留策略

EOF
}

# 主函数
main() {
    # 解析参数
    case "${1:-optimize}" in
        "analyze")
            init_environment
            analyze_existing_images
            ;;
        "optimize")
            init_environment
            analyze_existing_images
            optimize_existing_dockerfiles
            create_build_optimization_script
            show_optimization_tips
            ;;
        "tips")
            show_optimization_tips
            ;;
        *)
            log_error "未知命令: $1"
            echo "用法: $0 [analyze|optimize|tips]"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
