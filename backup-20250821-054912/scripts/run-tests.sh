#!/bin/bash

# PaaS 平台测试运行脚本
# 运行单元测试、集成测试并生成覆盖率报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COVERAGE_DIR="$PROJECT_ROOT/coverage"
REPORTS_DIR="$PROJECT_ROOT/test-reports"

# 测试配置
COVERAGE_THRESHOLD=80
TEST_TIMEOUT="10m"
PARALLEL_TESTS=4

echo -e "${BLUE}🧪 PaaS 平台测试运行脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查测试依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.19+"
        exit 1
    fi
    
    # 检查 Node.js (前端测试)
    if ! command -v node &> /dev/null; then
        print_warning "Node.js 未安装，将跳过前端测试"
    fi
    
    # 检查测试工具
    if ! go list -m github.com/stretchr/testify &> /dev/null; then
        print_info "安装测试依赖..."
        go mod tidy
    fi
    
    print_success "依赖检查完成"
}

# 函数：创建测试目录
setup_test_directories() {
    print_info "创建测试目录..."
    
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$REPORTS_DIR"
    mkdir -p "$PROJECT_ROOT/logs/tests"
    
    # 清理旧的测试结果
    rm -f "$COVERAGE_DIR"/*.out
    rm -f "$COVERAGE_DIR"/*.html
    rm -f "$REPORTS_DIR"/*.xml
    rm -f "$REPORTS_DIR"/*.json
    
    print_success "测试目录准备完成"
}

# 函数：运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    
    cd "$PROJECT_ROOT"
    
    # 设置测试环境变量
    export ENV=test
    export LOG_LEVEL=error
    export DB_URL=":memory:"
    
    # 运行测试并生成覆盖率报告
    local test_packages=$(go list ./... | grep -v /vendor/ | grep -v /scripts/ | grep -v /docs/)
    local coverage_file="$COVERAGE_DIR/unit-coverage.out"
    
    print_info "测试包列表："
    echo "$test_packages" | sed 's/^/  - /'
    
    # 运行测试
    go test \
        -v \
        -race \
        -timeout="$TEST_TIMEOUT" \
        -parallel="$PARALLEL_TESTS" \
        -coverprofile="$coverage_file" \
        -covermode=atomic \
        -coverpkg=./... \
        $test_packages \
        2>&1 | tee "$REPORTS_DIR/unit-test.log"
    
    local test_exit_code=${PIPESTATUS[0]}
    
    if [[ $test_exit_code -eq 0 ]]; then
        print_success "单元测试通过"
    else
        print_error "单元测试失败"
        return 1
    fi
    
    # 生成覆盖率报告
    if [[ -f "$coverage_file" ]]; then
        generate_coverage_report "$coverage_file" "unit"
    fi
    
    return 0
}

# 函数：运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    
    cd "$PROJECT_ROOT"
    
    # 设置集成测试环境变量
    export ENV=integration
    export LOG_LEVEL=info
    
    # 查找集成测试
    local integration_tests=$(find . -name "*_integration_test.go" -o -name "*_e2e_test.go" | grep -v vendor)
    
    if [[ -z "$integration_tests" ]]; then
        print_warning "未找到集成测试文件"
        return 0
    fi
    
    print_info "集成测试文件："
    echo "$integration_tests" | sed 's/^/  - /'
    
    # 运行集成测试
    local coverage_file="$COVERAGE_DIR/integration-coverage.out"
    
    go test \
        -v \
        -timeout="$TEST_TIMEOUT" \
        -tags=integration \
        -coverprofile="$coverage_file" \
        -covermode=atomic \
        $(dirname $integration_tests | sort -u) \
        2>&1 | tee "$REPORTS_DIR/integration-test.log"
    
    local test_exit_code=${PIPESTATUS[0]}
    
    if [[ $test_exit_code -eq 0 ]]; then
        print_success "集成测试通过"
    else
        print_error "集成测试失败"
        return 1
    fi
    
    # 生成覆盖率报告
    if [[ -f "$coverage_file" ]]; then
        generate_coverage_report "$coverage_file" "integration"
    fi
    
    return 0
}

# 函数：运行前端测试
run_frontend_tests() {
    print_info "运行前端测试..."
    
    if [[ ! -d "$PROJECT_ROOT/web" ]]; then
        print_warning "未找到前端项目目录"
        return 0
    fi
    
    cd "$PROJECT_ROOT/web"
    
    # 检查是否有 package.json
    if [[ ! -f "package.json" ]]; then
        print_warning "未找到 package.json，跳过前端测试"
        return 0
    fi
    
    # 安装依赖
    if [[ ! -d "node_modules" ]]; then
        print_info "安装前端依赖..."
        npm install
    fi
    
    # 运行前端测试
    if npm run test:unit &> /dev/null; then
        print_info "运行前端单元测试..."
        npm run test:unit -- --coverage --reporter=json --outputFile="../$REPORTS_DIR/frontend-test.json"
        
        if [[ $? -eq 0 ]]; then
            print_success "前端测试通过"
        else
            print_error "前端测试失败"
            return 1
        fi
    else
        print_warning "未配置前端测试脚本"
    fi
    
    return 0
}

# 函数：生成覆盖率报告
generate_coverage_report() {
    local coverage_file=$1
    local test_type=$2
    
    print_info "生成 $test_type 覆盖率报告..."
    
    if [[ ! -f "$coverage_file" ]]; then
        print_warning "覆盖率文件不存在: $coverage_file"
        return 1
    fi
    
    # 生成 HTML 报告
    local html_file="$COVERAGE_DIR/${test_type}-coverage.html"
    go tool cover -html="$coverage_file" -o "$html_file"
    
    # 计算覆盖率
    local coverage_percent=$(go tool cover -func="$coverage_file" | grep total | awk '{print $3}' | sed 's/%//')
    
    print_info "$test_type 测试覆盖率: ${coverage_percent}%"
    
    # 检查覆盖率阈值
    if (( $(echo "$coverage_percent >= $COVERAGE_THRESHOLD" | bc -l) )); then
        print_success "$test_type 覆盖率达到阈值 (${coverage_percent}% >= ${COVERAGE_THRESHOLD}%)"
    else
        print_warning "$test_type 覆盖率未达到阈值 (${coverage_percent}% < ${COVERAGE_THRESHOLD}%)"
    fi
    
    # 生成 JSON 报告
    local json_file="$REPORTS_DIR/${test_type}-coverage.json"
    cat > "$json_file" << EOF
{
  "test_type": "$test_type",
  "coverage_percent": $coverage_percent,
  "threshold": $COVERAGE_THRESHOLD,
  "passed_threshold": $(if (( $(echo "$coverage_percent >= $COVERAGE_THRESHOLD" | bc -l) )); then echo "true"; else echo "false"; fi),
  "coverage_file": "$coverage_file",
  "html_report": "$html_file",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF
    
    print_success "$test_type 覆盖率报告生成完成"
    print_info "HTML 报告: $html_file"
    print_info "JSON 报告: $json_file"
}

# 函数：合并覆盖率报告
merge_coverage_reports() {
    print_info "合并覆盖率报告..."
    
    local coverage_files=()
    
    # 收集所有覆盖率文件
    for file in "$COVERAGE_DIR"/*.out; do
        if [[ -f "$file" ]]; then
            coverage_files+=("$file")
        fi
    done
    
    if [[ ${#coverage_files[@]} -eq 0 ]]; then
        print_warning "未找到覆盖率文件"
        return 1
    fi
    
    # 合并覆盖率文件
    local merged_file="$COVERAGE_DIR/merged-coverage.out"
    
    # 写入第一个文件的头部
    head -n 1 "${coverage_files[0]}" > "$merged_file"
    
    # 合并所有文件的内容（跳过头部）
    for file in "${coverage_files[@]}"; do
        tail -n +2 "$file" >> "$merged_file"
    done
    
    # 生成合并后的报告
    generate_coverage_report "$merged_file" "merged"
    
    print_success "覆盖率报告合并完成"
}

# 函数：生成测试总结报告
generate_test_summary() {
    print_info "生成测试总结报告..."
    
    local summary_file="$REPORTS_DIR/test-summary.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 收集测试结果
    local unit_test_passed=false
    local integration_test_passed=false
    local frontend_test_passed=false
    
    if [[ -f "$REPORTS_DIR/unit-test.log" ]]; then
        if grep -q "PASS" "$REPORTS_DIR/unit-test.log"; then
            unit_test_passed=true
        fi
    fi
    
    if [[ -f "$REPORTS_DIR/integration-test.log" ]]; then
        if grep -q "PASS" "$REPORTS_DIR/integration-test.log"; then
            integration_test_passed=true
        fi
    fi
    
    if [[ -f "$REPORTS_DIR/frontend-test.json" ]]; then
        frontend_test_passed=true
    fi
    
    # 收集覆盖率信息
    local unit_coverage=0
    local integration_coverage=0
    local merged_coverage=0
    
    if [[ -f "$REPORTS_DIR/unit-coverage.json" ]]; then
        unit_coverage=$(jq -r '.coverage_percent' "$REPORTS_DIR/unit-coverage.json")
    fi
    
    if [[ -f "$REPORTS_DIR/integration-coverage.json" ]]; then
        integration_coverage=$(jq -r '.coverage_percent' "$REPORTS_DIR/integration-coverage.json")
    fi
    
    if [[ -f "$REPORTS_DIR/merged-coverage.json" ]]; then
        merged_coverage=$(jq -r '.coverage_percent' "$REPORTS_DIR/merged-coverage.json")
    fi
    
    # 生成总结报告
    cat > "$summary_file" << EOF
{
  "test_summary": {
    "timestamp": "$timestamp",
    "overall_status": "$(if [[ "$unit_test_passed" == "true" ]]; then echo "passed"; else echo "failed"; fi)",
    "tests": {
      "unit_tests": {
        "passed": $unit_test_passed,
        "coverage_percent": $unit_coverage
      },
      "integration_tests": {
        "passed": $integration_test_passed,
        "coverage_percent": $integration_coverage
      },
      "frontend_tests": {
        "passed": $frontend_test_passed
      }
    },
    "coverage": {
      "merged_coverage_percent": $merged_coverage,
      "threshold": $COVERAGE_THRESHOLD,
      "passed_threshold": $(if (( $(echo "$merged_coverage >= $COVERAGE_THRESHOLD" | bc -l) )); then echo "true"; else echo "false"; fi)
    },
    "reports": {
      "coverage_html": "$COVERAGE_DIR/merged-coverage.html",
      "test_logs": "$REPORTS_DIR/"
    }
  }
}
EOF
    
    print_success "测试总结报告生成完成: $summary_file"
}

# 函数：显示测试结果
show_test_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 测试运行完成！"
    echo "=================================================="
    echo ""
    
    # 显示覆盖率信息
    if [[ -f "$REPORTS_DIR/merged-coverage.json" ]]; then
        local coverage=$(jq -r '.coverage_percent' "$REPORTS_DIR/merged-coverage.json")
        echo "📊 总体测试覆盖率: ${coverage}%"
        
        if (( $(echo "$coverage >= $COVERAGE_THRESHOLD" | bc -l) )); then
            print_success "✅ 覆盖率达到目标阈值 (${COVERAGE_THRESHOLD}%)"
        else
            print_warning "⚠️  覆盖率未达到目标阈值 (${COVERAGE_THRESHOLD}%)"
        fi
    fi
    
    echo ""
    echo "📁 测试报告位置："
    echo "  • 覆盖率报告: $COVERAGE_DIR/"
    echo "  • 测试日志:   $REPORTS_DIR/"
    echo "  • 总结报告:   $REPORTS_DIR/test-summary.json"
    echo ""
    
    if [[ -f "$COVERAGE_DIR/merged-coverage.html" ]]; then
        echo "🌐 在浏览器中查看覆盖率报告："
        echo "  file://$COVERAGE_DIR/merged-coverage.html"
        echo ""
    fi
    
    echo "🔧 下一步建议："
    echo "  1. 查看覆盖率报告，识别未测试的代码"
    echo "  2. 为覆盖率低的模块添加测试用例"
    echo "  3. 修复失败的测试用例"
    echo "  4. 集成到 CI/CD 流水线"
    echo ""
}

# 主函数
main() {
    local run_unit=true
    local run_integration=true
    local run_frontend=true
    local generate_reports=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                run_integration=false
                run_frontend=false
                shift
                ;;
            --integration-only)
                run_unit=false
                run_frontend=false
                shift
                ;;
            --frontend-only)
                run_unit=false
                run_integration=false
                shift
                ;;
            --no-reports)
                generate_reports=false
                shift
                ;;
            --coverage-threshold)
                COVERAGE_THRESHOLD="$2"
                shift 2
                ;;
            --timeout)
                TEST_TIMEOUT="$2"
                shift 2
                ;;
            --parallel)
                PARALLEL_TESTS="$2"
                shift 2
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --unit-only              只运行单元测试"
                echo "  --integration-only       只运行集成测试"
                echo "  --frontend-only          只运行前端测试"
                echo "  --no-reports             不生成报告"
                echo "  --coverage-threshold N   设置覆盖率阈值 (默认: 80)"
                echo "  --timeout DURATION       设置测试超时 (默认: 10m)"
                echo "  --parallel N             设置并行测试数 (默认: 4)"
                echo "  -h, --help               显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行测试流程
    check_dependencies
    setup_test_directories
    
    local overall_success=true
    
    # 运行测试
    if [[ "$run_unit" == "true" ]]; then
        if ! run_unit_tests; then
            overall_success=false
        fi
    fi
    
    if [[ "$run_integration" == "true" ]]; then
        if ! run_integration_tests; then
            overall_success=false
        fi
    fi
    
    if [[ "$run_frontend" == "true" ]]; then
        if ! run_frontend_tests; then
            overall_success=false
        fi
    fi
    
    # 生成报告
    if [[ "$generate_reports" == "true" ]]; then
        merge_coverage_reports
        generate_test_summary
    fi
    
    # 显示结果
    show_test_results
    
    # 返回结果
    if [[ "$overall_success" == "true" ]]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "部分测试失败！"
        exit 1
    fi
}

# 脚本入口
main "$@"
