#!/bin/bash

# PaaS 平台安全扫描脚本
# 识别和修复系统中的安全漏洞

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$PROJECT_ROOT/security-reports"

echo -e "${BLUE}🔒 PaaS 平台安全扫描脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查安全工具
check_security_tools() {
    print_info "检查安全扫描工具..."
    
    local missing_tools=()
    
    # 检查基础工具
    for tool in curl jq openssl; do
        if ! command -v $tool &> /dev/null; then
            missing_tools+=($tool)
        fi
    done
    
    # 检查 Go 安全工具
    if ! command -v gosec &> /dev/null; then
        print_info "安装 gosec..."
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
    fi
    
    # 检查 nancy (依赖漏洞扫描)
    if ! command -v nancy &> /dev/null; then
        print_info "安装 nancy..."
        go install github.com/sonatypecommunity/nancy@latest
    fi
    
    # 检查 govulncheck
    if ! command -v govulncheck &> /dev/null; then
        print_info "安装 govulncheck..."
        go install golang.org/x/vuln/cmd/govulncheck@latest
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    print_success "安全扫描工具检查完成"
}

# 函数：创建报告目录
setup_reports_directory() {
    print_info "创建安全报告目录..."
    
    mkdir -p "$REPORTS_DIR"
    rm -f "$REPORTS_DIR"/*.txt
    rm -f "$REPORTS_DIR"/*.json
    rm -f "$REPORTS_DIR"/*.html
    
    print_success "安全报告目录准备完成"
}

# 函数：扫描代码安全漏洞
scan_code_vulnerabilities() {
    print_info "扫描代码安全漏洞..."
    
    cd "$PROJECT_ROOT"
    
    local code_scan_report="$REPORTS_DIR/code-vulnerabilities.txt"
    
    echo "# 代码安全漏洞扫描报告" > "$code_scan_report"
    echo "生成时间: $(date)" >> "$code_scan_report"
    echo "" >> "$code_scan_report"
    
    # 使用 gosec 扫描 Go 代码
    print_info "运行 gosec 代码安全扫描..."
    echo "## Gosec 安全扫描结果" >> "$code_scan_report"
    
    if gosec -fmt=text ./... >> "$code_scan_report" 2>&1; then
        print_success "Gosec 扫描完成"
    else
        print_warning "Gosec 扫描发现安全问题"
    fi
    
    echo "" >> "$code_scan_report"
    
    # 使用 govulncheck 扫描已知漏洞
    print_info "运行 govulncheck 漏洞扫描..."
    echo "## Govulncheck 漏洞扫描结果" >> "$code_scan_report"
    
    if govulncheck ./... >> "$code_scan_report" 2>&1; then
        print_success "Govulncheck 扫描完成"
    else
        print_warning "Govulncheck 发现已知漏洞"
    fi
    
    print_info "报告已保存: $code_scan_report"
}

# 函数：扫描依赖漏洞
scan_dependency_vulnerabilities() {
    print_info "扫描依赖漏洞..."
    
    cd "$PROJECT_ROOT"
    
    local dep_scan_report="$REPORTS_DIR/dependency-vulnerabilities.txt"
    
    echo "# 依赖漏洞扫描报告" > "$dep_scan_report"
    echo "生成时间: $(date)" >> "$dep_scan_report"
    echo "" >> "$dep_scan_report"
    
    # 生成依赖列表
    print_info "生成依赖列表..."
    echo "## Go 模块依赖" >> "$dep_scan_report"
    go list -m all >> "$dep_scan_report" 2>&1
    echo "" >> "$dep_scan_report"
    
    # 使用 nancy 扫描依赖漏洞
    print_info "运行 nancy 依赖漏洞扫描..."
    echo "## Nancy 依赖漏洞扫描结果" >> "$dep_scan_report"
    
    if go list -json -deps ./... | nancy sleuth >> "$dep_scan_report" 2>&1; then
        print_success "Nancy 扫描完成"
    else
        print_warning "Nancy 发现依赖漏洞"
    fi
    
    echo "" >> "$dep_scan_report"
    
    # 检查过时的依赖
    print_info "检查过时的依赖..."
    echo "## 过时依赖检查" >> "$dep_scan_report"
    
    go list -u -m all | grep '\[' >> "$dep_scan_report" 2>/dev/null || echo "未发现过时依赖" >> "$dep_scan_report"
    
    print_info "报告已保存: $dep_scan_report"
}

# 函数：扫描配置安全
scan_configuration_security() {
    print_info "扫描配置安全..."
    
    local config_scan_report="$REPORTS_DIR/configuration-security.txt"
    
    echo "# 配置安全扫描报告" > "$config_scan_report"
    echo "生成时间: $(date)" >> "$config_scan_report"
    echo "" >> "$config_scan_report"
    
    # 检查敏感文件权限
    print_info "检查敏感文件权限..."
    echo "## 敏感文件权限检查" >> "$config_scan_report"
    
    local sensitive_files=(
        "configs/config.yaml"
        "configs/database.yaml"
        "configs/jwt-secret.txt"
        ".env"
        "docker-compose.yml"
    )
    
    for file in "${sensitive_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            local perms=$(stat -c "%a" "$PROJECT_ROOT/$file" 2>/dev/null || stat -f "%A" "$PROJECT_ROOT/$file" 2>/dev/null)
            echo "文件: $file, 权限: $perms" >> "$config_scan_report"
            
            # 检查是否权限过于宽松
            if [[ "$perms" =~ [2367] ]]; then
                echo "  ⚠️  警告: 文件权限过于宽松" >> "$config_scan_report"
            fi
        fi
    done
    
    echo "" >> "$config_scan_report"
    
    # 检查硬编码密钥
    print_info "检查硬编码密钥..."
    echo "## 硬编码密钥检查" >> "$config_scan_report"
    
    local secret_patterns=(
        "password.*=.*['\"][^'\"]{8,}['\"]"
        "secret.*=.*['\"][^'\"]{16,}['\"]"
        "key.*=.*['\"][^'\"]{16,}['\"]"
        "token.*=.*['\"][^'\"]{20,}['\"]"
    )
    
    for pattern in "${secret_patterns[@]}"; do
        echo "搜索模式: $pattern" >> "$config_scan_report"
        grep -r -i -E "$pattern" --include="*.go" --include="*.yaml" --include="*.yml" . >> "$config_scan_report" 2>/dev/null || echo "  未发现匹配" >> "$config_scan_report"
    done
    
    echo "" >> "$config_scan_report"
    
    # 检查 Docker 安全配置
    if [[ -f "$PROJECT_ROOT/Dockerfile" ]]; then
        print_info "检查 Docker 安全配置..."
        echo "## Docker 安全配置检查" >> "$config_scan_report"
        
        # 检查是否使用 root 用户
        if grep -q "USER root" "$PROJECT_ROOT/Dockerfile"; then
            echo "⚠️  警告: Dockerfile 使用 root 用户" >> "$config_scan_report"
        fi
        
        # 检查是否暴露敏感端口
        if grep -q "EXPOSE.*22\|EXPOSE.*3306\|EXPOSE.*5432" "$PROJECT_ROOT/Dockerfile"; then
            echo "⚠️  警告: Dockerfile 暴露敏感端口" >> "$config_scan_report"
        fi
        
        # 检查是否有安全更新
        if ! grep -q "apt.*update.*upgrade\|apk.*update.*upgrade" "$PROJECT_ROOT/Dockerfile"; then
            echo "⚠️  建议: 在 Dockerfile 中添加安全更新" >> "$config_scan_report"
        fi
    fi
    
    print_info "报告已保存: $config_scan_report"
}

# 函数：扫描网络安全
scan_network_security() {
    print_info "扫描网络安全..."
    
    local network_scan_report="$REPORTS_DIR/network-security.txt"
    
    echo "# 网络安全扫描报告" > "$network_scan_report"
    echo "生成时间: $(date)" >> "$network_scan_report"
    echo "" >> "$network_scan_report"
    
    # 检查开放端口
    print_info "检查开放端口..."
    echo "## 开放端口检查" >> "$network_scan_report"
    
    if command -v netstat &> /dev/null; then
        netstat -tuln | grep LISTEN >> "$network_scan_report"
    elif command -v ss &> /dev/null; then
        ss -tuln | grep LISTEN >> "$network_scan_report"
    else
        echo "无法检查开放端口 (netstat/ss 不可用)" >> "$network_scan_report"
    fi
    
    echo "" >> "$network_scan_report"
    
    # 检查 SSL/TLS 配置
    print_info "检查 SSL/TLS 配置..."
    echo "## SSL/TLS 配置检查" >> "$network_scan_report"
    
    local ssl_endpoints=(
        "localhost:8080"
        "localhost:8443"
    )
    
    for endpoint in "${ssl_endpoints[@]}"; do
        echo "检查端点: $endpoint" >> "$network_scan_report"
        
        if timeout 5 openssl s_client -connect "$endpoint" -servername localhost < /dev/null >> "$network_scan_report" 2>&1; then
            echo "  SSL/TLS 连接成功" >> "$network_scan_report"
        else
            echo "  SSL/TLS 连接失败或未配置" >> "$network_scan_report"
        fi
    done
    
    print_info "报告已保存: $network_scan_report"
}

# 函数：扫描数据库安全
scan_database_security() {
    print_info "扫描数据库安全..."
    
    local db_scan_report="$REPORTS_DIR/database-security.txt"
    
    echo "# 数据库安全扫描报告" > "$db_scan_report"
    echo "生成时间: $(date)" >> "$db_scan_report"
    echo "" >> "$db_scan_report"
    
    # 检查数据库连接配置
    echo "## 数据库连接配置检查" >> "$db_scan_report"
    
    local db_config_files=(
        "configs/database.yaml"
        "configs/config.yaml"
        ".env"
    )
    
    for config_file in "${db_config_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$config_file" ]]; then
            echo "检查配置文件: $config_file" >> "$db_scan_report"
            
            # 检查是否使用默认密码
            if grep -i "password.*admin\|password.*root\|password.*123" "$PROJECT_ROOT/$config_file" > /dev/null; then
                echo "  ⚠️  警告: 发现默认或弱密码" >> "$db_scan_report"
            fi
            
            # 检查是否启用 SSL
            if ! grep -i "ssl.*true\|sslmode.*require" "$PROJECT_ROOT/$config_file" > /dev/null; then
                echo "  ⚠️  建议: 启用数据库 SSL 连接" >> "$db_scan_report"
            fi
        fi
    done
    
    echo "" >> "$db_scan_report"
    
    # 检查数据库迁移文件
    if [[ -d "$PROJECT_ROOT/migrations" ]]; then
        echo "## 数据库迁移安全检查" >> "$db_scan_report"
        
        # 检查是否有危险的 SQL 操作
        local dangerous_patterns=(
            "DROP.*DATABASE"
            "TRUNCATE.*TABLE"
            "DELETE.*FROM.*WHERE.*1.*=.*1"
            "GRANT.*ALL"
        )
        
        for pattern in "${dangerous_patterns[@]}"; do
            if grep -r -i "$pattern" "$PROJECT_ROOT/migrations/" > /dev/null 2>&1; then
                echo "  ⚠️  警告: 发现潜在危险的 SQL 操作: $pattern" >> "$db_scan_report"
            fi
        done
    fi
    
    print_info "报告已保存: $db_scan_report"
}

# 函数：生成安全修复建议
generate_security_recommendations() {
    print_info "生成安全修复建议..."
    
    local recommendations_file="$REPORTS_DIR/security-recommendations.md"
    
    cat > "$recommendations_file" << 'EOF'
# 安全修复建议报告

## 🔒 安全修复优先级

### 🔴 高优先级（立即修复）
1. **已知漏洞修复** - 修复 govulncheck 发现的已知漏洞
2. **硬编码密钥清理** - 移除代码中的硬编码密钥和密码
3. **权限配置修复** - 修复过于宽松的文件权限
4. **SQL 注入防护** - 实施参数化查询和输入验证

### 🟡 中优先级（近期修复）
1. **依赖更新** - 更新过时的依赖库
2. **SSL/TLS 配置** - 配置 HTTPS 和数据库 SSL 连接
3. **安全头配置** - 添加 HTTP 安全头
4. **访问控制** - 实施细粒度的访问控制

### 🟢 低优先级（有时间时修复）
1. **安全监控** - 实施安全事件监控
2. **安全培训** - 开发团队安全培训
3. **渗透测试** - 定期进行渗透测试
4. **安全文档** - 完善安全操作文档

## 🛠️ 具体修复措施

### 代码安全
```bash
# 1. 修复 gosec 发现的问题
gosec -fmt=json -out=gosec-report.json ./...

# 2. 更新有漏洞的依赖
go get -u ./...
go mod tidy

# 3. 使用安全的加密函数
# 避免使用 MD5, SHA1，使用 SHA256 或更强的算法
```

### 配置安全
```bash
# 1. 设置正确的文件权限
chmod 600 configs/config.yaml
chmod 600 .env

# 2. 使用环境变量存储敏感信息
export JWT_SECRET=$(openssl rand -hex 32)
export DB_PASSWORD=$(openssl rand -base64 32)

# 3. 启用 SSL/TLS
# 在配置文件中启用 HTTPS 和数据库 SSL
```

### 数据库安全
```sql
-- 1. 创建专用数据库用户
CREATE USER 'paas_app'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON paas_db.* TO 'paas_app'@'localhost';

-- 2. 启用 SSL 连接
-- 在数据库配置中设置 sslmode=require

-- 3. 定期备份和加密
-- 实施定期数据库备份和加密存储
```

### 网络安全
```bash
# 1. 配置防火墙
ufw enable
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# 2. 使用反向代理
# 配置 Nginx 或 Apache 作为反向代理

# 3. 实施 DDoS 防护
# 使用 Cloudflare 或类似服务
```

## 📊 安全检查清单

### 认证和授权
- [ ] 实施强密码策略
- [ ] 启用多因素认证
- [ ] 使用安全的会话管理
- [ ] 实施最小权限原则

### 数据保护
- [ ] 加密敏感数据
- [ ] 实施数据备份
- [ ] 配置数据保留策略
- [ ] 启用审计日志

### 网络安全
- [ ] 配置 HTTPS
- [ ] 实施网络分段
- [ ] 配置防火墙规则
- [ ] 启用入侵检测

### 应用安全
- [ ] 输入验证和过滤
- [ ] 输出编码
- [ ] 错误处理
- [ ] 安全日志记录

## 🔧 自动化安全工具

### 持续安全扫描
```bash
# 1. 集成到 CI/CD 流水线
# 在构建过程中运行安全扫描

# 2. 定期依赖扫描
# 使用 Dependabot 或类似工具

# 3. 代码安全扫描
# 集成 SonarQube 或 CodeQL
```

### 运行时安全监控
```bash
# 1. 部署 SIEM 系统
# 收集和分析安全日志

# 2. 实施异常检测
# 监控异常的用户行为

# 3. 自动化响应
# 配置自动化安全响应
```

## ⚠️ 注意事项

1. **渐进式修复**: 按优先级逐步修复，避免一次性大规模修改
2. **测试验证**: 每次安全修复后进行充分测试
3. **文档更新**: 及时更新安全配置文档
4. **团队培训**: 确保团队了解安全最佳实践
5. **定期评估**: 定期进行安全评估和渗透测试

## 📞 安全事件响应

### 发现安全事件时的步骤
1. **立即隔离** - 隔离受影响的系统
2. **评估影响** - 评估安全事件的影响范围
3. **收集证据** - 保存相关日志和证据
4. **通知相关方** - 通知管理层和相关团队
5. **修复漏洞** - 修复导致事件的安全漏洞
6. **恢复服务** - 安全地恢复服务
7. **事后分析** - 分析事件原因并改进流程

EOF
    
    print_success "安全修复建议已生成: $recommendations_file"
}

# 函数：生成安全报告
generate_security_report() {
    print_info "生成安全报告..."
    
    local report_file="$REPORTS_DIR/security-summary.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 统计漏洞数量
    local code_vulns=$(grep -c "Severity:" "$REPORTS_DIR/code-vulnerabilities.txt" 2>/dev/null || echo "0")
    local dep_vulns=$(grep -c "vulnerability" "$REPORTS_DIR/dependency-vulnerabilities.txt" 2>/dev/null || echo "0")
    local config_issues=$(grep -c "⚠️" "$REPORTS_DIR/configuration-security.txt" 2>/dev/null || echo "0")
    
    # 生成 JSON 报告
    cat > "$report_file" << EOF
{
  "security_scan_summary": {
    "timestamp": "$timestamp",
    "scan_results": {
      "code_vulnerabilities": $code_vulns,
      "dependency_vulnerabilities": $dep_vulns,
      "configuration_issues": $config_issues,
      "total_issues": $((code_vulns + dep_vulns + config_issues))
    },
    "scan_reports": {
      "code_vulnerabilities": "$REPORTS_DIR/code-vulnerabilities.txt",
      "dependency_vulnerabilities": "$REPORTS_DIR/dependency-vulnerabilities.txt",
      "configuration_security": "$REPORTS_DIR/configuration-security.txt",
      "network_security": "$REPORTS_DIR/network-security.txt",
      "database_security": "$REPORTS_DIR/database-security.txt",
      "security_recommendations": "$REPORTS_DIR/security-recommendations.md"
    },
    "security_score": $(( 100 - (code_vulns * 10) - (dep_vulns * 5) - (config_issues * 3) )),
    "recommendations": [
      "修复发现的代码安全漏洞",
      "更新有漏洞的依赖库",
      "修复配置安全问题",
      "实施安全监控和告警",
      "定期进行安全扫描"
    ]
  }
}
EOF
    
    print_success "安全报告已生成: $report_file"
}

# 函数：显示扫描结果
show_scan_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 安全扫描完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 扫描报告："
    echo "  • 代码漏洞:     $REPORTS_DIR/code-vulnerabilities.txt"
    echo "  • 依赖漏洞:     $REPORTS_DIR/dependency-vulnerabilities.txt"
    echo "  • 配置安全:     $REPORTS_DIR/configuration-security.txt"
    echo "  • 网络安全:     $REPORTS_DIR/network-security.txt"
    echo "  • 数据库安全:   $REPORTS_DIR/database-security.txt"
    echo "  • 修复建议:     $REPORTS_DIR/security-recommendations.md"
    echo "  • 总结报告:     $REPORTS_DIR/security-summary.json"
    echo ""
    
    # 显示安全分数
    if [[ -f "$REPORTS_DIR/security-summary.json" ]]; then
        local security_score=$(jq -r '.security_scan_summary.security_score' "$REPORTS_DIR/security-summary.json")
        local total_issues=$(jq -r '.security_scan_summary.scan_results.total_issues' "$REPORTS_DIR/security-summary.json")
        
        echo "🔒 安全评分: $security_score/100"
        echo "🚨 发现问题: $total_issues 个"
        echo ""
        
        if [[ $security_score -ge 80 ]]; then
            print_success "安全状况良好"
        elif [[ $security_score -ge 60 ]]; then
            print_warning "安全状况一般，建议修复发现的问题"
        else
            print_error "安全状况较差，需要立即修复关键问题"
        fi
    fi
    
    echo ""
    echo "🔧 下一步操作："
    echo "  1. 查看扫描报告，了解安全问题"
    echo "  2. 按优先级修复发现的漏洞"
    echo "  3. 实施安全监控和告警"
    echo "  4. 定期进行安全扫描"
    echo ""
}

# 主函数
main() {
    local auto_fix=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto-fix)
                auto_fix=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --auto-fix       自动修复可修复的安全问题"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行安全扫描流程
    check_security_tools
    setup_reports_directory
    
    # 扫描阶段
    scan_code_vulnerabilities
    scan_dependency_vulnerabilities
    scan_configuration_security
    scan_network_security
    scan_database_security
    generate_security_recommendations
    
    # 报告阶段
    generate_security_report
    show_scan_results
    
    if [[ "$auto_fix" == "true" ]]; then
        print_info "自动修复功能开发中..."
    else
        print_info "扫描完成，查看报告并手动修复发现的问题"
    fi
}

# 脚本入口
main "$@"
