#!/bin/bash

# Docker镜像清理定时任务设置脚本
# 用于配置自动化的Docker镜像清理任务

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly CLEANUP_SCRIPT="$SCRIPT_DIR/docker-image-cleanup.sh"
readonly SYSTEMD_SERVICE_FILE="/etc/systemd/system/docker-cleanup.service"
readonly SYSTEMD_TIMER_FILE="/etc/systemd/system/docker-cleanup.timer"
readonly CRON_FILE="/etc/cron.d/docker-cleanup"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查清理脚本
    if [[ ! -f "$CLEANUP_SCRIPT" ]]; then
        log_error "清理脚本不存在: $CLEANUP_SCRIPT"
        exit 1
    fi
    
    # 确保清理脚本可执行
    chmod +x "$CLEANUP_SCRIPT"
    
    log_success "依赖检查完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    cat > "$SYSTEMD_SERVICE_FILE" << EOF
[Unit]
Description=Docker镜像清理服务
Documentation=https://docs.docker.com/
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
User=root
Group=root
ExecStart=$CLEANUP_SCRIPT --config $PROJECT_ROOT/configs/docker-cleanup.yaml
StandardOutput=journal
StandardError=journal
TimeoutStartSec=1800
RemainAfterExit=no

# 环境变量
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PROJECT_ROOT=$PROJECT_ROOT

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$PROJECT_ROOT/logs $PROJECT_ROOT/data

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "systemd服务文件已创建: $SYSTEMD_SERVICE_FILE"
}

# 创建systemd定时器
create_systemd_timer() {
    log_info "创建systemd定时器..."
    
    cat > "$SYSTEMD_TIMER_FILE" << EOF
[Unit]
Description=Docker镜像清理定时器
Documentation=https://docs.docker.com/
Requires=docker-cleanup.service

[Timer]
# 每天凌晨2点执行
OnCalendar=*-*-* 02:00:00
# 如果错过了执行时间，立即执行
Persistent=true
# 随机延迟0-30分钟，避免系统负载峰值
RandomizedDelaySec=1800

[Install]
WantedBy=timers.target
EOF
    
    log_success "systemd定时器文件已创建: $SYSTEMD_TIMER_FILE"
}

# 创建cron任务
create_cron_job() {
    log_info "创建cron任务..."
    
    cat > "$CRON_FILE" << EOF
# Docker镜像清理定时任务
# 每天凌晨2点执行清理
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
PROJECT_ROOT=$PROJECT_ROOT

# 主要清理任务 - 每天凌晨2点
0 2 * * * root $CLEANUP_SCRIPT --config $PROJECT_ROOT/configs/docker-cleanup.yaml dangling unused old >> $PROJECT_ROOT/logs/docker-cleanup-cron.log 2>&1

# 快速清理任务 - 每6小时清理悬空镜像
0 */6 * * * root $CLEANUP_SCRIPT --config $PROJECT_ROOT/configs/docker-cleanup.yaml dangling --force >> $PROJECT_ROOT/logs/docker-cleanup-quick.log 2>&1

# 周末深度清理 - 每周日凌晨3点
0 3 * * 0 root $CLEANUP_SCRIPT --config $PROJECT_ROOT/configs/docker-cleanup.yaml all --force >> $PROJECT_ROOT/logs/docker-cleanup-weekly.log 2>&1

# 磁盘空间检查 - 每小时检查一次
0 * * * * root $SCRIPT_DIR/check-docker-disk-usage.sh >> $PROJECT_ROOT/logs/disk-usage-check.log 2>&1
EOF
    
    log_success "cron任务文件已创建: $CRON_FILE"
}

# 启用systemd服务
enable_systemd_service() {
    log_info "启用systemd服务..."
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用定时器
    systemctl enable docker-cleanup.timer
    systemctl start docker-cleanup.timer
    
    # 检查状态
    if systemctl is-active --quiet docker-cleanup.timer; then
        log_success "systemd定时器已启用并运行"
    else
        log_error "systemd定时器启用失败"
        return 1
    fi
    
    # 显示下次执行时间
    log_info "下次执行时间:"
    systemctl list-timers docker-cleanup.timer --no-pager
}

# 启用cron服务
enable_cron_service() {
    log_info "启用cron服务..."
    
    # 重新加载cron配置
    if command -v systemctl &> /dev/null; then
        systemctl reload cron || systemctl reload crond || true
    else
        service cron reload || service crond reload || true
    fi
    
    log_success "cron任务已启用"
    
    # 显示cron任务
    log_info "已配置的cron任务:"
    crontab -l 2>/dev/null | grep -E "(docker-cleanup|Docker)" || echo "  (通过 /etc/cron.d/docker-cleanup 配置)"
}

# 创建磁盘使用检查脚本
create_disk_check_script() {
    log_info "创建磁盘使用检查脚本..."
    
    local disk_check_script="$SCRIPT_DIR/check-docker-disk-usage.sh"
    
    cat > "$disk_check_script" << 'EOF'
#!/bin/bash

# Docker磁盘使用检查脚本
# 监控Docker存储使用情况并在必要时触发清理

set -euo pipefail

# 配置
DOCKER_ROOT="/var/lib/docker"
THRESHOLD=85  # 磁盘使用率阈值（百分比）
CLEANUP_SCRIPT="$(dirname "$0")/docker-image-cleanup.sh"
PROJECT_ROOT="$(dirname "$(dirname "$0")")"
LOG_FILE="$PROJECT_ROOT/logs/disk-usage-check.log"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# 获取磁盘使用率
get_disk_usage() {
    df "$DOCKER_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//'
}

# 获取Docker系统信息
get_docker_info() {
    docker system df --format "table {{.Type}}\t{{.Total}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}" 2>/dev/null || echo "Docker信息获取失败"
}

# 主函数
main() {
    local usage=$(get_disk_usage)
    
    log_message "磁盘使用率检查: ${usage}%"
    
    if [[ $usage -gt $THRESHOLD ]]; then
        log_message "磁盘使用率超过阈值 ${THRESHOLD}%，触发自动清理"
        
        # 记录Docker系统信息
        log_message "清理前Docker系统信息:"
        get_docker_info >> "$LOG_FILE"
        
        # 执行清理
        if [[ -x "$CLEANUP_SCRIPT" ]]; then
            "$CLEANUP_SCRIPT" --force dangling unused >> "$LOG_FILE" 2>&1
            log_message "自动清理完成"
            
            # 记录清理后信息
            local new_usage=$(get_disk_usage)
            log_message "清理后磁盘使用率: ${new_usage}%"
            
            # 发送通知（如果配置了）
            if command -v curl &> /dev/null && [[ -n "${WEBHOOK_URL:-}" ]]; then
                curl -X POST "$WEBHOOK_URL" \
                    -H "Content-Type: application/json" \
                    -d "{\"text\":\"Docker磁盘清理完成: ${usage}% -> ${new_usage}%\"}" \
                    2>/dev/null || true
            fi
        else
            log_message "清理脚本不存在或不可执行: $CLEANUP_SCRIPT"
        fi
    fi
}

# 执行主函数
main "$@"
EOF
    
    chmod +x "$disk_check_script"
    log_success "磁盘使用检查脚本已创建: $disk_check_script"
}

# 测试配置
test_configuration() {
    log_info "测试配置..."
    
    # 测试清理脚本
    if "$CLEANUP_SCRIPT" --dry-run --help >/dev/null 2>&1; then
        log_success "清理脚本测试通过"
    else
        log_error "清理脚本测试失败"
        return 1
    fi
    
    # 测试systemd服务
    if systemctl is-enabled docker-cleanup.timer >/dev/null 2>&1; then
        log_success "systemd定时器配置正确"
    else
        log_warning "systemd定时器未启用"
    fi
    
    # 测试cron配置
    if [[ -f "$CRON_FILE" ]]; then
        log_success "cron任务配置正确"
    else
        log_warning "cron任务文件不存在"
    fi
}

# 显示状态
show_status() {
    echo ""
    log_info "Docker清理任务状态:"
    echo ""
    
    # systemd状态
    echo "📅 Systemd定时器状态:"
    if systemctl is-active --quiet docker-cleanup.timer 2>/dev/null; then
        echo "  ✅ 运行中"
        systemctl list-timers docker-cleanup.timer --no-pager | tail -n +2
    else
        echo "  ❌ 未运行"
    fi
    echo ""
    
    # cron状态
    echo "⏰ Cron任务状态:"
    if [[ -f "$CRON_FILE" ]]; then
        echo "  ✅ 已配置"
        echo "  📋 任务列表:"
        grep -v "^#" "$CRON_FILE" | grep -v "^$" | sed 's/^/    /'
    else
        echo "  ❌ 未配置"
    fi
    echo ""
    
    # Docker状态
    echo "🐳 Docker系统状态:"
    if docker info >/dev/null 2>&1; then
        echo "  ✅ Docker运行正常"
        docker system df 2>/dev/null | sed 's/^/    /' || echo "    无法获取系统信息"
    else
        echo "  ❌ Docker未运行"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Docker镜像清理定时任务设置脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -s, --systemd       仅设置systemd定时器
  -c, --cron          仅设置cron任务
  -t, --test          测试配置
  --status            显示状态
  --uninstall         卸载所有定时任务

示例:
  $0                  # 设置所有定时任务
  $0 --systemd        # 仅设置systemd定时器
  $0 --cron           # 仅设置cron任务
  $0 --status         # 显示状态

EOF
}

# 卸载定时任务
uninstall_tasks() {
    log_info "卸载Docker清理定时任务..."
    
    # 停止并禁用systemd定时器
    if systemctl is-active --quiet docker-cleanup.timer 2>/dev/null; then
        systemctl stop docker-cleanup.timer
        systemctl disable docker-cleanup.timer
        log_info "systemd定时器已停止并禁用"
    fi
    
    # 删除systemd文件
    [[ -f "$SYSTEMD_SERVICE_FILE" ]] && rm -f "$SYSTEMD_SERVICE_FILE"
    [[ -f "$SYSTEMD_TIMER_FILE" ]] && rm -f "$SYSTEMD_TIMER_FILE"
    
    # 删除cron文件
    [[ -f "$CRON_FILE" ]] && rm -f "$CRON_FILE"
    
    # 重新加载配置
    systemctl daemon-reload 2>/dev/null || true
    if command -v systemctl &> /dev/null; then
        systemctl reload cron || systemctl reload crond || true
    fi
    
    log_success "定时任务已卸载"
}

# 主函数
main() {
    local setup_systemd=true
    local setup_cron=true
    local test_only=false
    local show_status_only=false
    local uninstall=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--systemd)
                setup_systemd=true
                setup_cron=false
                shift
                ;;
            -c|--cron)
                setup_systemd=false
                setup_cron=true
                shift
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            --status)
                show_status_only=true
                shift
                ;;
            --uninstall)
                uninstall=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 仅显示状态
    if [[ "$show_status_only" == "true" ]]; then
        show_status
        exit 0
    fi
    
    # 卸载任务
    if [[ "$uninstall" == "true" ]]; then
        check_permissions
        uninstall_tasks
        exit 0
    fi
    
    # 仅测试
    if [[ "$test_only" == "true" ]]; then
        test_configuration
        exit 0
    fi
    
    # 检查权限和依赖
    check_permissions
    check_dependencies
    
    # 创建必要的目录
    mkdir -p "$PROJECT_ROOT/logs"
    
    # 设置定时任务
    if [[ "$setup_systemd" == "true" ]]; then
        create_systemd_service
        create_systemd_timer
        enable_systemd_service
    fi
    
    if [[ "$setup_cron" == "true" ]]; then
        create_cron_job
        enable_cron_service
    fi
    
    # 创建辅助脚本
    create_disk_check_script
    
    # 测试配置
    test_configuration
    
    # 显示状态
    show_status
    
    log_success "Docker镜像清理定时任务设置完成"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
