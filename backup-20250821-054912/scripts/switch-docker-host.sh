#!/bin/bash

# Docker Host 切换脚本
# 用于快速切换本地和远程 Docker Host 连接

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENV_FILE="${PROJECT_ROOT}/.env.development"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker Host 切换脚本

用法: $0 [选项] <连接类型>

连接类型:
  local                 使用本地 Docker (unix socket)
  tcp <主机IP> [端口]    使用 TCP 连接 (不安全)
  tls <主机IP> [端口]    使用 TLS 连接 (安全)
  ssh <用户@主机IP>      使用 SSH 连接 (推荐)

选项:
  -h, --help           显示此帮助信息
  -t, --test           测试连接但不保存配置
  -v, --verbose        显示详细信息
  -c, --cert-path      指定 TLS 证书路径 (默认: ./docker-certs)
  -k, --key-path       指定 SSH 密钥路径 (默认: ~/.ssh/id_rsa)

示例:
  $0 local                                    # 切换到本地 Docker
  $0 tcp *************                       # TCP 连接到远程主机
  $0 tls ************* 2376                  # TLS 连接到远程主机
  $0 ssh root@*************                  # SSH 连接到远程主机
  $0 -t ssh root@*************               # 仅测试 SSH 连接
  $0 -c /path/to/certs tls *************     # 指定证书路径的 TLS 连接

EOF
}

# 解析命令行参数
parse_args() {
    TEST_ONLY=false
    VERBOSE=false
    CERT_PATH="./docker-certs"
    KEY_PATH="~/.ssh/id_rsa"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                TEST_ONLY=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--cert-path)
                CERT_PATH="$2"
                shift 2
                ;;
            -k|--key-path)
                KEY_PATH="$2"
                shift 2
                ;;
            local|tcp|tls|ssh)
                CONNECTION_TYPE="$1"
                shift
                break
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 获取剩余参数
    REMAINING_ARGS=("$@")
}

# 验证参数
validate_args() {
    if [[ -z "$CONNECTION_TYPE" ]]; then
        log_error "请指定连接类型"
        show_help
        exit 1
    fi
    
    case "$CONNECTION_TYPE" in
        local)
            # 本地连接不需要额外参数
            ;;
        tcp|tls)
            if [[ ${#REMAINING_ARGS[@]} -lt 1 ]]; then
                log_error "$CONNECTION_TYPE 连接需要指定主机IP"
                exit 1
            fi
            HOST_IP="${REMAINING_ARGS[0]}"
            PORT="${REMAINING_ARGS[1]:-2376}"
            if [[ "$CONNECTION_TYPE" == "tcp" ]]; then
                PORT="${REMAINING_ARGS[1]:-2375}"
            fi
            ;;
        ssh)
            if [[ ${#REMAINING_ARGS[@]} -lt 1 ]]; then
                log_error "SSH 连接需要指定用户@主机IP"
                exit 1
            fi
            SSH_TARGET="${REMAINING_ARGS[0]}"
            ;;
        *)
            log_error "不支持的连接类型: $CONNECTION_TYPE"
            exit 1
            ;;
    esac
}

# 测试 Docker 连接
test_docker_connection() {
    local docker_host="$1"
    local tls_verify="$2"
    local cert_path="$3"
    
    log_info "测试 Docker 连接..."
    
    # 设置临时环境变量
    export DOCKER_HOST="$docker_host"
    if [[ "$tls_verify" == "1" ]]; then
        export DOCKER_TLS_VERIFY=1
        export DOCKER_CERT_PATH="$cert_path"
    else
        unset DOCKER_TLS_VERIFY
        unset DOCKER_CERT_PATH
    fi
    
    # 测试连接
    if docker version >/dev/null 2>&1; then
        log_info "✅ Docker 连接测试成功"
        if [[ "$VERBOSE" == "true" ]]; then
            log_debug "Docker 版本信息:"
            docker version --format "客户端版本: {{.Client.Version}}"
            docker version --format "服务端版本: {{.Server.Version}}"
        fi
        return 0
    else
        log_error "❌ Docker 连接测试失败"
        return 1
    fi
}

# 配置本地连接
configure_local() {
    log_header "配置本地 Docker 连接"
    
    local docker_host="unix:///var/run/docker.sock"
    
    if test_docker_connection "$docker_host" "0" ""; then
        if [[ "$TEST_ONLY" == "false" ]]; then
            update_env_file "$docker_host" "0" ""
            log_info "✅ 已切换到本地 Docker"
        fi
    else
        log_error "本地 Docker 连接失败，请检查 Docker 服务是否运行"
        exit 1
    fi
}

# 配置 TCP 连接
configure_tcp() {
    log_header "配置 TCP Docker 连接"
    log_warn "⚠️  TCP 连接不安全，仅适用于开发环境"
    
    local docker_host="tcp://${HOST_IP}:${PORT}"
    
    if test_docker_connection "$docker_host" "0" ""; then
        if [[ "$TEST_ONLY" == "false" ]]; then
            update_env_file "$docker_host" "0" ""
            log_info "✅ 已切换到 TCP Docker: $docker_host"
        fi
    else
        log_error "TCP Docker 连接失败，请检查:"
        log_error "1. 远程主机 Docker 服务是否运行"
        log_error "2. 端口 $PORT 是否开放"
        log_error "3. 防火墙设置"
        exit 1
    fi
}

# 配置 TLS 连接
configure_tls() {
    log_header "配置 TLS Docker 连接"
    
    local docker_host="tcp://${HOST_IP}:${PORT}"
    
    # 检查证书文件
    if [[ ! -d "$CERT_PATH" ]]; then
        log_error "证书目录不存在: $CERT_PATH"
        log_info "请确保证书文件存在:"
        log_info "  - $CERT_PATH/ca.pem"
        log_info "  - $CERT_PATH/cert.pem"
        log_info "  - $CERT_PATH/key.pem"
        exit 1
    fi
    
    local required_files=("ca.pem" "cert.pem" "key.pem")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$CERT_PATH/$file" ]]; then
            log_error "证书文件不存在: $CERT_PATH/$file"
            exit 1
        fi
    done
    
    if test_docker_connection "$docker_host" "1" "$CERT_PATH"; then
        if [[ "$TEST_ONLY" == "false" ]]; then
            update_env_file "$docker_host" "1" "$CERT_PATH"
            log_info "✅ 已切换到 TLS Docker: $docker_host"
        fi
    else
        log_error "TLS Docker 连接失败，请检查:"
        log_error "1. 远程主机 Docker TLS 配置"
        log_error "2. 证书文件是否正确"
        log_error "3. 网络连接"
        exit 1
    fi
}

# 配置 SSH 连接
configure_ssh() {
    log_header "配置 SSH Docker 连接"
    
    local docker_host="ssh://${SSH_TARGET}"
    
    # 测试 SSH 连接
    log_info "测试 SSH 连接..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SSH_TARGET" "echo 'SSH 连接成功'" >/dev/null 2>&1; then
        log_info "✅ SSH 连接测试成功"
    else
        log_error "❌ SSH 连接失败，请检查:"
        log_error "1. SSH 服务是否运行"
        log_error "2. 用户名和主机IP是否正确"
        log_error "3. SSH 密钥认证是否配置"
        exit 1
    fi
    
    if test_docker_connection "$docker_host" "0" ""; then
        if [[ "$TEST_ONLY" == "false" ]]; then
            update_env_file "$docker_host" "0" ""
            log_info "✅ 已切换到 SSH Docker: $docker_host"
        fi
    else
        log_error "SSH Docker 连接失败，请检查远程主机 Docker 服务"
        exit 1
    fi
}

# 更新环境变量文件
update_env_file() {
    local docker_host="$1"
    local tls_verify="$2"
    local cert_path="$3"
    
    log_info "更新环境变量文件: $ENV_FILE"
    
    # 备份原文件
    cp "$ENV_FILE" "${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 更新 DOCKER_HOST
    sed -i "s|^DOCKER_HOST=.*|DOCKER_HOST=$docker_host|" "$ENV_FILE"
    
    # 更新 DOCKER_TLS_VERIFY
    sed -i "s|^DOCKER_TLS_VERIFY=.*|DOCKER_TLS_VERIFY=$tls_verify|" "$ENV_FILE"
    
    # 更新 DOCKER_CERT_PATH
    if [[ -n "$cert_path" ]]; then
        sed -i "s|^DOCKER_CERT_PATH=.*|DOCKER_CERT_PATH=$cert_path|" "$ENV_FILE"
    fi
    
    log_info "✅ 环境变量文件已更新"
}

# 显示当前配置
show_current_config() {
    log_header "当前 Docker 配置"
    
    if [[ -f "$ENV_FILE" ]]; then
        local current_host=$(grep "^DOCKER_HOST=" "$ENV_FILE" | cut -d'=' -f2)
        local current_tls=$(grep "^DOCKER_TLS_VERIFY=" "$ENV_FILE" | cut -d'=' -f2)
        local current_cert=$(grep "^DOCKER_CERT_PATH=" "$ENV_FILE" | cut -d'=' -f2)
        
        echo "Docker Host: $current_host"
        echo "TLS 验证: $current_tls"
        if [[ "$current_tls" == "1" ]]; then
            echo "证书路径: $current_cert"
        fi
    else
        log_warn "环境变量文件不存在: $ENV_FILE"
    fi
}

# 主函数
main() {
    log_header "Docker Host 切换工具"
    
    # 解析和验证参数
    parse_args "$@"
    validate_args
    
    # 显示当前配置
    if [[ "$VERBOSE" == "true" ]]; then
        show_current_config
        echo
    fi
    
    # 根据连接类型执行相应配置
    case "$CONNECTION_TYPE" in
        local)
            configure_local
            ;;
        tcp)
            configure_tcp
            ;;
        tls)
            configure_tls
            ;;
        ssh)
            configure_ssh
            ;;
    esac
    
    if [[ "$TEST_ONLY" == "true" ]]; then
        log_info "🧪 测试模式完成，配置未保存"
    else
        log_info "🎉 Docker Host 切换完成！"
        log_info "请重新加载环境变量或重启服务以使配置生效"
    fi
}

# 执行主函数
main "$@"
