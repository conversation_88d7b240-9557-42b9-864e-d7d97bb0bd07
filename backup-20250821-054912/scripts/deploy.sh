#!/bin/bash

# CI/CD 服务部署脚本
# 支持 Docker Compose 和 Kubernetes 部署方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
CI/CD 服务部署脚本

用法: $0 [选项] <部署方式>

部署方式:
  docker      使用 Docker Compose 部署
  k8s         使用 Kubernetes 部署
  local       本地开发环境部署

选项:
  -h, --help              显示帮助信息
  -e, --env ENV           指定环境 (dev, staging, prod)
  -n, --namespace NS      指定 Kubernetes 命名空间 (默认: cicd-system)
  -r, --registry REG      指定镜像仓库地址
  -t, --tag TAG           指定镜像标签 (默认: latest)
  --skip-build            跳过镜像构建
  --skip-migrate          跳过数据库迁移
  --dry-run               仅显示将要执行的命令，不实际执行

示例:
  $0 docker                           # 使用 Docker Compose 部署
  $0 k8s -e prod -t v1.0.0           # 部署到 Kubernetes 生产环境
  $0 local --skip-build               # 本地部署，跳过构建

EOF
}

# 默认配置
DEPLOY_MODE=""
ENVIRONMENT="dev"
NAMESPACE="cicd-system"
REGISTRY="localhost:5000"
TAG="latest"
SKIP_BUILD=false
SKIP_MIGRATE=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-migrate)
            SKIP_MIGRATE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        docker|k8s|local)
            DEPLOY_MODE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查部署方式
if [[ -z "$DEPLOY_MODE" ]]; then
    log_error "请指定部署方式: docker, k8s, 或 local"
    show_help
    exit 1
fi

# 执行命令函数
execute_command() {
    local cmd="$1"
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY-RUN] $cmd"
    else
        log_info "执行: $cmd"
        eval "$cmd"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    case $DEPLOY_MODE in
        docker)
            if ! command -v docker &> /dev/null; then
                log_error "Docker 未安装"
                exit 1
            fi
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose 未安装"
                exit 1
            fi
            ;;
        k8s)
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl 未安装"
                exit 1
            fi
            if ! kubectl cluster-info &> /dev/null; then
                log_error "无法连接到 Kubernetes 集群"
                exit 1
            fi
            ;;
        local)
            if ! command -v go &> /dev/null; then
                log_error "Go 未安装"
                exit 1
            fi
            ;;
    esac
    
    log_success "依赖检查通过"
}

# 构建镜像
build_image() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_warning "跳过镜像构建"
        return
    fi
    
    log_info "构建 CI/CD 服务镜像..."
    
    local image_name="${REGISTRY}/cicd-service:${TAG}"
    
    execute_command "docker build -t $image_name -f deployments/docker/Dockerfile ."
    
    if [[ "$DEPLOY_MODE" == "k8s" && "$REGISTRY" != "localhost:5000" ]]; then
        log_info "推送镜像到仓库..."
        execute_command "docker push $image_name"
    fi
    
    log_success "镜像构建完成: $image_name"
}

# 准备配置文件
prepare_configs() {
    log_info "准备配置文件..."
    
    local config_dir="deployments/${DEPLOY_MODE}/configs"
    mkdir -p "$config_dir"
    
    # 复制基础配置
    cp "configs/cicd-service.yaml" "$config_dir/"
    
    # 根据环境调整配置
    case $ENVIRONMENT in
        prod)
            sed -i 's/mode: debug/mode: release/g' "$config_dir/cicd-service.yaml"
            sed -i 's/level: debug/level: info/g' "$config_dir/cicd-service.yaml"
            ;;
        staging)
            sed -i 's/mode: debug/mode: release/g' "$config_dir/cicd-service.yaml"
            ;;
    esac
    
    log_success "配置文件准备完成"
}

# Docker Compose 部署
deploy_docker() {
    log_info "使用 Docker Compose 部署..."
    
    cd deployments/docker
    
    # 设置环境变量
    export CICD_IMAGE="${REGISTRY}/cicd-service:${TAG}"
    export ENVIRONMENT="$ENVIRONMENT"
    
    # 启动服务
    execute_command "docker-compose down"
    execute_command "docker-compose up -d"
    
    # 等待服务启动
    if [[ "$DRY_RUN" != "true" ]]; then
        log_info "等待服务启动..."
        sleep 30
        
        # 健康检查
        if curl -f http://localhost:8082/health &> /dev/null; then
            log_success "CI/CD 服务部署成功"
            log_info "服务地址: http://localhost:8082"
            log_info "API 文档: http://localhost:8082/swagger/index.html"
        else
            log_error "服务健康检查失败"
            execute_command "docker-compose logs cicd-service"
            exit 1
        fi
    fi
    
    cd - > /dev/null
}

# Kubernetes 部署
deploy_k8s() {
    log_info "使用 Kubernetes 部署..."
    
    # 创建命名空间
    execute_command "kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
    
    # 更新镜像标签
    local k8s_config="deployments/kubernetes/cicd-service.yaml"
    local temp_config="/tmp/cicd-service-${ENVIRONMENT}.yaml"
    
    cp "$k8s_config" "$temp_config"
    sed -i "s|image: cicd-service:latest|image: ${REGISTRY}/cicd-service:${TAG}|g" "$temp_config"
    sed -i "s|namespace: cicd-system|namespace: ${NAMESPACE}|g" "$temp_config"
    
    # 应用配置
    execute_command "kubectl apply -f $temp_config"
    
    # 等待部署完成
    if [[ "$DRY_RUN" != "true" ]]; then
        log_info "等待部署完成..."
        kubectl rollout status deployment/cicd-service -n "$NAMESPACE" --timeout=300s
        
        # 获取服务信息
        local service_ip=$(kubectl get service cicd-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [[ -z "$service_ip" ]]; then
            service_ip=$(kubectl get service cicd-service -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
        fi
        
        log_success "CI/CD 服务部署成功"
        log_info "命名空间: $NAMESPACE"
        log_info "服务 IP: $service_ip"
        log_info "端口: 8082"
        
        # 显示 Pod 状态
        kubectl get pods -n "$NAMESPACE" -l app=cicd-service
    fi
    
    rm -f "$temp_config"
}

# 本地部署
deploy_local() {
    log_info "本地开发环境部署..."
    
    if [[ "$SKIP_BUILD" != "true" ]]; then
        log_info "构建应用..."
        execute_command "go build -o bin/cicd-service ./cmd/cicd-service"
    fi
    
    # 启动依赖服务
    log_info "启动依赖服务..."
    execute_command "docker-compose -f deployments/docker/docker-compose.yml up -d postgres redis"
    
    # 等待数据库启动
    if [[ "$DRY_RUN" != "true" ]]; then
        log_info "等待数据库启动..."
        sleep 10
    fi
    
    # 数据库迁移
    if [[ "$SKIP_MIGRATE" != "true" ]]; then
        log_info "执行数据库迁移..."
        # 这里可以添加数据库迁移命令
    fi
    
    # 启动服务
    log_info "启动 CI/CD 服务..."
    if [[ "$DRY_RUN" != "true" ]]; then
        export GIN_MODE=debug
        export LOG_LEVEL=debug
        ./bin/cicd-service &
        
        # 等待服务启动
        sleep 5
        
        if curl -f http://localhost:8082/health &> /dev/null; then
            log_success "CI/CD 服务启动成功"
            log_info "服务地址: http://localhost:8082"
            log_info "API 文档: http://localhost:8082/swagger/index.html"
        else
            log_error "服务启动失败"
            exit 1
        fi
    fi
}

# 数据库迁移
migrate_database() {
    if [[ "$SKIP_MIGRATE" == "true" ]]; then
        log_warning "跳过数据库迁移"
        return
    fi
    
    log_info "执行数据库迁移..."
    
    case $DEPLOY_MODE in
        docker)
            execute_command "docker-compose -f deployments/docker/docker-compose.yml exec cicd-service ./cicd-service migrate"
            ;;
        k8s)
            local pod_name=$(kubectl get pods -n "$NAMESPACE" -l app=cicd-service -o jsonpath='{.items[0].metadata.name}')
            execute_command "kubectl exec -n $NAMESPACE $pod_name -- ./cicd-service migrate"
            ;;
        local)
            execute_command "./bin/cicd-service migrate"
            ;;
    esac
    
    log_success "数据库迁移完成"
}

# 主函数
main() {
    log_info "开始部署 CI/CD 服务..."
    log_info "部署方式: $DEPLOY_MODE"
    log_info "环境: $ENVIRONMENT"
    log_info "镜像标签: $TAG"
    
    check_dependencies
    
    if [[ "$DEPLOY_MODE" != "local" ]]; then
        build_image
    fi
    
    prepare_configs
    
    case $DEPLOY_MODE in
        docker)
            deploy_docker
            ;;
        k8s)
            deploy_k8s
            ;;
        local)
            deploy_local
            ;;
    esac
    
    migrate_database
    
    log_success "CI/CD 服务部署完成！"
}

# 执行主函数
main
