#!/bin/bash

# PaaS 平台智能启动脚本
# 根据配置文件自动选择需要的服务

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="docker-compose.flexible.yml"
CONFIG_FILE=""
PROFILES=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
PaaS 平台智能启动脚本

用法: $0 [选项] [命令]

命令:
  start     启动开发环境
  stop      停止开发环境
  restart   重启开发环境
  status    查看服务状态
  logs      查看服务日志
  clean     清理环境（删除容器和数据卷）
  build     重新构建所有镜像
  shell     进入指定服务的容器

选项:
  -h, --help           显示此帮助信息
  -c, --config FILE    指定配置文件
  -m, --minimal        使用最小化配置（仅核心服务）
  -s, --standard       使用标准配置（包含 Redis、数据库等）
  -f, --full           使用完整配置（包含所有服务）
  -d, --detach         后台运行
  -v, --verbose        详细输出
  --no-build           不重新构建镜像

配置模式:
  minimal    最小化模式：仅核心服务，使用 SQLite，无 Redis
  standard   标准模式：包含 PostgreSQL、Redis、CI/CD
  full       完整模式：包含所有服务（Gitea、Registry 等）

示例:
  $0 start --minimal           # 启动最小化环境
  $0 start --standard          # 启动标准环境
  $0 start --full              # 启动完整环境
  $0 start -c app.custom.yaml  # 使用自定义配置
  $0 logs api-gateway          # 查看 API Gateway 日志
  $0 shell user-service        # 进入用户服务容器

EOF
}

# 检查 Docker 环境
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 守护进程未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 检测配置文件并设置环境变量
setup_config() {
    local config_mode="$1"

    # 根据模式选择配置文件
    case "$config_mode" in
        "minimal")
            CONFIG_FILE="configs/app.minimal.yaml"
            PROFILES=""
            ;;
        "standard")
            CONFIG_FILE="configs/app.standard.yaml"
            PROFILES="database,redis,cicd,config"
            ;;
        "full")
            CONFIG_FILE="configs/app.standard.yaml"
            PROFILES="database,redis,cicd,config,git,registry"
            ;;
        *)
            # 自动检测配置文件
            if [[ -f "$PROJECT_ROOT/configs/app.minimal.yaml" ]]; then
                CONFIG_FILE="configs/app.minimal.yaml"
                PROFILES=""
                config_mode="minimal"
            elif [[ -f "$PROJECT_ROOT/configs/app.standard.yaml" ]]; then
                CONFIG_FILE="configs/app.standard.yaml"
                PROFILES="database,redis,cicd,config"
                config_mode="standard"
            else
                CONFIG_FILE="configs/app.yaml"
                PROFILES="database,redis"
                config_mode="auto"
            fi
            ;;
    esac

    log_info "使用配置模式: $config_mode"
    log_info "配置文件: $CONFIG_FILE"
    log_info "启用的服务组: $PROFILES"

    # 设置环境变量
    export CONFIG_FILE
    export LOG_LEVEL=${LOG_LEVEL:-info}

    # 根据配置模式设置服务相关环境变量
    case "$config_mode" in
        "minimal")
            export DATABASE_ENABLED=false
            export DATABASE_DRIVER=sqlite
            export DATABASE_DSN="./data/paas.db"
            export REDIS_ENABLED=false
            export DOCKER_REGISTRY_ENABLED=false
            ;;
        "standard"|"full")
            export DATABASE_ENABLED=true
            export DATABASE_DRIVER=postgres
            export DATABASE_DSN="host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
            export REDIS_ENABLED=true
            export REDIS_ADDR="redis:6379"
            export DOCKER_REGISTRY_ENABLED=$([[ "$config_mode" == "full" ]] && echo "true" || echo "false")
            export DOCKER_REGISTRY_URL="localhost:5000"
            export GITEA_URL="http://gitea:3000"
            ;;
    esac

    # JWT 配置
    export JWT_SECRET=${JWT_SECRET:-"dev-secret-key-$(date +%s)"}

    # 项目名称
    export COMPOSE_PROJECT_NAME="paas-${config_mode}"
}

# 启动服务
start_services() {
    local detach="$1"
    local no_build="$2"

    log_info "启动 PaaS 平台开发环境..."

    cd "$PROJECT_ROOT"

    # 构建 docker-compose 参数
    local compose_args=("-f" "$COMPOSE_FILE")

    # 添加 profiles 参数
    if [[ -n "$PROFILES" ]]; then
        IFS=',' read -ra PROFILE_ARRAY <<< "$PROFILES"
        for profile in "${PROFILE_ARRAY[@]}"; do
            compose_args+=("--profile" "$profile")
        done
        log_info "启用服务组: $PROFILES"
    else
        log_info "启动核心服务（最小化模式）"
    fi

    # 构建镜像
    if [[ "$no_build" != "true" ]]; then
        log_info "构建服务镜像..."
        docker-compose "${compose_args[@]}" build --parallel
    fi

    # 启动服务
    if [[ "$detach" == "true" ]]; then
        docker-compose "${compose_args[@]}" up -d
    else
        docker-compose "${compose_args[@]}" up
    fi

    if [[ "$detach" == "true" ]]; then
        log_success "服务已在后台启动"
        show_service_info
    fi
}

# 停止服务
stop_services() {
    log_info "停止 PaaS 平台开发环境..."

    cd "$PROJECT_ROOT"

    # 构建 docker-compose 参数
    local compose_args=("-f" "$COMPOSE_FILE")
    if [[ -n "$PROFILES" ]]; then
        IFS=',' read -ra PROFILE_ARRAY <<< "$PROFILES"
        for profile in "${PROFILE_ARRAY[@]}"; do
            compose_args+=("--profile" "$profile")
        done
    fi

    docker-compose "${compose_args[@]}" down

    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 PaaS 平台开发环境..."
    stop_services
    start_services "true" "true"
}

# 查看服务状态
show_status() {
    log_info "查看服务状态..."

    cd "$PROJECT_ROOT"

    # 构建 docker-compose 参数
    local compose_args=("-f" "$COMPOSE_FILE")
    if [[ -n "$PROFILES" ]]; then
        IFS=',' read -ra PROFILE_ARRAY <<< "$PROFILES"
        for profile in "${PROFILE_ARRAY[@]}"; do
            compose_args+=("--profile" "$profile")
        done
    fi

    docker-compose "${compose_args[@]}" ps
}

# 查看服务日志
show_logs() {
    local service="$1"
    
    cd "$PROJECT_ROOT"
    
    if [[ -n "$service" ]]; then
        log_info "查看 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    fi
}

# 清理环境
clean_environment() {
    log_warning "这将删除所有容器、网络和数据卷！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理本地开发环境..."
        
        cd "$PROJECT_ROOT"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --remove-orphans
        
        # 清理相关镜像
        log_info "清理构建的镜像..."
        docker images --filter "reference=paas-*" -q | xargs -r docker rmi -f
        
        log_success "环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 重新构建镜像
rebuild_images() {
    log_info "重新构建所有镜像..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache --parallel
    
    log_success "镜像重新构建完成"
}

# 进入容器 shell
enter_shell() {
    local service="$1"
    
    if [[ -z "$service" ]]; then
        log_error "请指定服务名称"
        exit 1
    fi
    
    log_info "进入 $service 容器..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec "$service" /bin/bash
}

# 显示服务信息
show_service_info() {
    log_info "服务访问信息："
    echo
    echo "  🌐 API Gateway:     http://localhost:8080"
    echo "  👤 User Service:    http://localhost:8083"
    echo "  📦 App Manager:     http://localhost:8081"
    echo "  🔧 Config Service:  http://localhost:8084"
    echo "  🚀 CI/CD Service:   http://localhost:8082"
    echo "  🗄️  PostgreSQL:      localhost:5432"
    echo "  🔴 Redis:           localhost:6379"
    echo
    echo "  📊 查看状态: $0 status"
    echo "  📝 查看日志: $0 logs [service]"
    echo "  🛑 停止服务: $0 stop"
    echo
}

# 主函数
main() {
    local command=""
    local config_mode=""
    local custom_config=""
    local detach="false"
    local verbose="false"
    local no_build="false"
    local service=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--config)
                custom_config="$2"
                shift 2
                ;;
            -m|--minimal)
                config_mode="minimal"
                shift
                ;;
            -s|--standard)
                config_mode="standard"
                shift
                ;;
            -f|--full)
                config_mode="full"
                shift
                ;;
            -d|--detach)
                detach="true"
                shift
                ;;
            -v|--verbose)
                verbose="true"
                set -x
                shift
                ;;
            --no-build)
                no_build="true"
                shift
                ;;
            start|stop|restart|status|logs|clean|build|shell)
                command="$1"
                shift
                ;;
            *)
                if [[ -z "$service" ]]; then
                    service="$1"
                fi
                shift
                ;;
        esac
    done

    # 默认命令
    if [[ -z "$command" ]]; then
        command="start"
    fi

    # 检查环境
    check_docker

    # 设置配置
    if [[ -n "$custom_config" ]]; then
        CONFIG_FILE="$custom_config"
        log_info "使用自定义配置文件: $CONFIG_FILE"
        # 根据配置文件内容推断需要的服务
        setup_config "auto"
    else
        setup_config "${config_mode:-auto}"
    fi

    # 执行命令
    case "$command" in
        start)
            start_services "$detach" "$no_build"
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$service"
            ;;
        clean)
            clean_environment
            ;;
        build)
            rebuild_images
            ;;
        shell)
            enter_shell "$service"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
