#!/bin/bash

# PaaS平台开发环境认证测试脚本
# 此脚本用于测试开发环境中的认证跳过功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 测试服务健康检查
test_health_check() {
    local service_name=$1
    local port=$2
    local url="http://localhost:$port/health"
    
    log_test "测试 ${service_name} 健康检查..."
    
    if curl -s "$url" > /dev/null 2>&1; then
        local response=$(curl -s "$url")
        log_success "${service_name} 健康检查通过"
        echo "    响应: $response"
    else
        log_error "${service_name} 健康检查失败"
        return 1
    fi
    
    echo
}

# 测试无认证API访问
test_no_auth_api() {
    local service_name=$1
    local port=$2
    local endpoint=$3
    local url="http://localhost:$port$endpoint"
    
    log_test "测试 ${service_name} 无认证API访问: $endpoint"
    
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" "$url" 2>/dev/null || echo "HTTP_CODE:000")
    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "404" ]; then
        log_success "${service_name} API访问成功 (HTTP $http_code)"
        if [ -n "$body" ] && [ "$body" != "404 page not found" ]; then
            echo "    响应: ${body:0:100}..."
        fi
    elif [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
        log_error "${service_name} API访问被拒绝 (HTTP $http_code) - 认证可能未正确跳过"
        echo "    响应: $body"
        return 1
    elif [ "$http_code" = "000" ]; then
        log_error "${service_name} 连接失败 - 服务可能未启动"
        return 1
    else
        log_warn "${service_name} API返回 HTTP $http_code"
        echo "    响应: $body"
    fi
    
    echo
}

# 测试带开发令牌的API访问
test_dev_token_api() {
    local service_name=$1
    local port=$2
    local endpoint=$3
    local dev_token="dev-token-2024-secure"
    local url="http://localhost:$port$endpoint"
    
    log_test "测试 ${service_name} 开发令牌API访问: $endpoint"
    
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Authorization: Bearer $dev_token" \
        "$url" 2>/dev/null || echo "HTTP_CODE:000")
    
    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "404" ]; then
        log_success "${service_name} 开发令牌访问成功 (HTTP $http_code)"
        if [ -n "$body" ] && [ "$body" != "404 page not found" ]; then
            echo "    响应: ${body:0:100}..."
        fi
    else
        log_warn "${service_name} 开发令牌访问返回 HTTP $http_code"
        echo "    响应: $body"
    fi
    
    echo
}

# 测试Swagger文档访问
test_swagger_access() {
    local service_name=$1
    local port=$2
    local url="http://localhost:$port/swagger/index.html"
    
    log_test "测试 ${service_name} Swagger文档访问..."
    
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" "$url" 2>/dev/null || echo "HTTP_CODE:000")
    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        log_success "${service_name} Swagger文档访问成功"
        echo "    URL: $url"
    elif [ "$http_code" = "404" ]; then
        log_warn "${service_name} Swagger文档未找到 (可能未启用)"
    else
        log_warn "${service_name} Swagger文档访问返回 HTTP $http_code"
    fi
    
    echo
}

# 运行所有测试
run_all_tests() {
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                  开发环境认证测试                          ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    local services=(
        "应用管理服务:8081:/api/v1/apps"
        "CI/CD服务:8082:/api/v1/pipelines"
        "配置服务:8083:/api/v1/configs"
        "脚本执行服务:8084:/api/v1/scripts"
    )

    # 🔧 特殊测试：登录API测试
    echo -e "${YELLOW}━━━ 测试登录API ━━━${NC}"
    test_login_api
    echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name port endpoint <<< "$service_info"
        
        echo -e "${YELLOW}━━━ 测试 ${service_name} ━━━${NC}"
        
        # 健康检查测试
        total_tests=$((total_tests + 1))
        if test_health_check "$service_name" "$port"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        
        # 无认证API测试
        total_tests=$((total_tests + 1))
        if test_no_auth_api "$service_name" "$port" "$endpoint"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        
        # 开发令牌API测试
        total_tests=$((total_tests + 1))
        if test_dev_token_api "$service_name" "$port" "$endpoint"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        
        # Swagger文档测试
        total_tests=$((total_tests + 1))
        if test_swagger_access "$service_name" "$port"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        
        echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
        echo
    done
    
    # 显示测试结果
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        测试结果                            ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║  总测试数: $total_tests                                           ║${NC}"
    echo -e "${BLUE}║  通过: ${GREEN}$passed_tests${BLUE}                                              ║${NC}"
    echo -e "${BLUE}║  失败: ${RED}$failed_tests${BLUE}                                              ║${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${BLUE}║  状态: ${GREEN}✅ 所有测试通过${BLUE}                                  ║${NC}"
    else
        echo -e "${BLUE}║  状态: ${RED}❌ 部分测试失败${BLUE}                                  ║${NC}"
    fi
    
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    if [ $failed_tests -eq 0 ]; then
        log_success "🎉 开发环境认证跳过配置正常工作！"
        echo -e "${GREEN}✅ 所有服务都可以在无认证情况下访问${NC}"
        echo -e "${GREEN}✅ 开发令牌认证也正常工作${NC}"
        echo -e "${GREEN}✅ API文档可以正常访问${NC}"
    else
        log_error "❌ 开发环境认证配置存在问题"
        echo -e "${RED}请检查以下内容：${NC}"
        echo -e "${RED}1. 服务是否使用了开发环境配置文件${NC}"
        echo -e "${RED}2. 环境变量 PAAS_AUTH_ENABLED 是否设置为 false${NC}"
        echo -e "${RED}3. 环境变量 PAAS_DEV_MODE 是否设置为 true${NC}"
        echo -e "${RED}4. 服务是否正确加载了认证中间件配置${NC}"
    fi
    
    echo
    return $failed_tests
}

# 测试登录API
test_login_api() {
    log_test "测试登录API..."

    local login_url="http://localhost:8081/api/v1/auth/login"
    local login_data='{"username":"test","password":"test123"}'

    local response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$login_url" 2>/dev/null || echo "HTTP_CODE:000")

    local http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

    if [ "$http_code" = "200" ]; then
        log_success "登录API测试成功 (HTTP $http_code)"
        echo "    响应包含access_token: $(echo "$body" | grep -o '"access_token"' | wc -l)"
    elif [ "$http_code" = "401" ]; then
        # 401是正常的，说明API工作但认证失败
        log_success "登录API正常工作 (HTTP $http_code - 认证失败是预期的)"
        echo "    这表明登录API正常响应，只是凭据无效"
    elif [ "$http_code" = "000" ]; then
        log_error "登录API连接失败 - 应用管理服务可能未启动"
        return 1
    else
        log_warn "登录API返回 HTTP $http_code"
        echo "    响应: $body"
    fi

    echo
}

# 显示环境信息
show_env_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                      环境信息                              ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  PAAS_AUTH_ENABLED: ${PAAS_AUTH_ENABLED:-未设置}                      ║${NC}"
    echo -e "${CYAN}║  PAAS_DEV_MODE: ${PAAS_DEV_MODE:-未设置}                          ║${NC}"
    echo -e "${CYAN}║  ENV: ${ENV:-未设置}                                      ║${NC}"
    echo -e "${CYAN}║  GO_ENV: ${GO_ENV:-未设置}                                ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🧪 PaaS平台开发环境认证测试${NC}"
    echo
    
    # 显示环境信息
    show_env_info
    
    # 运行所有测试
    if run_all_tests; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
