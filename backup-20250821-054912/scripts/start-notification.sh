#!/bin/bash

# PaaS 平台告警通知系统启动脚本
# 启动告警通知服务和相关组件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/configs"
NOTIFICATION_PORT="${NOTIFICATION_PORT:-8090}"

echo -e "${BLUE}🔔 PaaS 平台告警通知系统启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.19+"
        exit 1
    fi
    
    # 检查 Go 版本
    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    local major_version=$(echo $go_version | cut -d. -f1)
    local minor_version=$(echo $go_version | cut -d. -f2)
    
    if [[ $major_version -lt 1 ]] || [[ $major_version -eq 1 && $minor_version -lt 19 ]]; then
        print_error "Go 版本过低 ($go_version)，需要 1.19+"
        exit 1
    fi
    
    print_success "Go 版本检查通过 ($go_version)"
}

# 函数：检查配置文件
check_config_files() {
    print_info "检查配置文件..."
    
    local required_configs=(
        "notification-config.yaml"
        "alertmanager.yml"
    )
    
    local missing_configs=()
    
    for config in "${required_configs[@]}"; do
        if [[ ! -f "$CONFIG_DIR/$config" ]]; then
            missing_configs+=("$config")
        fi
    done
    
    if [[ ${#missing_configs[@]} -gt 0 ]]; then
        print_warning "缺少配置文件: ${missing_configs[*]}"
        print_info "将使用默认配置启动服务"
    else
        print_success "配置文件检查通过"
    fi
}

# 函数：检查端口占用
check_port() {
    print_info "检查端口 $NOTIFICATION_PORT 占用情况..."
    
    if netstat -tuln 2>/dev/null | grep -q ":$NOTIFICATION_PORT "; then
        print_error "端口 $NOTIFICATION_PORT 已被占用"
        print_info "请设置环境变量 NOTIFICATION_PORT 使用其他端口"
        exit 1
    else
        print_success "端口 $NOTIFICATION_PORT 可用"
    fi
}

# 函数：构建通知服务
build_notification_service() {
    print_info "构建通知服务..."
    
    cd "$PROJECT_ROOT"
    
    # 创建临时的 main.go 文件用于通知服务
    cat > cmd/notification-service/main.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/internal/notification"
	"paas-platform/pkg/logger"
)

func main() {
	// 初始化日志
	logger := logger.NewLogger("notification-service", "info")
	
	// 创建通知服务配置
	config := &notification.Config{
		Email: notification.EmailConfig{
			Enabled:   true,
			SMTPHost:  "smtp.gmail.com",
			SMTPPort:  587,
			Username:  "<EMAIL>",
			Password:  "your_app_password",
			FromEmail: "<EMAIL>",
			FromName:  "PaaS Platform Alerts",
			UseTLS:    true,
		},
		Slack: notification.SlackConfig{
			Enabled:    true,
			WebhookURL: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
			Channel:    "#paas-alerts",
			Username:   "PaaS AlertBot",
			IconEmoji:  ":rotating_light:",
		},
		SMS: notification.SMSConfig{
			Enabled:   false,
			Provider:  "aliyun",
			AccessKey: "your_access_key",
			SecretKey: "your_secret_key",
			SignName:  "PaaS平台",
		},
		DingTalk: notification.DingTalkConfig{
			Enabled:    true,
			WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN",
			Secret:     "your_secret_key",
		},
		WeChat: notification.WeChatConfig{
			Enabled: false,
			CorpID:  "your_corp_id",
			AgentID: 1000001,
			Secret:  "your_secret",
			ToUser:  "@all",
		},
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		RateLimit:  100,
	}
	
	// 创建通知服务
	notificationService := notification.NewNotificationService(config, logger)
	
	// 创建 HTTP 处理器
	handler := notification.NewHandler(notificationService, logger)
	
	// 创建 Gin 路由
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	router.Use(gin.Recovery())
	
	// 添加日志中间件
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		
		logger.Info("HTTP请求",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status", c.Writer.Status(),
			"duration", duration,
			"client_ip", c.ClientIP())
	})
	
	// 注册路由
	v1 := router.Group("/api/v1/notifications")
	handler.RegisterRoutes(v1)
	
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "notification-service",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// 启动服务器
	port := os.Getenv("NOTIFICATION_PORT")
	if port == "" {
		port = "8090"
	}
	
	srv := &http.Server{
		Addr:    ":" + port,
		Handler: router,
	}
	
	// 优雅关闭
	go func() {
		logger.Info("通知服务启动", "port", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭通知服务...")
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("服务关闭失败", "error", err)
	}
	
	logger.Info("通知服务已关闭")
}
EOF
    
    # 创建目录
    mkdir -p cmd/notification-service
    
    # 构建服务
    print_info "编译通知服务..."
    go build -o bin/notification-service cmd/notification-service/main.go
    
    if [[ $? -eq 0 ]]; then
        print_success "通知服务构建成功"
    else
        print_error "通知服务构建失败"
        exit 1
    fi
}

# 函数：启动通知服务
start_notification_service() {
    print_info "启动通知服务..."
    
    # 设置环境变量
    export NOTIFICATION_PORT="$NOTIFICATION_PORT"
    export GIN_MODE="release"
    
    # 启动服务
    cd "$PROJECT_ROOT"
    nohup ./bin/notification-service > logs/notification-service.log 2>&1 &
    local pid=$!
    
    # 保存 PID
    echo $pid > /tmp/notification-service.pid
    
    # 等待服务启动
    print_info "等待通知服务启动..."
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s -f "http://localhost:$NOTIFICATION_PORT/health" > /dev/null 2>&1; then
            print_success "通知服务启动成功 (PID: $pid)"
            return 0
        fi
        
        attempt=$((attempt + 1))
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "通知服务启动超时"
            return 1
        fi
        
        sleep 2
    done
}

# 函数：测试通知功能
test_notification() {
    print_info "测试通知功能..."
    
    # 测试健康检查
    if curl -s -f "http://localhost:$NOTIFICATION_PORT/health" > /dev/null; then
        print_success "健康检查通过"
    else
        print_warning "健康检查失败"
    fi
    
    # 测试通知接口
    print_info "测试通知接口..."
    
    # 测试邮件通知 (模拟)
    local email_test=$(curl -s -X POST "http://localhost:$NOTIFICATION_PORT/api/v1/notifications/test?type=email" \
        -H "Content-Type: application/json" \
        -w "%{http_code}")
    
    if [[ "${email_test: -3}" == "200" ]]; then
        print_success "邮件通知接口测试通过"
    else
        print_warning "邮件通知接口测试失败"
    fi
    
    # 测试 Slack 通知 (模拟)
    local slack_test=$(curl -s -X POST "http://localhost:$NOTIFICATION_PORT/api/v1/notifications/test?type=slack" \
        -H "Content-Type: application/json" \
        -w "%{http_code}")
    
    if [[ "${slack_test: -3}" == "200" ]]; then
        print_success "Slack 通知接口测试通过"
    else
        print_warning "Slack 通知接口测试失败"
    fi
}

# 函数：显示访问信息
show_access_info() {
    echo ""
    echo "=================================================="
    print_success "🎉 PaaS 平台告警通知系统启动完成！"
    echo "=================================================="
    echo ""
    echo "🔔 通知服务信息："
    echo "  • 服务地址:     http://localhost:$NOTIFICATION_PORT"
    echo "  • 健康检查:     http://localhost:$NOTIFICATION_PORT/health"
    echo "  • 服务状态:     http://localhost:$NOTIFICATION_PORT/api/v1/notifications/status"
    echo "  • 统计信息:     http://localhost:$NOTIFICATION_PORT/api/v1/notifications/statistics"
    echo ""
    echo "📡 API 接口："
    echo "  • 邮件通知:     POST /api/v1/notifications/email"
    echo "  • Slack通知:    POST /api/v1/notifications/slack"
    echo "  • 短信通知:     POST /api/v1/notifications/sms"
    echo "  • 钉钉通知:     POST /api/v1/notifications/dingtalk"
    echo "  • 企业微信:     POST /api/v1/notifications/wechat"
    echo "  • Webhook:      POST /api/v1/notifications/webhook"
    echo "  • 批量通知:     POST /api/v1/notifications/batch"
    echo "  • AlertManager: POST /api/v1/notifications/alertmanager"
    echo ""
    echo "🧪 测试接口："
    echo "  • 功能测试:     POST /api/v1/notifications/test"
    echo "  • 邮件测试:     POST /api/v1/notifications/test?type=email"
    echo "  • Slack测试:    POST /api/v1/notifications/test?type=slack"
    echo ""
    echo "🔧 管理命令："
    echo "  • 查看日志:     tail -f logs/notification-service.log"
    echo "  • 停止服务:     $0 stop"
    echo "  • 重启服务:     $0 restart"
    echo "  • 查看状态:     $0 status"
    echo ""
    echo "📝 下一步："
    echo "  1. 配置邮件服务器信息"
    echo "  2. 配置 Slack Webhook URL"
    echo "  3. 配置钉钉机器人"
    echo "  4. 测试各种通知渠道"
    echo "  5. 集成到 AlertManager"
    echo ""
}

# 函数：停止服务
stop_service() {
    print_info "停止通知服务..."
    
    if [[ -f /tmp/notification-service.pid ]]; then
        local pid=$(cat /tmp/notification-service.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid
            fi
            
            rm -f /tmp/notification-service.pid
            print_success "通知服务已停止"
        else
            print_warning "通知服务进程不存在"
            rm -f /tmp/notification-service.pid
        fi
    else
        print_warning "未找到通知服务 PID 文件"
    fi
}

# 函数：查看服务状态
check_status() {
    print_info "检查通知服务状态..."
    
    if [[ -f /tmp/notification-service.pid ]]; then
        local pid=$(cat /tmp/notification-service.pid)
        if kill -0 $pid 2>/dev/null; then
            print_success "通知服务正在运行 (PID: $pid)"
            
            # 检查健康状态
            if curl -s -f "http://localhost:$NOTIFICATION_PORT/health" > /dev/null; then
                print_success "服务健康检查通过"
            else
                print_warning "服务健康检查失败"
            fi
        else
            print_error "通知服务进程不存在"
        fi
    else
        print_error "通知服务未运行"
    fi
}

# 主函数
main() {
    # 检查参数
    case "${1:-}" in
        "stop")
            stop_service
            exit 0
            ;;
        "restart")
            stop_service
            sleep 2
            ;;
        "status")
            check_status
            exit 0
            ;;
        "test")
            test_notification
            exit 0
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令："
            echo "  start     启动通知服务 (默认)"
            echo "  stop      停止通知服务"
            echo "  restart   重启通知服务"
            echo "  status    查看服务状态"
            echo "  test      测试通知功能"
            echo "  help      显示帮助信息"
            exit 0
            ;;
    esac
    
    # 执行启动流程
    check_dependencies
    check_config_files
    check_port
    build_notification_service
    start_notification_service
    test_notification
    show_access_info
}

# 脚本入口
main "$@"
