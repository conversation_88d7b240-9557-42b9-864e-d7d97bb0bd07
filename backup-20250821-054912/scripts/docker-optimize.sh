#!/bin/bash

# Docker 容器化优化脚本
# 优化 Docker 镜像构建和容器运行配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REGISTRY=${DOCKER_REGISTRY:-"localhost:5000"}
NAMESPACE=${DOCKER_NAMESPACE:-"paas"}
VERSION=${VERSION:-"latest"}

echo -e "${PURPLE}🐳 Docker 容器化优化脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：优化 Dockerfile
optimize_dockerfiles() {
    print_title "优化 Dockerfile"
    
    # 优化 Go 服务 Dockerfile
    for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer; do
        local dockerfile="$PROJECT_ROOT/services/$service/Dockerfile"
        
        if [[ -f "$dockerfile" ]]; then
            print_info "优化 $service Dockerfile..."
            
            # 创建优化后的 Dockerfile
            cat > "$dockerfile.optimized" << 'EOF'
# 多阶段构建优化 Dockerfile
FROM golang:1.21-alpine AS builder

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖（利用 Docker 缓存层）
RUN go mod download

# 复制源代码
COPY . .

# 构建应用（启用优化）
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main ./cmd/main.go

# 最终镜像
FROM scratch

# 从 builder 阶段复制必要文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /app/main /main

# 设置时区
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/main", "health"]

# 运行应用
ENTRYPOINT ["/main"]
EOF

            # 备份原文件并替换
            mv "$dockerfile" "$dockerfile.backup"
            mv "$dockerfile.optimized" "$dockerfile"
            
            print_success "$service Dockerfile 已优化"
        else
            print_warning "$service Dockerfile 不存在，跳过"
        fi
    done
    
    # 优化前端 Dockerfile
    local frontend_dockerfile="$PROJECT_ROOT/web/Dockerfile"
    if [[ -f "$frontend_dockerfile" ]]; then
        print_info "优化前端 Dockerfile..."
        
        cat > "$frontend_dockerfile.optimized" << 'EOF'
# 多阶段构建优化前端 Dockerfile
FROM node:18-alpine AS builder

# 安装必要工具
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖（利用缓存）
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产镜像
FROM nginx:alpine

# 复制自定义 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建非 root 用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 切换到非 root 用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
EOF

        mv "$frontend_dockerfile" "$frontend_dockerfile.backup"
        mv "$frontend_dockerfile.optimized" "$frontend_dockerfile"
        
        print_success "前端 Dockerfile 已优化"
    fi
}

# 函数：创建 .dockerignore 文件
create_dockerignore() {
    print_title "创建 .dockerignore 文件"
    
    # 为每个服务创建 .dockerignore
    for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer; do
        local service_dir="$PROJECT_ROOT/services/$service"
        local dockerignore="$service_dir/.dockerignore"
        
        if [[ -d "$service_dir" ]]; then
            print_info "创建 $service .dockerignore..."
            
            cat > "$dockerignore" << 'EOF'
# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
docs/

# Development files
.env
.env.local
.env.development
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Build artifacts
bin/
build/
dist/
target/

# Test files
*_test.go
test/
tests/
coverage.out
*.test

# Temporary files
tmp/
temp/
*.tmp
*.log

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
Jenkinsfile
.travis.yml

# Kubernetes files
k8s/
kubernetes/
helm/
EOF

            print_success "$service .dockerignore 已创建"
        fi
    done
    
    # 为前端创建 .dockerignore
    local frontend_dockerignore="$PROJECT_ROOT/web/.dockerignore"
    print_info "创建前端 .dockerignore..."
    
    cat > "$frontend_dockerignore" << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist/
build/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
docs/

# Test files
tests/
test/
coverage/
*.test.js

# Linting
.eslintcache

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
Jenkinsfile

# Temporary files
tmp/
temp/
*.tmp
*.log
EOF

    print_success "前端 .dockerignore 已创建"
}

# 函数：优化镜像构建
optimize_image_build() {
    print_title "优化镜像构建"
    
    # 创建构建脚本
    local build_script="$PROJECT_ROOT/scripts/build-images.sh"
    
    cat > "$build_script" << 'EOF'
#!/bin/bash

# 镜像构建脚本
set -e

REGISTRY=${DOCKER_REGISTRY:-"localhost:5000"}
NAMESPACE=${DOCKER_NAMESPACE:-"paas"}
VERSION=${VERSION:-"latest"}
BUILD_ARGS=${BUILD_ARGS:-""}

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 构建服务镜像
build_service() {
    local service=$1
    local service_dir="services/$service"
    
    if [[ -d "$service_dir" ]]; then
        print_info "构建 $service 镜像..."
        
        docker build \
            --build-arg VERSION="$VERSION" \
            --build-arg BUILD_TIME="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            --build-arg GIT_COMMIT="$(git rev-parse HEAD)" \
            $BUILD_ARGS \
            -t "$REGISTRY/$NAMESPACE/$service:$VERSION" \
            -t "$REGISTRY/$NAMESPACE/$service:latest" \
            "$service_dir"
        
        print_success "$service 镜像构建完成"
    else
        echo "警告: $service_dir 不存在"
    fi
}

# 构建前端镜像
build_frontend() {
    print_info "构建前端镜像..."
    
    docker build \
        --build-arg VERSION="$VERSION" \
        --build-arg BUILD_TIME="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        --build-arg GIT_COMMIT="$(git rev-parse HEAD)" \
        $BUILD_ARGS \
        -t "$REGISTRY/$NAMESPACE/web:$VERSION" \
        -t "$REGISTRY/$NAMESPACE/web:latest" \
        web/
    
    print_success "前端镜像构建完成"
}

# 并行构建所有镜像
build_all() {
    print_info "开始并行构建所有镜像..."
    
    # 服务列表
    services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "ci-cd-service"
        "monitoring-service"
        "notification-service"
        "load-balancer"
    )
    
    # 并行构建服务
    for service in "${services[@]}"; do
        build_service "$service" &
    done
    
    # 构建前端
    build_frontend &
    
    # 等待所有构建完成
    wait
    
    print_success "所有镜像构建完成"
}

# 推送镜像
push_images() {
    print_info "推送镜像到仓库..."
    
    services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "ci-cd-service"
        "monitoring-service"
        "notification-service"
        "load-balancer"
        "web"
    )
    
    for service in "${services[@]}"; do
        docker push "$REGISTRY/$NAMESPACE/$service:$VERSION"
        docker push "$REGISTRY/$NAMESPACE/$service:latest"
    done
    
    print_success "镜像推送完成"
}

# 清理悬空镜像
cleanup() {
    print_info "清理悬空镜像..."
    docker image prune -f
    print_success "清理完成"
}

# 主函数
main() {
    case "${1:-all}" in
        "all")
            build_all
            ;;
        "push")
            push_images
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            build_service "$1"
            ;;
    esac
}

main "$@"
EOF

    chmod +x "$build_script"
    print_success "镜像构建脚本已创建: $build_script"
}

# 函数：创建 Docker Compose 优化配置
create_docker_compose() {
    print_title "创建 Docker Compose 优化配置"
    
    local compose_file="$PROJECT_ROOT/docker-compose.prod.yml"
    
    cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  # API 网关
  api-gateway:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/api-gateway:${VERSION:-latest}
    ports:
      - "8080:8080"
    environment:
      - ENV=production
      - LOG_LEVEL=info
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - paas-network
    depends_on:
      - user-service
      - app-manager

  # 用户服务
  user-service:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/user-service:${VERSION:-latest}
    environment:
      - ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.15'
          memory: 128M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network
    depends_on:
      - postgres
      - redis

  # 应用管理服务
  app-manager:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/app-manager:${VERSION:-latest}
    environment:
      - ENV=production
      - DB_HOST=postgres
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network
    depends_on:
      - postgres

  # CI/CD 服务
  ci-cd-service:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/ci-cd-service:${VERSION:-latest}
    environment:
      - ENV=production
      - DB_HOST=postgres
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network
    depends_on:
      - postgres

  # 监控服务
  monitoring-service:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/monitoring-service:${VERSION:-latest}
    environment:
      - ENV=production
      - DB_HOST=postgres
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network
    depends_on:
      - postgres

  # 通知服务
  notification-service:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/notification-service:${VERSION:-latest}
    environment:
      - ENV=production
      - REDIS_HOST=redis
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.15'
          memory: 128M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network
    depends_on:
      - redis

  # 负载均衡器
  load-balancer:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/load-balancer:${VERSION:-latest}
    ports:
      - "80:8080"
      - "443:8443"
    environment:
      - ENV=production
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    healthcheck:
      test: ["CMD", "/main", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network

  # 前端
  web:
    image: ${REGISTRY:-localhost:5000}/${NAMESPACE:-paas}/web:${VERSION:-latest}
    ports:
      - "3000:8080"
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.15'
          memory: 128M
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=paas
      - POSTGRES_USER=paas
      - POSTGRES_PASSWORD=paas123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - paas-network

volumes:
  postgres_data:
  redis_data:

networks:
  paas-network:
    driver: overlay
    attachable: true
EOF

    print_success "Docker Compose 生产配置已创建: $compose_file"
}

# 函数：创建容器安全配置
create_security_config() {
    print_title "创建容器安全配置"
    
    # 创建安全策略文件
    local security_file="$PROJECT_ROOT/docker-security.yml"
    
    cat > "$security_file" << 'EOF'
# Docker 安全配置
version: '3.8'

x-security-defaults: &security-defaults
  security_opt:
    - no-new-privileges:true
  read_only: true
  tmpfs:
    - /tmp:noexec,nosuid,size=100m
  user: "1001:1001"
  cap_drop:
    - ALL
  cap_add:
    - NET_BIND_SERVICE

x-logging-defaults: &logging-defaults
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"

services:
  api-gateway:
    <<: *security-defaults
    <<: *logging-defaults
    
  user-service:
    <<: *security-defaults
    <<: *logging-defaults
    
  app-manager:
    <<: *security-defaults
    <<: *logging-defaults
    
  ci-cd-service:
    <<: *security-defaults
    <<: *logging-defaults
    read_only: false  # CI/CD 需要写权限
    
  monitoring-service:
    <<: *security-defaults
    <<: *logging-defaults
    
  notification-service:
    <<: *security-defaults
    <<: *logging-defaults
    
  load-balancer:
    <<: *security-defaults
    <<: *logging-defaults
    
  web:
    <<: *security-defaults
    <<: *logging-defaults
EOF

    print_success "容器安全配置已创建: $security_file"
}

# 函数：显示帮助信息
show_help() {
    echo "Docker 容器化优化脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --dockerfiles        优化 Dockerfile"
    echo "  -i, --ignore             创建 .dockerignore 文件"
    echo "  -b, --build              优化镜像构建"
    echo "  -c, --compose            创建 Docker Compose 配置"
    echo "  -s, --security           创建安全配置"
    echo "  -a, --all                执行所有优化"
    echo "  --help                   显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY          Docker 镜像仓库地址"
    echo "  DOCKER_NAMESPACE         Docker 命名空间"
    echo "  VERSION                  镜像版本标签"
    echo ""
    echo "示例:"
    echo "  $0 -a                    # 执行所有优化"
    echo "  $0 -d -i                 # 只优化 Dockerfile 和创建 .dockerignore"
    echo "  $0 -b                    # 只优化镜像构建"
}

# 主函数
main() {
    local optimize_dockerfiles=false
    local create_ignore=false
    local optimize_build=false
    local create_compose=false
    local create_security=false
    local all=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dockerfiles)
                optimize_dockerfiles=true
                shift
                ;;
            -i|--ignore)
                create_ignore=true
                shift
                ;;
            -b|--build)
                optimize_build=true
                shift
                ;;
            -c|--compose)
                create_compose=true
                shift
                ;;
            -s|--security)
                create_security=true
                shift
                ;;
            -a|--all)
                all=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定选项，默认执行所有优化
    if [[ "$all" == "true" ]] || [[ "$optimize_dockerfiles" == "false" && "$create_ignore" == "false" && "$optimize_build" == "false" && "$create_compose" == "false" && "$create_security" == "false" ]]; then
        optimize_dockerfiles=true
        create_ignore=true
        optimize_build=true
        create_compose=true
        create_security=true
    fi
    
    print_info "开始 Docker 容器化优化..."
    
    if [[ "$optimize_dockerfiles" == "true" ]]; then
        optimize_dockerfiles
    fi
    
    if [[ "$create_ignore" == "true" ]]; then
        create_dockerignore
    fi
    
    if [[ "$optimize_build" == "true" ]]; then
        optimize_image_build
    fi
    
    if [[ "$create_compose" == "true" ]]; then
        create_docker_compose
    fi
    
    if [[ "$create_security" == "true" ]]; then
        create_security_config
    fi
    
    print_success "Docker 容器化优化完成"
    
    echo ""
    print_info "优化总结:"
    echo "- ✅ Dockerfile 多阶段构建优化"
    echo "- ✅ .dockerignore 文件创建"
    echo "- ✅ 镜像构建脚本优化"
    echo "- ✅ Docker Compose 生产配置"
    echo "- ✅ 容器安全配置"
    echo ""
    print_info "下一步操作:"
    echo "1. 运行 scripts/build-images.sh 构建优化后的镜像"
    echo "2. 使用 docker-compose -f docker-compose.prod.yml up 部署"
    echo "3. 应用安全配置进行生产部署"
}

# 脚本入口
main "$@"
