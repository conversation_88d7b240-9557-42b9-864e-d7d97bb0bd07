version: '3.8'

# PaaS 平台灵活配置 Docker Compose
# 支持通过 profiles 控制启动哪些服务

services:
  # PostgreSQL 数据库 (可选，使用 database profile)
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    environment:
      POSTGRES_DB: paas_platform
      POSTGRES_USER: paas
      POSTGRES_PASSWORD: paas123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - paas-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas -d paas_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
    profiles:
      - database
      - standard
      - full

  # Redis 缓存 (可选，使用 redis profile)
  redis:
    image: redis:7-alpine
    container_name: paas-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - paas-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    profiles:
      - redis
      - standard
      - full

  # Gitea 代码仓库 (可选，使用 git profile)
  gitea:
    image: gitea/gitea:1.20
    container_name: paas-gitea
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=gitea
      - GITEA__database__USER=paas
      - GITEA__database__PASSWD=paas123
    restart: always
    ports:
      - "3000:3000"
      - "2222:22"
    volumes:
      - gitea_data:/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - git
      - full

  # Docker Registry (可选，使用 registry profile)
  registry:
    image: registry:2
    container_name: paas-registry
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - paas-network
    environment:
      REGISTRY_STORAGE_DELETE_ENABLED: "true"
    profiles:
      - registry
      - full

  # API Gateway (核心服务，始终启动)
  api-gateway:
    build:
      context: .
      dockerfile: deployments/docker/api-gateway.Dockerfile
    container_name: paas-api-gateway
    ports:
      - "8080:8080"
    environment:
      - SERVER_PORT=8080
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CONFIG_FILE=${CONFIG_FILE:-configs/app.minimal.yaml}
      # 数据库配置 (根据是否启用数据库服务动态配置)
      - DATABASE_ENABLED=${DATABASE_ENABLED:-false}
      - DATABASE_DRIVER=${DATABASE_DRIVER:-sqlite}
      - DATABASE_DSN=${DATABASE_DSN:-./data/api-gateway.db}
      # Redis 配置 (根据是否启用 Redis 服务动态配置)
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      # Docker 配置
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_REGISTRY_ENABLED=${DOCKER_REGISTRY_ENABLED:-false}
      - DOCKER_REGISTRY_URL=${DOCKER_REGISTRY_URL:-localhost:5000}
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - paas-network
    restart: unless-stopped

  # User Service (核心服务，始终启动)
  user-service:
    build:
      context: .
      dockerfile: deployments/docker/user-service.Dockerfile
    container_name: paas-user-service
    ports:
      - "8083:8083"
    environment:
      - SERVER_PORT=8083
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CONFIG_FILE=${CONFIG_FILE:-configs/app.minimal.yaml}
      - DATABASE_ENABLED=${DATABASE_ENABLED:-false}
      - DATABASE_DRIVER=${DATABASE_DRIVER:-sqlite}
      - DATABASE_DSN=${DATABASE_DSN:-./data/user-service.db}
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      - JWT_SECRET=${JWT_SECRET:-dev-secret-key}
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
    networks:
      - paas-network
    restart: unless-stopped

  # App Manager (核心服务，始终启动)
  app-manager:
    build:
      context: .
      dockerfile: deployments/docker/app-manager.Dockerfile
    container_name: paas-app-manager
    ports:
      - "8081:8081"
    environment:
      - SERVER_PORT=8081
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CONFIG_FILE=${CONFIG_FILE:-configs/app.minimal.yaml}
      - DATABASE_ENABLED=${DATABASE_ENABLED:-false}
      - DATABASE_DRIVER=${DATABASE_DRIVER:-sqlite}
      - DATABASE_DSN=${DATABASE_DSN:-./data/app-manager.db}
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - paas-network
    restart: unless-stopped

  # CI/CD Service (可选，使用 cicd profile)
  cicd-service:
    build:
      context: .
      dockerfile: deployments/docker/cicd-service.Dockerfile
    container_name: paas-cicd-service
    ports:
      - "8082:8082"
    environment:
      - SERVER_PORT=8082
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CONFIG_FILE=${CONFIG_FILE:-configs/app.minimal.yaml}
      - DATABASE_ENABLED=${DATABASE_ENABLED:-false}
      - DATABASE_DRIVER=${DATABASE_DRIVER:-sqlite}
      - DATABASE_DSN=${DATABASE_DSN:-./data/cicd-service.db}
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_REGISTRY_ENABLED=${DOCKER_REGISTRY_ENABLED:-false}
      - DOCKER_REGISTRY_URL=${DOCKER_REGISTRY_URL:-localhost:5000}
      - GITEA_URL=${GITEA_URL:-http://gitea:3000}
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - paas-network
    restart: unless-stopped
    profiles:
      - cicd
      - standard
      - full

  # Config Service (可选，使用 config profile)
  config-service:
    build:
      context: .
      dockerfile: deployments/docker/config-service.Dockerfile
    container_name: paas-config-service
    ports:
      - "8084:8084"
    environment:
      - SERVER_PORT=8084
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CONFIG_FILE=${CONFIG_FILE:-configs/app.minimal.yaml}
      - DATABASE_ENABLED=${DATABASE_ENABLED:-false}
      - DATABASE_DRIVER=${DATABASE_DRIVER:-sqlite}
      - DATABASE_DSN=${DATABASE_DSN:-./data/config-service.db}
      - REDIS_ENABLED=${REDIS_ENABLED:-false}
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
    networks:
      - paas-network
    restart: unless-stopped
    profiles:
      - config
      - standard
      - full

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  gitea_data:
    driver: local
  registry_data:
    driver: local

# 网络
networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
