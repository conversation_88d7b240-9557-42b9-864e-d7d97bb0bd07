# PaaS 平台日志聚合系统 Docker Compose 配置
# 包含 Elasticsearch, Logstash, Kibana, Filebeat 完整日志栈

version: '3.8'

services:
  # 🔍 Elasticsearch - 日志存储和搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: paas-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      # 集群配置
      - cluster.name=paas-logs-cluster
      - node.name=paas-elasticsearch-node
      - discovery.type=single-node
      
      # 内存配置
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
      
      # 安全配置 (开发环境关闭)
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      
      # 监控配置
      - xpack.monitoring.collection.enabled=true
      
      # 网络配置
      - network.host=0.0.0.0
      - http.port=9200
      - transport.port=9300
    
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
      - ./configs/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
      - ./logs:/var/log/paas:ro
    
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    
    networks:
      - paas-logging
    
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 🔄 Logstash - 日志处理和转换
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: paas-logstash
    restart: unless-stopped
    ports:
      - "5044:5044"  # Beats input
      - "5000:5000"  # TCP input
      - "9600:9600"  # HTTP API
    
    environment:
      - "LS_JAVA_OPTS=-Xms512m -Xmx512m"
      - PIPELINE_WORKERS=2
      - LOG_LEVEL=info
    
    volumes:
      - ./configs/logstash/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ./configs/logstash/pipelines.yml:/usr/share/logstash/config/pipelines.yml:ro
      - ./configs/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./logs:/var/log/paas:ro
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 📊 Kibana - 日志可视化和分析
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: paas-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    
    environment:
      # Elasticsearch 连接
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=
      
      # 服务器配置
      - SERVER_NAME=paas-kibana
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=5601
      - SERVER_BASEPATH=""
      
      # 安全配置 (开发环境)
      - XPACK_SECURITY_ENABLED=false
      - XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY=a7a6311933d3503b89bc2dbc36572c33a6c10925682e591bffcab6911c06786d
      
      # 监控配置
      - XPACK_MONITORING_ENABLED=true
      - XPACK_MONITORING_KIBANA_COLLECTION_ENABLED=false
      
      # 日志配置
      - LOGGING_ROOT_LEVEL=warn
    
    volumes:
      - ./configs/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
      - kibana-data:/usr/share/kibana/data
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # 📁 Filebeat - 日志文件收集器
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: paas-filebeat
    restart: unless-stopped
    user: root
    
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
      - LOGSTASH_HOSTS=logstash:5044
    
    volumes:
      - ./configs/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/paas:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat-data:/usr/share/filebeat/data
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
      logstash:
        condition: service_healthy
    
    command: filebeat -e -strict.perms=false

  # 📊 Metricbeat - 系统指标收集器
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.8.0
    container_name: paas-metricbeat
    restart: unless-stopped
    user: root
    
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    
    volumes:
      - ./configs/metricbeat/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat-data:/usr/share/metricbeat/data
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
    
    command: metricbeat -e -strict.perms=false -system.hostfs=/hostfs

  # 🔍 APM Server - 应用性能监控数据收集
  apm-server:
    image: docker.elastic.co/apm/apm-server:8.8.0
    container_name: paas-apm-server
    restart: unless-stopped
    ports:
      - "8200:8200"
    
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    
    volumes:
      - ./configs/apm-server/apm-server.yml:/usr/share/apm-server/apm-server.yml:ro
      - apm-server-data:/usr/share/apm-server/data
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
    
    command: apm-server -e -strict.perms=false

  # 🔧 Curator - 索引生命周期管理
  curator:
    image: untergeek/curator:8.0.4
    container_name: paas-curator
    restart: "no"
    
    environment:
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
    
    volumes:
      - ./configs/curator/curator.yml:/usr/share/curator/curator.yml:ro
      - ./configs/curator/actions.yml:/usr/share/curator/actions.yml:ro
    
    networks:
      - paas-logging
    
    depends_on:
      elasticsearch:
        condition: service_healthy
    
    # 每天凌晨2点运行一次
    command: >
      sh -c "
        while true; do
          sleep 86400
          curator --config /usr/share/curator/curator.yml /usr/share/curator/actions.yml
        done
      "

  # 📈 Grafana Loki - 轻量级日志聚合 (可选)
  loki:
    image: grafana/loki:2.8.0
    container_name: paas-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    
    volumes:
      - ./configs/loki/loki.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    
    networks:
      - paas-logging
    
    command: -config.file=/etc/loki/local-config.yaml

  # 📊 Promtail - Loki 日志收集器
  promtail:
    image: grafana/promtail:2.8.0
    container_name: paas-promtail
    restart: unless-stopped
    
    volumes:
      - ./configs/promtail/promtail.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log/paas:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    
    networks:
      - paas-logging
    
    depends_on:
      - loki
    
    command: -config.file=/etc/promtail/config.yml

# 📊 数据卷
volumes:
  elasticsearch-data:
    driver: local
  kibana-data:
    driver: local
  filebeat-data:
    driver: local
  metricbeat-data:
    driver: local
  apm-server-data:
    driver: local
  loki-data:
    driver: local

# 🌐 网络
networks:
  paas-logging:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

---
# 📝 使用说明
# 
# 1. 启动日志系统：
#    docker-compose -f docker-compose.logging.yml up -d
# 
# 2. 访问地址：
#    - Elasticsearch: http://localhost:9200
#    - Kibana: http://localhost:5601
#    - APM Server: http://localhost:8200
#    - Loki: http://localhost:3100
# 
# 3. 停止日志系统：
#    docker-compose -f docker-compose.logging.yml down
# 
# 4. 清理数据：
#    docker-compose -f docker-compose.logging.yml down -v
# 
# 注意：
# - 首次启动需要等待 Elasticsearch 完全启动
# - Kibana 需要时间初始化索引模式
# - 建议至少分配 4GB 内存给 Elasticsearch
