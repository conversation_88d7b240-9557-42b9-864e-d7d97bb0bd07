# PaaS 平台最小化配置
# 适用于单机开发，不需要 Redis、监控等额外组件

# 应用基础配置
app:
  name: "paas-platform"
  version: "1.0.0"
  environment: "minimal"  # minimal, development, production
  
# 服务器配置
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 日志配置
logging:
  level: "info"           # debug, info, warn, error
  format: "text"          # text, json
  output: "stdout"        # stdout, stderr, file
  file_path: ""           # 空表示不写文件

# 数据库配置
database:
  enabled: true           # 数据库是必需的
  driver: "sqlite"        # sqlite, postgres, mysql
  dsn: "./data/paas.db"   # 数据源
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300s
  auto_migrate: true      # 自动迁移数据库表

# Redis 配置（可选）
redis:
  enabled: false          # 禁用 Redis
  addr: ""
  password: ""
  db: 0
  # 当禁用时，使用内存缓存作为替代

# 认证配置
auth:
  enabled: false          # 开发环境可以禁用认证
  jwt:
    secret: "dev-secret-key"
    expires_in: 24h
  
  # 开发模式用户（当认证禁用时使用）
  dev_user:
    id: "dev-user-001"
    username: "开发者"
    email: "dev@localhost"
    roles: ["admin"]

# Docker 配置
docker:
  enabled: true           # Docker 是必需的（用于应用部署）
  host: "unix:///var/run/docker.sock"
  api_version: "1.41"
  
  # 镜像仓库配置
  registry:
    enabled: false        # 禁用镜像仓库
    mode: "local"         # local, remote
    url: ""
    username: ""
    password: ""
  
  # 构建配置
  build:
    local_only: true      # 仅本地构建
    use_cache: true       # 使用构建缓存
    timeout: 600s         # 构建超时

# Git 配置（可选）
git:
  enabled: false          # 禁用 Git 集成
  providers: []           # 空数组表示不配置任何 Git 提供商

# 监控配置（可选）
monitoring:
  enabled: false          # 禁用监控
  metrics:
    enabled: false
    port: 0
  health_check:
    enabled: true         # 保留健康检查
    interval: 30s

# 通知配置（可选）
notification:
  enabled: false          # 禁用通知

# 存储配置
storage:
  type: "local"           # local, s3, minio
  local:
    base_path: "./data/storage"
    max_size: "1GB"

# 功能开关
features:
  # 核心功能（始终启用）
  app_management: true
  user_management: true
  config_management: true
  
  # 可选功能（最小化配置中禁用）
  cicd: false             # 禁用 CI/CD
  monitoring: false       # 禁用监控
  logging_aggregation: false  # 禁用日志聚合
  auto_scaling: false     # 禁用自动扩缩容
  load_balancing: false   # 禁用负载均衡
  backup: false           # 禁用备份

# 资源限制
resources:
  # 内存缓存限制（当 Redis 禁用时使用）
  memory_cache:
    max_size: "100MB"
    ttl: "1h"
  
  # 文件上传限制
  upload:
    max_file_size: "10MB"
    max_total_size: "100MB"

# 安全配置
security:
  # API 限流（简化版）
  rate_limit:
    enabled: false        # 开发环境禁用限流
    requests_per_minute: 0
  
  # CORS 配置
  cors:
    enabled: true
    allowed_origins: ["*"]  # 开发环境允许所有来源
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]

# 开发模式配置
development:
  enabled: true           # 启用开发模式
  hot_reload: false       # Go 应用不支持热重载
  debug_mode: true        # 启用调试模式
  mock_external_services: true  # 模拟外部服务
  
  # 开发工具
  tools:
    swagger: true         # 启用 Swagger 文档
    pprof: true          # 启用性能分析
    admin_ui: true       # 启用管理界面
