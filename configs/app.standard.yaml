# PaaS 平台标准配置
# 适用于完整开发环境，包含 Redis、监控等组件

# 应用基础配置
app:
  name: "paas-platform"
  version: "1.0.0"
  environment: "development"  # minimal, development, production
  
# 服务器配置
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 日志配置
logging:
  level: "debug"          # 开发环境使用 debug 级别
  format: "text"          # 开发环境使用文本格式便于阅读
  output: "stdout"
  file_path: "./logs/app.log"  # 同时写入文件

# 数据库配置
database:
  enabled: true
  driver: "postgres"      # 标准环境使用 PostgreSQL
  dsn: "host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 25
  max_idle_conns: 10
  conn_max_lifetime: 300s
  auto_migrate: true

# Redis 配置（启用）
redis:
  enabled: true           # 启用 Redis
  addr: "redis:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

# 认证配置
auth:
  enabled: true           # 启用认证
  jwt:
    secret: "your-jwt-secret-key"
    expires_in: 24h
    refresh_expires_in: 168h
  
  # 开发模式用户
  dev_user:
    id: "dev-user-001"
    username: "开发者"
    email: "dev@localhost"
    roles: ["admin", "developer"]

# Docker 配置
docker:
  enabled: true
  host: "unix:///var/run/docker.sock"
  api_version: "1.41"
  
  # 镜像仓库配置
  registry:
    enabled: true         # 启用本地镜像仓库
    mode: "local"         # 本地模式
    url: "localhost:5000"
    username: ""
    password: ""
    insecure: true        # 允许不安全连接
  
  # 构建配置
  build:
    local_only: false     # 允许推送到仓库
    use_cache: true
    timeout: 1800s        # 30分钟超时

# Git 配置
git:
  enabled: true           # 启用 Git 集成
  providers:
    - name: "gitea"
      type: "gitea"
      url: "http://gitea:3000"
      api_url: "http://gitea:3000/api/v1"
      webhook_secret: "webhook-secret"

# 监控配置
monitoring:
  enabled: true           # 启用监控
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
  health_check:
    enabled: true
    interval: 30s
    timeout: 5s

# 通知配置
notification:
  enabled: true           # 启用通知
  channels:
    - type: "webhook"
      url: "http://localhost:3001/webhook"
      events: ["build_success", "build_failed", "deploy_success", "deploy_failed"]

# 存储配置
storage:
  type: "local"
  local:
    base_path: "./data/storage"
    max_size: "10GB"

# 功能开关
features:
  # 核心功能
  app_management: true
  user_management: true
  config_management: true
  
  # 扩展功能
  cicd: true              # 启用 CI/CD
  monitoring: true        # 启用监控
  logging_aggregation: true   # 启用日志聚合
  auto_scaling: false     # 开发环境暂不启用
  load_balancing: true    # 启用负载均衡
  backup: true            # 启用备份

# 资源限制
resources:
  # Redis 缓存配置
  redis_cache:
    default_ttl: "1h"
    max_memory: "256MB"
  
  # 文件上传限制
  upload:
    max_file_size: "100MB"
    max_total_size: "1GB"

# 安全配置
security:
  # API 限流
  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst: 20
  
  # CORS 配置
  cors:
    enabled: true
    allowed_origins: ["http://localhost:3000", "http://localhost:8080"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization"]

# 开发模式配置
development:
  enabled: true
  hot_reload: false
  debug_mode: true
  mock_external_services: false  # 使用真实的外部服务
  
  # 开发工具
  tools:
    swagger: true
    pprof: true
    admin_ui: true
    
# CI/CD 配置
cicd:
  enabled: true
  build:
    workspace_dir: "/tmp/builds"
    max_concurrent_builds: 5
    default_timeout: 1800s
  
  deploy:
    strategies: ["rolling", "blue_green"]
    default_strategy: "rolling"
    
# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"   # 每天凌晨2点
  retention_days: 7
  targets:
    - type: "database"
      enabled: true
    - type: "storage"
      enabled: true
