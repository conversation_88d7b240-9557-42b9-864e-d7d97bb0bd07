# CI/CD 服务本地开发配置
# 专为单台服务器开发优化，无需镜像仓库

# 服务基本配置
server:
  port: 8082                              # 服务端口
  host: "0.0.0.0"                        # 监听地址
  read_timeout: 30s                       # 读取超时
  write_timeout: 30s                      # 写入超时
  idle_timeout: 60s                       # 空闲超时
  max_header_bytes: 1048576               # 最大请求头大小

# 日志配置
logging:
  level: "debug"                          # 日志级别：debug, info, warn, error
  format: "json"                          # 日志格式：json, text
  output: "stdout"                        # 输出目标：stdout, stderr, file
  file_path: "/app/logs/cicd-service.log" # 日志文件路径
  max_size: 100                           # 日志文件最大大小(MB)
  max_backups: 5                          # 保留的日志文件数量
  max_age: 30                             # 日志文件保留天数
  compress: true                          # 是否压缩旧日志文件

# 数据库配置
database:
  driver: "postgres"                      # 数据库驱动
  host: "postgres"                        # 数据库主机
  port: 5432                              # 数据库端口
  username: "paas"                        # 数据库用户名
  password: "paas123"                     # 数据库密码
  database: "paas_platform"               # 数据库名称
  sslmode: "disable"                      # SSL模式
  timezone: "Asia/Shanghai"               # 时区
  max_open_conns: 25                      # 最大打开连接数
  max_idle_conns: 10                      # 最大空闲连接数
  conn_max_lifetime: 300s                 # 连接最大生存时间

# Redis 配置
redis:
  addr: "redis:6379"                      # Redis 地址
  password: ""                            # Redis 密码
  db: 0                                   # Redis 数据库编号
  pool_size: 10                           # 连接池大小
  min_idle_conns: 5                       # 最小空闲连接数
  dial_timeout: 5s                        # 连接超时
  read_timeout: 3s                        # 读取超时
  write_timeout: 3s                       # 写入超时
  pool_timeout: 4s                        # 连接池超时

# JWT 配置
jwt:
  secret: "your-jwt-secret-key"           # JWT 密钥
  expires_in: 24h                         # Token 过期时间
  refresh_expires_in: 168h                # 刷新 Token 过期时间
  issuer: "paas-platform"                 # 签发者
  audience: "paas-users"                  # 受众

# Git 配置
git:
  # Gitea 配置
  gitea:
    url: "http://gitea:3000"              # Gitea 服务地址
    admin_token: ""                       # 管理员 Token
    webhook_secret: "webhook-secret"      # Webhook 密钥
    
  # Git 操作配置
  clone_timeout: 300s                     # 克隆超时时间
  fetch_timeout: 60s                      # 拉取超时时间
  push_timeout: 120s                      # 推送超时时间
  max_repo_size: 1GB                      # 最大仓库大小

# Docker 配置 - 本地开发模式
docker:
  # Docker 守护进程配置
  host: "unix:///var/run/docker.sock"    # Docker 守护进程地址
  api_version: "1.41"                     # Docker API 版本
  
  # 镜像仓库配置 - 本地开发模式
  registry:
    mode: "local"                         # 本地模式，不使用镜像仓库
    default_url: ""                       # 不使用镜像仓库
    username: ""                          # 用户名
    password: ""                          # 密码
    insecure: false                       # 不需要配置
  
  # 构建配置 - 本地开发优化
  build:
    context_size_limit: 500MB             # 构建上下文大小限制
    build_timeout: 900s                   # 镜像构建超时时间
    no_cache: false                       # 启用缓存加速构建
    pull_always: false                    # 不总是拉取基础镜像
    local_only: true                      # 仅本地构建，不推送
    
  # 容器运行配置
  container:
    default_network: "paas-network"       # 默认网络
    auto_remove: false                    # 开发环境保留容器便于调试
    restart_policy: "unless-stopped"     # 重启策略
    log_driver: "json-file"               # 日志驱动
    log_opts:
      max-size: "10m"                     # 日志文件最大大小
      max-file: "3"                       # 日志文件数量

# CI/CD 流水线配置
pipeline:
  # 构建配置
  build:
    default_timeout: 1800s                # 默认构建超时
    max_parallel_jobs: 3                  # 最大并行任务数
    workspace_dir: "/tmp/cicd-workspace"  # 工作空间目录
    artifact_retention: 7                 # 构建产物保留天数
    
  # 部署配置 - 本地开发模式
  deploy:
    mode: "local"                         # 本地部署模式
    target_network: "paas-network"        # 目标网络
    health_check_timeout: 60s             # 健康检查超时
    rollback_enabled: true                # 启用回滚
    
  # 通知配置
  notification:
    enabled: false                        # 开发环境禁用通知
    webhook_url: ""                       # Webhook 地址
    channels: []                          # 通知渠道

# 监控配置
monitoring:
  enabled: true                           # 启用监控
  metrics_port: 9090                      # 指标端口
  health_check_interval: 30s              # 健康检查间隔
  
  # Prometheus 配置
  prometheus:
    enabled: true                         # 启用 Prometheus 指标
    path: "/metrics"                      # 指标路径
    
# 安全配置
security:
  # API 安全
  api:
    rate_limit: 100                       # 速率限制（每分钟请求数）
    cors_enabled: true                    # 启用 CORS
    cors_origins: ["*"]                   # 允许的源（开发环境允许所有）
    
  # 认证配置
  auth:
    required: true                        # 需要认证
    skip_paths: ["/health", "/metrics"]   # 跳过认证的路径

# 开发模式特定配置
development:
  enabled: true                           # 启用开发模式
  hot_reload: true                        # 热重载
  debug_mode: true                        # 调试模式
  mock_external_services: false          # 不模拟外部服务
  
  # 本地构建优化
  build_optimization:
    use_cache: true                       # 使用构建缓存
    parallel_build: true                  # 并行构建
    skip_tests: false                     # 不跳过测试
    
  # 调试配置
  debug:
    enable_pprof: true                    # 启用性能分析
    pprof_port: 6060                      # 性能分析端口
    log_sql: true                         # 记录 SQL 查询
    log_requests: true                    # 记录 HTTP 请求
