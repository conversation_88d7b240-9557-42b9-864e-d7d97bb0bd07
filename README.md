# 🚀 企业级 PaaS 平台

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org)
[![Vue Version](https://img.shields.io/badge/Vue.js-3.4+-green.svg)](https://vuejs.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

一个基于 Go 语言开发的企业级 Platform as a Service (PaaS) 平台，采用微服务架构设计，提供应用部署、CI/CD、用户认证、配置管理等完整的云原生解决方案。

## 📋 目录

- [功能特性](#-功能特性)
- [技术栈](#-技术栈)
- [快速开始](#-快速开始)
- [项目结构](#-项目结构)
- [开发指南](#-开发指南)
- [部署指南](#-部署指南)
- [配置说明](#-配置说明)
- [监控运维](#-监控运维)
- [贡献指南](#-贡献指南)

## ✨ 功能特性

### 🏗️ 核心功能
- **🚀 应用管理**: 支持 Node.js、Python、Go、Java 等多种运行时的应用部署和管理
- **🔄 CI/CD 流水线**: 完整的持续集成和持续部署，支持滚动更新、蓝绿部署、金丝雀部署
- **👥 多租户架构**: 完整的用户认证和基于角色的访问控制 (RBAC)
- **🔧 配置管理**: 分层配置继承（全局 > 平台 > 租户 > 应用），支持密钥管理和动态更新
- **📜 脚本执行**: 支持多种脚本语言的安全执行环境
- **⚡ FaaS 无服务器**: Function as a Service 函数执行器，支持多运行时、容器池管理、资源限制
- **🗄️ 数据存储**: 支持 PostgreSQL、SQLite 等多种数据库，自动化数据迁移

### 📊 监控与运维
- **📈 性能监控**: 基于 Prometheus + Grafana 的完整监控体系
- **📝 日志聚合**: ELK (Elasticsearch + Logstash + Kibana) 日志栈
- **🔔 智能告警**: AlertManager 告警路由和多渠道通知（邮件、Slack、钉钉、企业微信）
- **🏥 健康检查**: 应用和服务的自动健康检查和故障恢复
- **📊 指标收集**: 应用性能指标 (APM) 和业务指标收集

### 🔒 安全与合规
- **🔐 身份认证**: JWT 令牌认证，支持多种身份提供商 (IDP) 集成
- **🛡️ 权限控制**: 细粒度的 RBAC 权限管理
- **🔑 密钥管理**: AES-256-GCM 加密的密钥存储和管理
- **🔍 安全扫描**: 代码安全扫描和漏洞检测
- **📋 审计日志**: 完整的操作审计和合规性记录

## 🏗️ 技术栈

### 后端技术
- **编程语言**: Go 1.24+ (最新稳定版本)
- **Web 框架**: Gin Web Framework
- **ORM 框架**: GORM v2 (支持多数据库)
- **认证授权**: JWT + RBAC
- **API 文档**: Swagger/OpenAPI 3.0
- **消息队列**: Redis Streams
- **缓存系统**: Redis

### 前端技术
- **框架**: Vue.js 3.4+ + TypeScript
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **图表库**: ECharts
- **HTTP 客户端**: Axios

### 数据存储
- **关系数据库**: PostgreSQL 15+ (生产推荐) / SQLite (开发环境)
- **缓存数据库**: Redis 7+
- **文件存储**: 本地文件系统 / 对象存储 (S3 兼容)
- **数据迁移**: 自动化数据库迁移管理

### 容器化与部署
- **容器化**: Docker + Docker Compose
- **编排工具**: Kubernetes (可选)
- **包管理**: Helm Charts
- **代码仓库**: Gitea (内置) / GitHub / GitLab
- **镜像仓库**: Docker Registry (内置)

### 监控与运维
- **指标监控**: Prometheus + Grafana
- **日志聚合**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **告警通知**: AlertManager + 多渠道通知
- **链路追踪**: Jaeger (可选)
- **性能分析**: pprof + 自定义性能监控

## 🏛️ 系统架构

### 微服务架构图

```mermaid
graph TB
    subgraph "前端层"
        WEB[Web 管理界面<br/>Vue.js 3 + TypeScript]
        MOBILE[移动端<br/>响应式设计]
    end

    subgraph "网关层"
        GATEWAY[API Gateway<br/>路由 + 认证 + 限流]
        LB[负载均衡器<br/>Nginx/HAProxy]
    end

    subgraph "业务服务层"
        APP[应用管理服务<br/>App Manager]
        CICD[CI/CD 服务<br/>流水线管理]
        USER[用户服务<br/>认证授权]
        CONFIG[配置服务<br/>配置管理]
        SCRIPT[脚本服务<br/>脚本执行]
        FAAS[FaaS 服务<br/>函数执行器]
        MONITOR[监控服务<br/>指标收集]
    end

    subgraph "数据层"
        PG[(PostgreSQL<br/>主数据库)]
        REDIS[(Redis<br/>缓存+队列)]
        FILES[文件存储<br/>本地/对象存储]
    end

    subgraph "基础设施层"
        DOCKER[Docker Engine<br/>容器运行时]
        GITEA[Gitea<br/>代码仓库]
        REGISTRY[Docker Registry<br/>镜像仓库]
    end

    subgraph "监控层"
        PROM[Prometheus<br/>指标收集]
        GRAFANA[Grafana<br/>可视化面板]
        ELK[ELK Stack<br/>日志聚合]
        ALERT[AlertManager<br/>告警管理]
    end

    WEB --> GATEWAY
    MOBILE --> GATEWAY
    GATEWAY --> LB
    LB --> APP
    LB --> CICD
    LB --> USER
    LB --> CONFIG
    LB --> SCRIPT
    LB --> FAAS
    LB --> MONITOR

    APP --> PG
    CICD --> PG
    USER --> PG
    CONFIG --> PG
    SCRIPT --> PG
    FAAS --> PG
    MONITOR --> PG

    APP --> REDIS
    CICD --> REDIS
    USER --> REDIS
    CONFIG --> REDIS
    SCRIPT --> REDIS
    FAAS --> REDIS

    APP --> FILES
    CICD --> FILES
    SCRIPT --> FILES
    FAAS --> FILES

    CICD --> DOCKER
    FAAS --> DOCKER
    CICD --> GITEA
    CICD --> REGISTRY

    MONITOR --> PROM
    PROM --> GRAFANA
    MONITOR --> ELK
    PROM --> ALERT
```

## 🚀 快速开始

### 📋 环境要求

在开始之前，请确保您的系统满足以下要求：

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Go** | 1.24+ | 后端服务开发语言 |
| **Node.js** | 18.0+ | 前端构建环境 |
| **Docker** | 20.0+ | 容器化部署 |
| **Docker Compose** | 2.0+ | 多容器编排 |
| **Git** | 2.30+ | 代码版本控制 |

**可选组件**：
- **PostgreSQL** 15+ (生产环境推荐，开发环境可使用 SQLite)
- **Redis** 7+ (可使用 Docker 启动)

### ⚡ 一键启动 (推荐)

```bash
# 1. 克隆项目
git clone https://github.com/your-org/paas-platform.git
cd paas-platform

# 2. 启动开发环境 (包含所有依赖服务)
./scripts/start-dev-mode.sh
```

启动成功后，您可以访问：
- 🌐 **Web 管理界面**: http://localhost:3000
- 📱 **应用管理服务**: http://localhost:8081
- 📜 **脚本执行服务**: http://localhost:8084
- 🚀 **CI/CD 服务**: http://localhost:8082
- ⚙️ **配置服务**: http://localhost:8083
- ⚡ **FaaS 服务**: http://localhost:8087
- 📚 **API 文档**: http://localhost:8081/swagger

### 🔧 手动安装步骤

如果您希望手动控制安装过程：

#### 1. 准备环境

```bash
# 检查 Go 版本
go version  # 应该 >= 1.24

# 检查 Node.js 版本
node --version  # 应该 >= 18.0

# 检查 Docker 版本
docker --version  # 应该 >= 20.0
docker-compose --version  # 应该 >= 2.0
```

#### 2. 安装后端依赖

```bash
# 下载 Go 模块依赖
go mod tidy

# 验证依赖
go mod verify
```

#### 3. 安装前端依赖

```bash
cd web
npm install
# 或使用 yarn
yarn install
```

#### 4. 启动基础服务

```bash
# 启动数据库和缓存服务
docker-compose -f docker-compose.dev.yml up -d postgres redis gitea registry

# 等待服务启动完成
./scripts/health-check.sh
```

#### 5. 启动后端服务

```bash
# 构建所有服务
make build

# 启动应用管理服务
./bin/app-manager --config=configs/app-manager.dev.yaml &

# 启动脚本执行服务
./bin/script-service --config=configs/script-service.dev.yaml &

# 启动 CI/CD 服务
./bin/cicd-service --config=configs/cicd-service.dev.yaml &

# 启动配置服务
./bin/config-service --config=configs/config-service.dev.yaml &
```

#### 6. 启动前端服务

```bash
cd web
npm run dev
```

### 🔍 验证安装

```bash
# 检查所有服务状态
./scripts/health-check.sh

# 运行基础测试
./scripts/run-tests.sh

# 查看服务日志
tail -f logs/*.log
```

## 📁 项目结构

```
paas-platform/
├── 📂 cmd/                          # 🚀 各微服务的入口文件
│   ├── api-gateway/                 #   API 网关服务
│   ├── app-manager/                 #   应用管理服务
│   ├── cicd-service/                #   CI/CD 流水线服务
│   ├── user-service/                #   用户认证服务
│   ├── config-service/              #   配置管理服务
│   ├── script-service/              #   脚本执行服务
│   └── monitor-service/             #   监控服务
├── 📂 internal/                     # 🔒 内部业务逻辑包
│   ├── app/                         #   应用管理模块
│   │   ├── models.go               #     数据模型定义
│   │   ├── service.go              #     业务逻辑服务
│   │   ├── handler.go              #     HTTP 处理器
│   │   └── dto.go                  #     数据传输对象
│   ├── auth/                        #   认证授权模块
│   ├── cicd/                        #   CI/CD 流水线模块
│   ├── config/                      #   配置管理模块
│   ├── script/                      #   脚本执行模块
│   ├── monitoring/                  #   监控模块
│   ├── database/                    #   数据库抽象层
│   ├── common/                      #   公共组件
│   └── testing/                     #   测试工具
├── 📂 pkg/                          # 📦 可复用的公共库
│   ├── logger/                      #   结构化日志库
│   ├── errors/                      #   错误处理库
│   ├── middleware/                  #   HTTP 中间件
│   ├── validator/                   #   数据验证库
│   ├── cache/                       #   缓存抽象层
│   ├── utils/                       #   工具函数库
│   └── websocket/                   #   WebSocket 支持
├── 📂 api/                          # 📋 API 定义和文档
│   ├── openapi/                     #   OpenAPI/Swagger 规范
│   └── proto/                       #   gRPC 协议定义
├── 📂 web/                          # 🌐 前端 Vue.js 应用
│   ├── src/                         #   源代码
│   │   ├── views/                   #     页面组件
│   │   ├── components/              #     可复用组件
│   │   ├── stores/                  #     Pinia 状态管理
│   │   ├── api/                     #     API 客户端
│   │   └── utils/                   #     工具函数
│   ├── public/                      #   静态资源
│   ├── package.json                 #   依赖配置
│   └── vite.config.ts              #   构建配置
├── 📂 configs/                      # ⚙️ 配置文件
│   ├── app-manager.yaml            #   应用管理服务配置
│   ├── cicd-service.yaml           #   CI/CD 服务配置
│   ├── user-service.yaml           #   用户服务配置
│   ├── *.dev.yaml                  #   开发环境配置
│   └── prometheus.yml              #   监控配置
├── 📂 deployments/                  # 🚢 部署配置
│   ├── docker/                      #   Docker 镜像配置
│   │   ├── Dockerfile.go-service   #     Go 服务镜像
│   │   └── Dockerfile.frontend     #     前端镜像
│   ├── k8s/                         #   Kubernetes 配置
│   ├── helm/                        #   Helm Charts
│   └── docker-compose.*.yml        #   Docker Compose 配置
├── 📂 scripts/                      # 🔧 运维和开发脚本
│   ├── start-dev-mode.sh           #   开发环境启动脚本
│   ├── deploy.sh                   #   部署脚本
│   ├── backup-restore.sh           #   备份恢复脚本
│   ├── health-check.sh             #   健康检查脚本
│   └── run-tests.sh                #   测试运行脚本
├── 📂 monitoring/                   # 📊 监控配置
│   ├── prometheus/                  #   Prometheus 配置
│   ├── grafana/                     #   Grafana 仪表板
│   └── alertmanager/               #   告警配置
├── 📂 docs/                         # 📚 项目文档
│   ├── api/                         #   API 文档
│   ├── architecture.md             #   架构设计文档
│   ├── deployment-guide.md         #   部署指南
│   └── development-guide.md        #   开发指南
├── 📂 tests/                        # 🧪 测试代码
│   ├── unit/                        #   单元测试
│   ├── integration/                 #   集成测试
│   ├── e2e/                         #   端到端测试
│   └── performance/                 #   性能测试
├── 📂 examples/                     # 💡 示例应用
│   ├── nodejs-app/                  #   Node.js 示例
│   ├── python-app/                  #   Python 示例
│   └── go-app/                      #   Go 示例
├── 📄 go.mod                        # Go 模块定义
├── 📄 go.sum                        # Go 依赖锁定
├── 📄 Makefile                      # 构建脚本
├── 📄 docker-compose.dev.yml       # 开发环境编排
└── 📄 README.md                     # 项目说明文档
```

### 🔍 关键目录说明

| 目录 | 用途 | 重要文件 |
|------|------|----------|
| `cmd/` | 各微服务的 main 函数入口 | `main.go` 文件 |
| `internal/` | 业务逻辑代码，不对外暴露 | `models.go`, `service.go`, `handler.go` |
| `pkg/` | 可复用的公共库 | 中间件、工具函数、错误处理 |
| `web/` | Vue.js 前端应用 | `src/`, `package.json` |
| `configs/` | 各环境的配置文件 | `*.yaml`, `*.yml` |
| `deployments/` | 部署相关配置 | `Dockerfile`, `k8s/`, `helm/` |
| `scripts/` | 自动化脚本 | `start-dev-mode.sh`, `deploy.sh` |
| `monitoring/` | 监控配置 | Prometheus, Grafana 配置 |

## 🛠️ 开发指南

### 📝 代码规范

#### Go 后端代码规范
```bash
# 代码格式化
gofmt -w .
go mod tidy

# 代码检查
golangci-lint run

# 测试覆盖率
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

**编码规范**：
- 遵循 [Go 官方代码规范](https://golang.org/doc/effective_go.html)
- 使用 `gofmt` 和 `goimports` 格式化代码
- 函数和方法添加详细的中文注释
- 所有公开的函数必须有文档注释
- 错误处理使用 `pkg/errors` 包装
- 单元测试覆盖率要求 ≥ 80%

#### 前端代码规范
```bash
# 代码格式化
npm run format

# 代码检查
npm run lint

# 类型检查
npm run type-check
```

**编码规范**：
- 使用 TypeScript 进行类型安全开发
- 遵循 Vue.js 3 Composition API 最佳实践
- 使用 ESLint + Prettier 进行代码格式化
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 🏗️ 开发环境设置

#### 1. IDE 配置推荐

**VS Code 扩展**：
```json
{
  "recommendations": [
    "golang.go",
    "vue.volar",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode"
  ]
}
```

**GoLand/WebStorm 配置**：
- 启用 Go Modules 支持
- 配置 gofmt 和 goimports
- 启用 Vue.js 插件

#### 2. 环境变量配置

创建 `.env.local` 文件：
```bash
# 数据库配置
DB_DRIVER=postgres
DB_DSN=host=localhost user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable

# Redis 配置
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE_HOURS=24

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=json

# 开发模式配置
PAAS_DEV_MODE=true
PAAS_AUTH_ENABLED=false
```

### 🔧 本地开发流程

#### 1. 启动开发环境

```bash
# 方式一：一键启动（推荐）
./scripts/start-dev-mode.sh

# 方式二：分步启动
# 启动基础服务
docker-compose -f docker-compose.dev.yml up -d postgres redis

# 启动后端服务
make dev-backend

# 启动前端服务
make dev-frontend
```

#### 2. 开发调试

```bash
# 查看服务日志
tail -f logs/app-manager.log
tail -f logs/script-service.log

# 重启特定服务
./scripts/restart-service.sh app-manager

# 运行测试
make test

# 性能分析
go tool pprof http://localhost:8081/debug/pprof/profile
```

#### 3. 数据库操作

```bash
# 创建新的数据迁移
./scripts/create-migration.sh add_new_table

# 运行数据迁移
./scripts/migrate.sh up

# 回滚数据迁移
./scripts/migrate.sh down

# 重置数据库
./scripts/reset-database.sh
```

### 🧪 测试指南

#### 单元测试
```bash
# 运行所有单元测试
go test ./...

# 运行特定包的测试
go test ./internal/app

# 运行测试并生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

#### 集成测试
```bash
# 运行集成测试
./scripts/run-integration-tests.sh

# 运行 API 测试
./scripts/test-swagger-api.sh
```

#### 端到端测试
```bash
# 运行 E2E 测试
./scripts/run-e2e-tests.sh

# 运行性能测试
./scripts/run-performance-tests.sh
```

### 📋 API 开发规范

#### RESTful API 设计
```go
// 统一的响应格式
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    *Meta       `json:"meta,omitempty"`
}

// 分页元数据
type Meta struct {
    Page     int `json:"page"`
    PageSize int `json:"page_size"`
    Total    int `json:"total"`
}
```

#### API 版本管理
```go
// 路由版本控制
v1 := router.Group("/api/v1")
v2 := router.Group("/api/v2")

// 向后兼容性
// v1 API 保持稳定，新功能在 v2 中添加
```

#### 错误处理
```go
// 使用统一的错误码
const (
    ErrCodeSuccess     = 0
    ErrCodeBadRequest  = 400
    ErrCodeUnauthorized = 401
    ErrCodeForbidden   = 403
    ErrCodeNotFound    = 404
    ErrCodeInternal    = 500
)
```

## 🚢 部署指南

### 🐳 Docker 部署 (推荐)

#### 开发环境部署
```bash
# 克隆项目
git clone https://github.com/your-org/paas-platform.git
cd paas-platform

# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 检查服务状态
docker-compose ps
```

#### 生产环境部署
```bash
# 构建生产镜像
make build-images

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 配置反向代理 (Nginx)
cp deployments/nginx/nginx.conf /etc/nginx/sites-available/paas-platform
```

### ☸️ Kubernetes 部署

#### 使用 Helm Charts
```bash
# 添加 Helm 仓库
helm repo add paas-platform ./helm/paas

# 安装到 Kubernetes
helm install paas-platform paas-platform/paas \
  --namespace paas-system \
  --create-namespace \
  --values values.prod.yaml
```

#### 手动部署
```bash
# 应用 Kubernetes 配置
kubectl apply -f deployments/k8s/namespace.yaml
kubectl apply -f deployments/k8s/configmap.yaml
kubectl apply -f deployments/k8s/secret.yaml
kubectl apply -f deployments/k8s/deployment.yaml
kubectl apply -f deployments/k8s/service.yaml
kubectl apply -f deployments/k8s/ingress.yaml
```

### 🔧 配置管理

#### 环境变量配置
```bash
# 数据库配置
DATABASE_DRIVER=postgres
DATABASE_DSN=host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable

# Redis 配置
REDIS_ADDR=redis:6379
REDIS_PASSWORD=your-redis-password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-for-production
JWT_EXPIRE_HOURS=24

# 服务配置
SERVER_PORT=8080
LOG_LEVEL=info
LOG_FORMAT=json

# 监控配置
METRICS_ENABLED=true
PROMETHEUS_ADDR=prometheus:9090

# 告警配置
ALERTMANAGER_ADDR=alertmanager:9093
NOTIFICATION_WEBHOOK=https://your-webhook-url
```

#### 配置文件示例
```yaml
# configs/app-manager.prod.yaml
server:
  port: 8081
  mode: release

database:
  driver: postgres
  dsn: ${DATABASE_DSN}
  max_open_conns: 100
  max_idle_conns: 10

redis:
  addr: ${REDIS_ADDR}
  password: ${REDIS_PASSWORD}
  db: 0

logging:
  level: info
  format: json
  output: stdout

monitoring:
  enabled: true
  prometheus_addr: ${PROMETHEUS_ADDR}
```

### 📊 监控部署

#### Prometheus + Grafana
```bash
# 启动监控栈
docker-compose -f docker-compose.monitoring.yml up -d

# 或使用脚本
./scripts/start-monitoring.sh
```

#### ELK 日志栈
```bash
# 启动日志栈
docker-compose -f docker-compose.logging.yml up -d

# 或使用脚本
./scripts/start-logging.sh
```

### 🔒 安全配置

#### SSL/TLS 配置
```nginx
# Nginx SSL 配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://paas-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 防火墙配置
```bash
# 开放必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 5432/tcp  # PostgreSQL (仅内网)
ufw allow 6379/tcp  # Redis (仅内网)
```

### 🔄 CI/CD 部署

#### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Build and Deploy
        run: |
          make build-images
          docker-compose -f docker-compose.prod.yml up -d
```

#### GitLab CI
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

deploy:
  stage: deploy
  script:
    - ./scripts/deploy-production.sh
  only:
    - main
```

### 📋 部署检查清单

- [ ] **环境准备**
  - [ ] 服务器资源充足 (CPU: 4核+, 内存: 8GB+, 磁盘: 100GB+)
  - [ ] Docker 和 Docker Compose 已安装
  - [ ] 域名和 SSL 证书已配置

- [ ] **数据库配置**
  - [ ] PostgreSQL 数据库已创建
  - [ ] 数据库用户权限已配置
  - [ ] 数据迁移已执行

- [ ] **安全配置**
  - [ ] JWT 密钥已更新
  - [ ] 数据库密码已更新
  - [ ] Redis 密码已配置
  - [ ] 防火墙规则已配置

- [ ] **监控配置**
  - [ ] Prometheus 监控已启动
  - [ ] Grafana 仪表板已导入
  - [ ] 告警规则已配置
  - [ ] 日志聚合已启动

- [ ] **功能验证**
  - [ ] 用户登录功能正常
  - [ ] 应用部署功能正常
  - [ ] CI/CD 流水线正常
  - [ ] 监控告警正常

## ⚙️ 配置说明

### 🗄️ 数据库配置

#### PostgreSQL 配置
```yaml
database:
  driver: postgres
  dsn: "host=localhost user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 100      # 最大连接数
  max_idle_conns: 10       # 最大空闲连接数
  conn_max_lifetime: 3600  # 连接最大生存时间(秒)
  log_level: info          # 日志级别: silent, error, warn, info
```

#### SQLite 配置 (开发环境)
```yaml
database:
  driver: sqlite
  dsn: "data/paas_platform.db"
  log_level: info
```

### 🔴 Redis 配置
```yaml
redis:
  addr: "localhost:6379"
  password: ""             # Redis 密码
  db: 0                    # 数据库编号
  pool_size: 10            # 连接池大小
  min_idle_conns: 5        # 最小空闲连接数
  dial_timeout: 5s         # 连接超时
  read_timeout: 3s         # 读取超时
  write_timeout: 3s        # 写入超时
```

### 🔐 JWT 配置
```yaml
jwt:
  secret: "your-super-secret-jwt-key"  # JWT 签名密钥
  expire_hours: 24                     # Token 过期时间(小时)
  refresh_expire_hours: 168            # 刷新 Token 过期时间(小时)
  issuer: "paas-platform"              # 签发者
```

### 📝 日志配置
```yaml
logging:
  level: info              # 日志级别: debug, info, warn, error
  format: json             # 日志格式: json, text
  output: stdout           # 输出目标: stdout, file
  file_path: "logs/app.log" # 日志文件路径
  max_size: 100            # 日志文件最大大小(MB)
  max_backups: 10          # 保留的日志文件数量
  max_age: 30              # 日志文件保留天数
```

### 🐳 Docker 配置
```yaml
docker:
  host: "unix:///var/run/docker.sock"  # Docker 守护进程地址
  api_version: "1.41"                   # Docker API 版本
  timeout: 30s                          # 操作超时时间
  registry: "localhost:5000"            # 私有镜像仓库地址
```

### 📊 监控配置
```yaml
monitoring:
  enabled: true                         # 是否启用监控
  prometheus_addr: "localhost:9090"     # Prometheus 地址
  metrics_path: "/metrics"              # 指标路径
  push_gateway: "localhost:9091"        # Push Gateway 地址
  scrape_interval: 15s                  # 抓取间隔
```

## 📊 监控运维

### 🔍 健康检查

所有服务都提供健康检查端点：
```bash
# 检查应用管理服务
curl http://localhost:8081/health

# 检查所有服务
./scripts/health-check.sh
```

### 📈 性能监控

#### Grafana 仪表板
- **系统概览**: http://localhost:3001/d/system-overview
- **应用性能**: http://localhost:3001/d/app-performance
- **数据库监控**: http://localhost:3001/d/database-monitoring
- **Redis 监控**: http://localhost:3001/d/redis-monitoring

#### 关键指标
- **响应时间**: API 平均响应时间 < 200ms
- **错误率**: HTTP 5xx 错误率 < 1%
- **CPU 使用率**: < 80%
- **内存使用率**: < 85%
- **磁盘使用率**: < 90%

### 🚨 告警配置

#### 告警规则示例
```yaml
# configs/alert-rules.yml
groups:
  - name: paas-platform
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率告警"
          description: "服务 {{ $labels.service }} 错误率超过 1%"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "服务 {{ $labels.service }} 95% 响应时间超过 200ms"
```

### 📝 日志管理

#### 日志查询示例
```bash
# 查看应用管理服务日志
tail -f logs/app-manager.log

# 查看错误日志
grep "ERROR" logs/*.log

# 使用 Kibana 查询 (如果启用了 ELK)
# 访问 http://localhost:5601
```

### 🔄 备份恢复

#### 数据库备份
```bash
# 自动备份
./scripts/backup-restore.sh backup

# 手动备份
pg_dump -h localhost -U paas -d paas_platform > backup.sql
```

#### 恢复数据
```bash
# 从备份恢复
./scripts/backup-restore.sh restore backup.sql

# 手动恢复
psql -h localhost -U paas -d paas_platform < backup.sql
```

## 🤝 贡献指南

### 📋 贡献流程

1. **Fork 项目**
   ```bash
   # Fork 项目到您的 GitHub 账户
   # 克隆您的 Fork
   git clone https://github.com/your-username/paas-platform.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   # 进行开发
   # 运行测试
   make test

   # 检查代码质量
   make lint
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**
   - 在 GitHub 上创建 Pull Request
   - 填写详细的描述和测试说明
   - 等待代码审查

### 📝 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(auth): 添加多因素认证支持

- 实现 TOTP 认证
- 添加备用恢复码
- 更新用户界面

Closes #123
```

### 🐛 问题报告

提交 Issue 时请包含：
- 问题的详细描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息 (操作系统、Go 版本等)
- 相关日志或截图

### 💡 功能请求

提交功能请求时请包含：
- 功能的详细描述
- 使用场景
- 预期收益
- 可能的实现方案

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 📞 联系我们

- **项目主页**: https://github.com/your-org/paas-platform
- **问题反馈**: https://github.com/your-org/paas-platform/issues
- **讨论区**: https://github.com/your-org/paas-platform/discussions
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**
## 📚 详细文档指南

### 🔧 开发指南
- [CI/CD 服务使用指南](docs/guides/cicd-service-guide.md) - 完整的CI/CD流水线配置和使用说明
- [开发环境认证配置](docs/guides/development-auth-guide.md) - 开发环境认证跳过和配置方法
- [身份提供商集成指南](docs/guides/idp-integration-guide.md) - 第三方身份提供商集成说明
- [Docker Compose 使用指南](docs/guides/docker-compose-guide.md) - 各种Docker配置文件的使用说明

### 📋 历史记录
- [修复记录归档](docs/历史修复记录/) - 历史问题修复记录和解决方案

### 🔧 脚本工具
- [脚本归档](scripts/archived/) - 已归档的重复或过期脚本

## 🔄 优化历史

本项目于2025年8月进行了全面的文档和脚本优化：
- 减少了68%的重复文件（从22个减少到7个）
- 节省了20MB存储空间
- 建立了清晰的文档组织结构
- 提升了开发效率和维护性

详细的优化过程请参考 [优化分析报告](代码库文档和脚本优化分析报告.md)。
