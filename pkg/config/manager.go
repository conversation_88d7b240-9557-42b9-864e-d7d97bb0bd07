package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	App          AppConfig          `mapstructure:"app"`
	Server       ServerConfig       `mapstructure:"server"`
	Logging      LoggingConfig      `mapstructure:"logging"`
	Database     DatabaseConfig     `mapstructure:"database"`
	Redis        RedisConfig        `mapstructure:"redis"`
	Auth         AuthConfig         `mapstructure:"auth"`
	Docker       DockerConfig       `mapstructure:"docker"`
	Git          GitConfig          `mapstructure:"git"`
	Monitoring   MonitoringConfig   `mapstructure:"monitoring"`
	Notification NotificationConfig `mapstructure:"notification"`
	Storage      StorageConfig      `mapstructure:"storage"`
	Features     FeaturesConfig     `mapstructure:"features"`
	Resources    ResourcesConfig    `mapstructure:"resources"`
	Security     SecurityConfig     `mapstructure:"security"`
	Development  DevelopmentConfig  `mapstructure:"development"`
	CICD         CICDConfig         `mapstructure:"cicd"`
	Backup       BackupConfig       `mapstructure:"backup"`
}

// AppConfig 应用基础配置
type AppConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `mapstructure:"port"`
	Host         string        `mapstructure:"host"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level    string `mapstructure:"level"`
	Format   string `mapstructure:"format"`
	Output   string `mapstructure:"output"`
	FilePath string `mapstructure:"file_path"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Enabled         bool          `mapstructure:"enabled"`
	Driver          string        `mapstructure:"driver"`
	DSN             string        `mapstructure:"dsn"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	AutoMigrate     bool          `mapstructure:"auto_migrate"`
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Enabled      bool          `mapstructure:"enabled"`
	Addr         string        `mapstructure:"addr"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	Enabled bool      `mapstructure:"enabled"`
	JWT     JWTConfig `mapstructure:"jwt"`
	DevUser DevUser   `mapstructure:"dev_user"`
}

// JWTConfig JWT 配置
type JWTConfig struct {
	Secret           string        `mapstructure:"secret"`
	ExpiresIn        time.Duration `mapstructure:"expires_in"`
	RefreshExpiresIn time.Duration `mapstructure:"refresh_expires_in"`
}

// DevUser 开发用户配置
type DevUser struct {
	ID       string   `mapstructure:"id"`
	Username string   `mapstructure:"username"`
	Email    string   `mapstructure:"email"`
	Roles    []string `mapstructure:"roles"`
}

// DockerConfig Docker 配置
type DockerConfig struct {
	Enabled    bool           `mapstructure:"enabled"`
	Host       string         `mapstructure:"host"`
	APIVersion string         `mapstructure:"api_version"`
	Registry   RegistryConfig `mapstructure:"registry"`
	Build      BuildConfig    `mapstructure:"build"`
}

// RegistryConfig 镜像仓库配置
type RegistryConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Mode     string `mapstructure:"mode"`
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Insecure bool   `mapstructure:"insecure"`
}

// BuildConfig 构建配置
type BuildConfig struct {
	LocalOnly bool          `mapstructure:"local_only"`
	UseCache  bool          `mapstructure:"use_cache"`
	Timeout   time.Duration `mapstructure:"timeout"`
}

// GitConfig Git 配置
type GitConfig struct {
	Enabled   bool          `mapstructure:"enabled"`
	Providers []GitProvider `mapstructure:"providers"`
}

// GitProvider Git 提供商配置
type GitProvider struct {
	Name          string `mapstructure:"name"`
	Type          string `mapstructure:"type"`
	URL           string `mapstructure:"url"`
	APIURL        string `mapstructure:"api_url"`
	WebhookSecret string `mapstructure:"webhook_secret"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled     bool              `mapstructure:"enabled"`
	Metrics     MetricsConfig     `mapstructure:"metrics"`
	HealthCheck HealthCheckConfig `mapstructure:"health_check"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled  bool          `mapstructure:"enabled"`
	Interval time.Duration `mapstructure:"interval"`
	Timeout  time.Duration `mapstructure:"timeout"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Enabled  bool                    `mapstructure:"enabled"`
	Channels []NotificationChannel   `mapstructure:"channels"`
}

// NotificationChannel 通知渠道
type NotificationChannel struct {
	Type   string   `mapstructure:"type"`
	URL    string   `mapstructure:"url"`
	Events []string `mapstructure:"events"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type  string      `mapstructure:"type"`
	Local LocalConfig `mapstructure:"local"`
}

// LocalConfig 本地存储配置
type LocalConfig struct {
	BasePath string `mapstructure:"base_path"`
	MaxSize  string `mapstructure:"max_size"`
}

// FeaturesConfig 功能开关配置
type FeaturesConfig struct {
	AppManagement       bool `mapstructure:"app_management"`
	UserManagement      bool `mapstructure:"user_management"`
	ConfigManagement    bool `mapstructure:"config_management"`
	CICD                bool `mapstructure:"cicd"`
	Monitoring          bool `mapstructure:"monitoring"`
	LoggingAggregation  bool `mapstructure:"logging_aggregation"`
	AutoScaling         bool `mapstructure:"auto_scaling"`
	LoadBalancing       bool `mapstructure:"load_balancing"`
	Backup              bool `mapstructure:"backup"`
}

// ResourcesConfig 资源配置
type ResourcesConfig struct {
	MemoryCache MemoryCacheConfig `mapstructure:"memory_cache"`
	RedisCache  RedisCacheConfig  `mapstructure:"redis_cache"`
	Upload      UploadConfig      `mapstructure:"upload"`
}

// MemoryCacheConfig 内存缓存配置
type MemoryCacheConfig struct {
	MaxSize string        `mapstructure:"max_size"`
	TTL     time.Duration `mapstructure:"ttl"`
}

// RedisCacheConfig Redis 缓存配置
type RedisCacheConfig struct {
	DefaultTTL time.Duration `mapstructure:"default_ttl"`
	MaxMemory  string        `mapstructure:"max_memory"`
}

// UploadConfig 上传配置
type UploadConfig struct {
	MaxFileSize  string `mapstructure:"max_file_size"`
	MaxTotalSize string `mapstructure:"max_total_size"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	RateLimit RateLimitConfig `mapstructure:"rate_limit"`
	CORS      CORSConfig      `mapstructure:"cors"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled            bool `mapstructure:"enabled"`
	RequestsPerMinute  int  `mapstructure:"requests_per_minute"`
	Burst              int  `mapstructure:"burst"`
}

// CORSConfig CORS 配置
type CORSConfig struct {
	Enabled        bool     `mapstructure:"enabled"`
	AllowedOrigins []string `mapstructure:"allowed_origins"`
	AllowedMethods []string `mapstructure:"allowed_methods"`
	AllowedHeaders []string `mapstructure:"allowed_headers"`
}

// DevelopmentConfig 开发配置
type DevelopmentConfig struct {
	Enabled               bool        `mapstructure:"enabled"`
	HotReload             bool        `mapstructure:"hot_reload"`
	DebugMode             bool        `mapstructure:"debug_mode"`
	MockExternalServices  bool        `mapstructure:"mock_external_services"`
	Tools                 ToolsConfig `mapstructure:"tools"`
}

// ToolsConfig 开发工具配置
type ToolsConfig struct {
	Swagger bool `mapstructure:"swagger"`
	Pprof   bool `mapstructure:"pprof"`
	AdminUI bool `mapstructure:"admin_ui"`
}

// CICDConfig CI/CD 配置
type CICDConfig struct {
	Enabled bool        `mapstructure:"enabled"`
	Build   BuildCICD   `mapstructure:"build"`
	Deploy  DeployCICD  `mapstructure:"deploy"`
}

// BuildCICD 构建配置
type BuildCICD struct {
	WorkspaceDir         string        `mapstructure:"workspace_dir"`
	MaxConcurrentBuilds  int           `mapstructure:"max_concurrent_builds"`
	DefaultTimeout       time.Duration `mapstructure:"default_timeout"`
}

// DeployCICD 部署配置
type DeployCICD struct {
	Strategies      []string `mapstructure:"strategies"`
	DefaultStrategy string   `mapstructure:"default_strategy"`
}

// BackupConfig 备份配置
type BackupConfig struct {
	Enabled       bool           `mapstructure:"enabled"`
	Schedule      string         `mapstructure:"schedule"`
	RetentionDays int            `mapstructure:"retention_days"`
	Targets       []BackupTarget `mapstructure:"targets"`
}

// BackupTarget 备份目标
type BackupTarget struct {
	Type    string `mapstructure:"type"`
	Enabled bool   `mapstructure:"enabled"`
}

// Manager 配置管理器
type Manager struct {
	config *Config
	viper  *viper.Viper
}

// NewManager 创建配置管理器
func NewManager() *Manager {
	return &Manager{
		viper: viper.New(),
	}
}

// Load 加载配置
func (m *Manager) Load(configFile string) error {
	// 设置默认值
	m.setDefaults()
	
	// 支持环境变量
	m.viper.AutomaticEnv()
	m.viper.SetEnvPrefix("PAAS")
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	
	// 如果指定了配置文件，直接使用
	if configFile != "" {
		m.viper.SetConfigFile(configFile)
	} else {
		// 自动检测配置文件
		if err := m.autoDetectConfig(); err != nil {
			return fmt.Errorf("自动检测配置文件失败: %w", err)
		}
	}
	
	// 读取配置文件
	if err := m.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	m.config = &Config{}
	if err := m.viper.Unmarshal(m.config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}
	
	// 验证配置
	if err := m.validate(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	return nil
}

// GetConfig 获取配置
func (m *Manager) GetConfig() *Config {
	return m.config
}

// setDefaults 设置默认值
func (m *Manager) setDefaults() {
	// 应用默认值
	m.viper.SetDefault("app.name", "paas-platform")
	m.viper.SetDefault("app.version", "1.0.0")
	m.viper.SetDefault("app.environment", "development")
	
	// 服务器默认值
	m.viper.SetDefault("server.port", 8080)
	m.viper.SetDefault("server.host", "0.0.0.0")
	m.viper.SetDefault("server.read_timeout", "30s")
	m.viper.SetDefault("server.write_timeout", "30s")
	m.viper.SetDefault("server.idle_timeout", "60s")
	
	// 日志默认值
	m.viper.SetDefault("logging.level", "info")
	m.viper.SetDefault("logging.format", "text")
	m.viper.SetDefault("logging.output", "stdout")
	
	// 数据库默认值
	m.viper.SetDefault("database.enabled", true)
	m.viper.SetDefault("database.driver", "sqlite")
	m.viper.SetDefault("database.dsn", "./data/paas.db")
	m.viper.SetDefault("database.auto_migrate", true)
	
	// Redis 默认值
	m.viper.SetDefault("redis.enabled", false)
	
	// 认证默认值
	m.viper.SetDefault("auth.enabled", false)
	m.viper.SetDefault("auth.jwt.secret", "dev-secret-key")
	m.viper.SetDefault("auth.jwt.expires_in", "24h")
	
	// Docker 默认值
	m.viper.SetDefault("docker.enabled", true)
	m.viper.SetDefault("docker.host", "unix:///var/run/docker.sock")
	m.viper.SetDefault("docker.api_version", "1.41")
	m.viper.SetDefault("docker.registry.enabled", false)
	m.viper.SetDefault("docker.registry.mode", "local")
	m.viper.SetDefault("docker.build.local_only", true)
	m.viper.SetDefault("docker.build.use_cache", true)
	
	// 功能开关默认值
	m.viper.SetDefault("features.app_management", true)
	m.viper.SetDefault("features.user_management", true)
	m.viper.SetDefault("features.config_management", true)
	
	// 开发模式默认值
	m.viper.SetDefault("development.enabled", true)
	m.viper.SetDefault("development.debug_mode", true)
	m.viper.SetDefault("development.tools.swagger", true)
}

// autoDetectConfig 自动检测配置文件
func (m *Manager) autoDetectConfig() error {
	// 配置文件优先级：
	// 1. app.minimal.yaml (最小化配置)
	// 2. app.local.yaml (本地开发配置)
	// 3. app.standard.yaml (标准配置)
	// 4. app.yaml (默认配置)
	
	configPaths := []string{"./configs", "."}
	configNames := []string{"app.minimal", "app.local", "app.standard", "app"}
	
	for _, path := range configPaths {
		for _, name := range configNames {
			configFile := filepath.Join(path, name+".yaml")
			if _, err := os.Stat(configFile); err == nil {
				m.viper.SetConfigFile(configFile)
				return nil
			}
		}
	}
	
	// 如果没有找到配置文件，使用默认配置
	m.viper.SetConfigName("app")
	m.viper.SetConfigType("yaml")
	m.viper.AddConfigPath("./configs")
	m.viper.AddConfigPath(".")
	
	return nil
}

// validate 验证配置
func (m *Manager) validate() error {
	// 验证必需的配置项
	if m.config.Database.Enabled && m.config.Database.DSN == "" {
		return fmt.Errorf("数据库已启用但未配置 DSN")
	}
	
	if m.config.Redis.Enabled && m.config.Redis.Addr == "" {
		return fmt.Errorf("Redis 已启用但未配置地址")
	}
	
	if m.config.Server.Port <= 0 || m.config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", m.config.Server.Port)
	}
	
	return nil
}

// IsMinimalMode 检查是否为最小化模式
func (m *Manager) IsMinimalMode() bool {
	configFile := m.viper.ConfigFileUsed()
	return strings.Contains(filepath.Base(configFile), "minimal")
}

// IsLocalMode 检查是否为本地模式
func (m *Manager) IsLocalMode() bool {
	configFile := m.viper.ConfigFileUsed()
	return strings.Contains(filepath.Base(configFile), "local") || 
		   m.config.Docker.Registry.Mode == "local" ||
		   m.config.Docker.Build.LocalOnly
}
