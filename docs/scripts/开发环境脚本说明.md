# PaaS 平台开发环境脚本说明

## 概述

本文档详细说明了 PaaS 平台开发环境相关脚本的功能、使用方法和配置选项。

## 脚本列表

### 1. start-dev-mode.sh - 开发环境启动脚本

#### 功能描述
- 🚀 一键启动完整的 PaaS 开发环境
- 🔓 自动配置开发模式（跳过认证）
- 📦 自动构建所有服务
- 🔧 生成开发配置文件
- 💾 创建必要的目录结构
- 🏥 提供健康检查和状态监控

#### 支持的服务
- **user-service** (端口: 8083) - 用户认证服务
- **api-gateway** (端口: 8080) - API网关服务
- **app-manager** (端口: 8081) - 应用管理服务
- **cicd-service** (端口: 8082) - CI/CD服务
- **config-service** (端口: 8084) - 配置服务
- **script-service** (端口: 8085) - 脚本执行服务
- **faas-service** (端口: 8087) - FaaS服务

#### 使用方法

```bash
# 启动所有服务
./scripts/start-dev-mode.sh

# 仅启动指定服务
./scripts/start-dev-mode.sh -s user-service,api-gateway

# 并行启动服务（更快）
./scripts/start-dev-mode.sh -p

# 清理数据后启动
./scripts/start-dev-mode.sh -c

# 详细输出模式
./scripts/start-dev-mode.sh -v

# 跳过构建步骤
./scripts/start-dev-mode.sh --skip-build

# 仅生成配置文件
./scripts/start-dev-mode.sh --config-only
```

#### 命令行选项

| 选项 | 说明 |
|------|------|
| `-h, --help` | 显示帮助信息 |
| `-s, --services SERVICES` | 指定要启动的服务（逗号分隔） |
| `-p, --parallel` | 并行启动服务 |
| `-c, --clean` | 清理数据后启动 |
| `-v, --verbose` | 详细输出 |
| `--skip-build` | 跳过构建步骤 |
| `--skip-deps` | 跳过依赖检查 |
| `--config-only` | 仅生成配置文件 |
| `--continue-on-error` | 出错时继续启动其他服务 |

#### 环境变量配置

脚本会自动设置以下开发环境变量：

```bash
# 基础环境
ENV=development
DEBUG=true
GIN_MODE=debug

# 认证配置（开发模式）
PAAS_AUTH_ENABLED=false
PAAS_DEV_MODE=true
PAAS_DEV_TOKEN=dev-token-2024-secure

# 开发用户信息
PAAS_DEV_USER_ID=dev-user-001
PAAS_DEV_USER_USERNAME=开发者
PAAS_DEV_USER_ROLES=admin,developer,script_executor

# 数据库配置
DATABASE_DRIVER=sqlite
DB_LOG_LEVEL=info

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=text
```

#### 目录结构

脚本会自动创建以下目录：

```
project/
├── bin/                    # 编译后的二进制文件
├── data/                   # 数据目录
│   ├── storage/           # 存储文件
│   ├── workspaces/        # 工作空间
│   ├── artifacts/         # 构建产物
│   ├── templates/         # 模板文件
│   ├── configs/           # 配置数据
│   └── faas/              # FaaS相关数据
├── logs/                   # 日志文件
├── tmp/                    # 临时文件
└── configs/               # 配置文件
```

### 2. stop-dev-mode.sh - 开发环境停止脚本

#### 功能描述
- 🛑 安全停止所有开发环境服务
- 🔍 智能检测运行中的服务
- 🧹 可选的数据清理功能
- 📊 详细的停止状态报告
- ⚡ 支持强制停止模式

#### 使用方法

```bash
# 停止所有服务
./scripts/stop-dev-mode.sh

# 仅停止指定服务
./scripts/stop-dev-mode.sh -s user-service,api-gateway

# 强制停止（使用 SIGKILL）
./scripts/stop-dev-mode.sh -f

# 停止服务并清理工作空间
./scripts/stop-dev-mode.sh --clean-workspace

# 停止服务并清理所有数据
./scripts/stop-dev-mode.sh --clean-all

# 保留日志文件
./scripts/stop-dev-mode.sh --keep-logs

# 详细输出
./scripts/stop-dev-mode.sh -v
```

#### 命令行选项

| 选项 | 说明 |
|------|------|
| `-h, --help` | 显示帮助信息 |
| `-s, --services SERVICES` | 指定要停止的服务（逗号分隔） |
| `-f, --force` | 强制停止（使用 SIGKILL） |
| `-v, --verbose` | 详细输出 |
| `--clean-workspace` | 同时清理工作空间数据 |
| `--clean-all` | 清理所有数据（包括数据库） |
| `--keep-logs` | 保留日志文件 |

#### 停止策略

脚本使用多层停止策略确保服务完全停止：

1. **PID文件停止** - 通过保存的PID文件停止服务
2. **端口停止** - 通过端口占用情况停止服务
3. **进程名停止** - 通过进程名称停止服务
4. **优雅退出** - 先发送 SIGTERM，等待优雅退出
5. **强制终止** - 如果优雅退出失败，使用 SIGKILL

### 3. stop-services.sh - 通用服务停止脚本

#### 功能描述
- 🔧 通用的服务停止工具
- 🐳 支持 Docker 容器停止
- 🔌 支持外部服务停止
- 🏭 支持生产环境模式
- 👀 预览模式（dry-run）

#### 使用方法

```bash
# 停止所有 PaaS 服务
./scripts/stop-services.sh

# 开发模式停止（调用 stop-dev-mode.sh）
./scripts/stop-services.sh --dev-mode

# 生产模式停止（需要确认）
./scripts/stop-services.sh --production

# 停止包括外部服务
./scripts/stop-services.sh -e

# 停止 Docker 容器
./scripts/stop-services.sh --docker

# 预览模式（不实际执行）
./scripts/stop-services.sh --dry-run

# 强制停止所有服务
./scripts/stop-services.sh -f
```

#### 支持的外部服务

- **Redis** (端口: 6379)
- **PostgreSQL** (端口: 5432)
- **Elasticsearch** (端口: 9200)
- **Kibana** (端口: 5601)
- **Prometheus** (端口: 9090)
- **Grafana** (端口: 3000)

## 最佳实践

### 开发工作流程

1. **启动开发环境**
   ```bash
   ./scripts/start-dev-mode.sh -v
   ```

2. **开发和测试**
   - 访问 API 文档: http://localhost:8080/swagger
   - 查看日志: `tail -f logs/*.log`
   - 检查服务状态: `ps aux | grep -E '(user|api|app)'`

3. **重启特定服务**
   ```bash
   ./scripts/stop-dev-mode.sh -s user-service
   ./scripts/start-dev-mode.sh -s user-service --skip-build
   ```

4. **清理重启**
   ```bash
   ./scripts/stop-dev-mode.sh --clean-all
   ./scripts/start-dev-mode.sh -c
   ```

### 故障排除

#### 端口占用问题
```bash
# 检查端口占用
netstat -tlnp | grep -E '808[0-7]'

# 强制释放端口
./scripts/stop-services.sh -f
```

#### 服务启动失败
```bash
# 查看详细日志
tail -f logs/[service-name].log

# 重新构建服务
go build -o bin/[service-name] cmd/[service-name]

# 清理重启
./scripts/start-dev-mode.sh -c --verbose
```

#### 数据库问题
```bash
# 清理数据库文件
./scripts/stop-dev-mode.sh --clean-all

# 重新初始化
./scripts/start-dev-mode.sh -c
```

## 安全注意事项

### ⚠️ 开发模式警告

开发模式脚本会：
- 🔓 **禁用用户认证** - 所有API请求无需Token
- 🧑‍💻 **使用默认开发用户** - 具有admin权限
- 📝 **启用调试日志** - 可能包含敏感信息
- 🚨 **绝不能在生产环境使用！**

### 生产环境使用

在生产环境中：
- ✅ 使用 `--production` 模式
- ✅ 启用认证: `PAAS_AUTH_ENABLED=true`
- ✅ 使用安全的JWT密钥
- ✅ 配置适当的日志级别
- ✅ 使用外部数据库（PostgreSQL）

## 配置文件

### 开发配置模板

每个服务的开发配置文件会自动生成在 `configs/` 目录：

```yaml
# 示例: configs/user-service.dev.yaml
server:
  port: 8083
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

database:
  driver: sqlite
  dsn: "./data/user-service.db"
  auto_migrate: true

dev:
  enabled: true
  auth_disabled: true
  cors_enabled: true
  pprof_enabled: true

logging:
  level: debug
  format: text
  output: stdout
```

## 扩展和自定义

### 添加新服务

1. 在 `SERVICES` 数组中添加服务配置
2. 在 `cmd/` 目录创建服务代码
3. 更新构建和启动逻辑

### 自定义环境变量

在启动脚本中修改 `setup_dev_env()` 函数来添加自定义环境变量。

### 集成外部工具

可以在脚本中集成其他开发工具，如：
- 代码热重载
- 自动测试运行
- 性能监控
- 日志聚合

## 相关文档

- [PaaS 平台架构文档](../architecture/README.md)
- [服务配置说明](../configuration/README.md)
- [部署指南](../deployment/README.md)
- [故障排除指南](../troubleshooting/README.md)
