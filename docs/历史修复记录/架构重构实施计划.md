# PaaS 平台架构重构实施计划

## 📋 重构概述

基于对当前架构问题的深入分析，制定详细的架构重构实施计划，解决认证服务与应用管理服务职责混乱的核心问题。

### 重构目标
- **职责分离**: 将认证功能从 App Manager 中完全分离
- **架构合规**: 符合微服务单一职责原则
- **系统稳定**: 确保重构过程中系统持续可用
- **向后兼容**: 保证现有功能不受影响

### 重构范围
- 创建独立的 User Service (8083)
- 重构 API Gateway 路由配置
- 修复前端登录问题
- 数据迁移和兼容性处理

## 🎯 重构策略

### 1. 渐进式重构策略

```mermaid
graph LR
    A[当前架构] --> B[并行架构]
    B --> C[流量切换]
    C --> D[旧架构清理]
    D --> E[新架构完成]
    
    subgraph "阶段1: 并行部署"
        B1[App Manager<br/>认证功能]
        B2[User Service<br/>新认证服务]
    end
    
    subgraph "阶段2: 流量切换"
        C1[API Gateway<br/>路由切换]
        C2[前端配置<br/>更新]
    end
    
    subgraph "阶段3: 清理优化"
        D1[移除旧认证代码]
        D2[数据清理]
    end
```

### 2. 风险控制策略

#### 2.1 蓝绿部署
- **蓝环境**: 当前运行的 App Manager (包含认证)
- **绿环境**: 新的 User Service + 重构后的 App Manager
- **切换策略**: 通过 API Gateway 控制流量分配

#### 2.2 回滚准备
- **配置回滚**: 快速恢复 API Gateway 路由配置
- **数据回滚**: 数据库变更的回滚脚本
- **服务回滚**: 容器镜像版本回滚

## 📅 实施时间表

### 第1周: 准备和设计阶段

#### Day 1-2: 详细设计
- [ ] 完善 User Service API 设计
- [ ] 数据迁移方案设计
- [ ] 测试策略制定
- [ ] 回滚方案准备

#### Day 3-5: 环境准备
- [ ] 开发环境搭建
- [ ] CI/CD 流水线配置
- [ ] 监控告警配置
- [ ] 测试数据准备

### 第2周: 开发和测试阶段

#### Day 6-8: User Service 开发
- [ ] 用户认证 API 实现
- [ ] JWT 服务实现
- [ ] RBAC 权限管理
- [ ] 数据库模型创建

#### Day 9-10: 集成和测试
- [ ] API Gateway 路由配置
- [ ] 前端登录流程修复
- [ ] 集成测试执行
- [ ] 性能测试验证

### 第3周: 部署和切换

#### Day 11-12: 生产部署
- [ ] User Service 生产部署
- [ ] 数据迁移执行
- [ ] 并行运行验证
- [ ] 监控指标确认

#### Day 13-14: 流量切换
- [ ] 灰度流量切换 (10% -> 50% -> 100%)
- [ ] 实时监控和问题处理
- [ ] 用户反馈收集
- [ ] 性能指标验证

## 🔧 技术实施方案

### 1. User Service 实现

#### 1.1 服务架构
```go
// User Service 主要组件
package main

import (
    "context"
    "log"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    
    "paas-platform/internal/auth"
    "paas-platform/internal/database"
    "paas-platform/pkg/logger"
)

func main() {
    // 初始化数据库连接
    db, err := database.NewConnection(&database.Config{
        Type: "postgres",
        Host: "localhost",
        Port: 5432,
        Database: "paas_users",
        Username: "paas_user",
        Password: "password",
    })
    if err != nil {
        log.Fatal("数据库连接失败:", err)
    }
    
    // 初始化日志
    logger := logger.New(&logger.Config{
        Level:  "info",
        Format: "json",
    })
    
    // 初始化认证服务
    authService := auth.NewAuthService(db, logger, &auth.Config{
        JWTSecret:          "your-jwt-secret",
        AccessTokenExpiry:  time.Hour * 24,
        RefreshTokenExpiry: time.Hour * 24 * 7,
    })
    
    // 初始化 HTTP 处理器
    authHandler := auth.NewHandler(authService, logger)
    
    // 设置路由
    router := gin.New()
    router.Use(gin.Logger(), gin.Recovery())
    
    v1 := router.Group("/api/v1")
    authHandler.RegisterRoutes(v1)
    
    // 启动服务
    server := &http.Server{
        Addr:    ":8083",
        Handler: router,
    }
    
    logger.Info("User Service 启动", "port", 8083)
    if err := server.ListenAndServe(); err != nil {
        log.Fatal("服务启动失败:", err)
    }
}
```

#### 1.2 核心 API 接口
```go
// 认证相关 API
type AuthHandler struct {
    service auth.Service
    logger  logger.Logger
}

// 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
    var req auth.LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误"})
        return
    }
    
    // 执行登录逻辑
    resp, err := h.service.Login(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error("用户登录失败", "error", err, "username", req.Username)
        c.JSON(http.StatusUnauthorized, gin.H{"error": "登录失败"})
        return
    }
    
    h.logger.Info("用户登录成功", "user_id", resp.User.ID)
    c.JSON(http.StatusOK, resp)
}

// 令牌验证
func (h *AuthHandler) ValidateToken(c *gin.Context) {
    token := c.GetHeader("Authorization")
    if token == "" {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
        return
    }
    
    // 移除 "Bearer " 前缀
    if len(token) > 7 && token[:7] == "Bearer " {
        token = token[7:]
    }
    
    // 验证令牌
    claims, err := h.service.ValidateToken(c.Request.Context(), token)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "令牌无效"})
        return
    }
    
    c.JSON(http.StatusOK, claims)
}

// 用户注销
func (h *AuthHandler) Logout(c *gin.Context) {
    token := c.GetHeader("Authorization")
    if len(token) > 7 && token[:7] == "Bearer " {
        token = token[7:]
    }
    
    err := h.service.Logout(c.Request.Context(), token)
    if err != nil {
        h.logger.Error("用户注销失败", "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": "注销失败"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "注销成功"})
}
```

### 2. API Gateway 路由重构

#### 2.1 新路由配置
```yaml
# API Gateway 路由配置
routes:
  # 认证相关路由 -> User Service
  - path: "/api/v1/auth/*"
    target: "http://user-service:8083"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware: ["cors", "rate_limit"]
    
  - path: "/api/v1/users/*"
    target: "http://user-service:8083"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware: ["cors", "rate_limit", "auth"]
    
  # 应用管理路由 -> App Manager
  - path: "/api/v1/apps/*"
    target: "http://app-manager:8081"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware: ["cors", "rate_limit", "auth"]
    
  # CI/CD 路由 -> CI/CD Service
  - path: "/api/v1/pipelines/*"
    target: "http://cicd-service:8082"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware: ["cors", "rate_limit", "auth"]
    
  # 配置管理路由 -> Config Service
  - path: "/api/v1/configs/*"
    target: "http://config-service:8084"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware: ["cors", "rate_limit", "auth"]
```

#### 2.2 认证中间件更新
```go
// 更新后的认证中间件
func AuthMiddleware(userServiceURL string) gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
            c.Abort()
            return
        }
        
        // 调用 User Service 验证令牌
        client := &http.Client{Timeout: 5 * time.Second}
        req, _ := http.NewRequest("GET", userServiceURL+"/api/v1/auth/validate", nil)
        req.Header.Set("Authorization", token)
        
        resp, err := client.Do(req)
        if err != nil || resp.StatusCode != http.StatusOK {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "令牌验证失败"})
            c.Abort()
            return
        }
        
        // 解析用户信息
        var claims auth.Claims
        if err := json.NewDecoder(resp.Body).Decode(&claims); err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "令牌解析失败"})
            c.Abort()
            return
        }
        
        // 将用户信息存储到上下文
        c.Set("user_id", claims.UserID)
        c.Set("tenant_id", claims.TenantID)
        c.Set("roles", claims.Roles)
        
        c.Next()
    }
}
```

### 3. 前端登录修复

#### 3.1 API 配置更新
```typescript
// 更新 API 基础配置
const API_CONFIG = {
  // 认证相关 API 通过 API Gateway 路由到 User Service
  AUTH_BASE_URL: '/api/v1/auth',
  USER_BASE_URL: '/api/v1/users',
  
  // 其他业务 API
  APP_BASE_URL: '/api/v1/apps',
  CICD_BASE_URL: '/api/v1/pipelines',
  CONFIG_BASE_URL: '/api/v1/configs',
}

// 认证 API 客户端
export class AuthApi {
  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await request.post(`${API_CONFIG.AUTH_BASE_URL}/login`, credentials)
    return response.data
  }
  
  /**
   * 用户注销
   */
  async logout(): Promise<ApiResponse<void>> {
    const response = await request.post(`${API_CONFIG.AUTH_BASE_URL}/logout`)
    return response.data
  }
  
  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<TokenResponse>> {
    const response = await request.post(`${API_CONFIG.AUTH_BASE_URL}/refresh`, {
      refresh_token: refreshToken
    })
    return response.data
  }
  
  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await request.get(`${API_CONFIG.USER_BASE_URL}/me`)
    return response.data
  }
}
```

#### 3.2 Token 处理修复
```typescript
// 修复 Token 解析问题
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'
  
  /**
   * 保存令牌
   */
  static saveTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
  }
  
  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY)
  }
  
  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }
  
  /**
   * 检查令牌是否即将过期
   */
  static isTokenExpiringSoon(token: string): boolean {
    try {
      // 修复：正确处理 JWT 格式
      const parts = token.split('.')
      if (parts.length !== 3) {
        console.warn('令牌格式不正确')
        return true
      }
      
      const payload = JSON.parse(atob(parts[1]))
      const exp = payload.exp * 1000 // 转换为毫秒
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000
      
      return exp - now < fiveMinutes
    } catch (error) {
      console.error('解析令牌失败:', error)
      return true
    }
  }
  
  /**
   * 清除所有令牌
   */
  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
  }
}
```

### 4. 数据迁移方案

#### 4.1 数据迁移脚本
```sql
-- 创建 User Service 数据库
CREATE DATABASE paas_users;

-- 迁移用户相关表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_users_email (email),
    INDEX idx_users_tenant_id (tenant_id),
    INDEX idx_users_status (status)
);

-- 从 App Manager 数据库迁移数据
INSERT INTO paas_users.users (id, name, email, password_hash, status, tenant_id, created_at, updated_at)
SELECT id, name, email, password_hash, status, tenant_id, created_at, updated_at
FROM paas_app.users
WHERE deleted_at IS NULL;

-- 迁移角色和权限表
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_roles_name_tenant (name, tenant_id)
);

-- 迁移用户角色关联表
CREATE TABLE user_roles (
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

#### 4.2 数据一致性检查
```bash
#!/bin/bash
# 数据迁移验证脚本

echo "=== 数据迁移验证 ==="

# 1. 检查用户数量一致性
OLD_COUNT=$(psql -h app-manager-db -U paas_user -d paas_app -t -c "SELECT COUNT(*) FROM users WHERE deleted_at IS NULL;")
NEW_COUNT=$(psql -h user-service-db -U paas_user -d paas_users -t -c "SELECT COUNT(*) FROM users;")

echo "原数据库用户数: $OLD_COUNT"
echo "新数据库用户数: $NEW_COUNT"

if [ "$OLD_COUNT" -eq "$NEW_COUNT" ]; then
    echo "✅ 用户数量一致"
else
    echo "❌ 用户数量不一致"
    exit 1
fi

# 2. 检查关键用户数据
echo "检查管理员用户..."
ADMIN_EXISTS=$(psql -h user-service-db -U paas_user -d paas_users -t -c "SELECT COUNT(*) FROM users WHERE email = '<EMAIL>';")

if [ "$ADMIN_EXISTS" -gt 0 ]; then
    echo "✅ 管理员用户存在"
else
    echo "❌ 管理员用户缺失"
    exit 1
fi

echo "=== 验证完成 ==="
```

## 📊 监控和验证

### 1. 关键指标监控
```yaml
# 重构过程监控指标
monitoring_metrics:
  # 系统可用性
  - name: service_availability
    query: up{job="user-service"}
    threshold: 1
    
  # 登录成功率
  - name: login_success_rate
    query: rate(auth_login_success_total[5m]) / rate(auth_login_attempts_total[5m])
    threshold: 0.995
    
  # API 响应时间
  - name: api_response_time
    query: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
    threshold: 0.2
    
  # 错误率
  - name: error_rate
    query: rate(http_requests_total{status=~"5.."}[5m])
    threshold: 0.001
```

### 2. 验收测试清单
- [ ] 用户登录功能正常
- [ ] 令牌验证功能正常
- [ ] 权限检查功能正常
- [ ] 用户管理功能正常
- [ ] 前端界面正常显示
- [ ] API 响应时间符合要求
- [ ] 系统可用性达标
- [ ] 数据一致性验证通过

---

*本实施计划将根据实际执行情况动态调整*
*最后更新时间: 2025-08-15*
