# Docker 镜像仓库移除方案总结

## 概述

本文档总结了在单台 Docker 服务器上进行开发时移除 Docker 镜像仓库的完整方案。通过配置本地构建模式，开发者可以在不使用镜像仓库的情况下进行 PaaS 平台的开发和测试。

## 方案优势

### 1. 简化开发环境
- **减少组件**: 无需运行 Docker Registry 服务
- **降低复杂度**: 减少网络配置和认证设置
- **节省资源**: 减少内存和存储占用

### 2. 提高开发效率
- **加快构建**: 直接本地构建，无需推送/拉取镜像
- **即时反馈**: 构建完成即可使用，无网络延迟
- **简化调试**: 本地镜像便于调试和测试

### 3. 适用场景
- 单人开发或小团队开发
- 原型开发和功能验证
- 本地测试和调试
- 学习和实验环境

## 实施方案

### 1. 配置文件修改

#### docker-compose.dev.yml
- 注释掉 `registry` 服务配置
- 修改 CI/CD 服务的 `DOCKER_REGISTRY` 环境变量为 `local`
- 移除 `registry_data` 数据卷

#### 新增配置文件
- `docker-compose.local.yml`: 专为本地开发优化的配置
- `configs/cicd-service.local.yaml`: 本地构建模式的 CI/CD 配置

### 2. 代码修改

#### Docker 客户端增强 (internal/cicd/docker.go)
```go
type PushImageOptions struct {
    Registry  RegistryAuth `json:"registry"`
    LocalOnly bool         `json:"local_only"` // 新增：本地构建模式标志
    // ... 其他字段
}

func (d *DefaultDockerClient) PushImage(ctx context.Context, image string, options PushImageOptions) error {
    // 检查是否为本地构建模式
    if options.LocalOnly {
        d.logger.Info("本地构建模式，跳过镜像推送", "image", image)
        return nil
    }
    // ... 原有推送逻辑
}
```

#### 构建执行器增强 (internal/cicd/executor.go)
```go
type BuildExecutorConfig struct {
    LocalBuildMode bool   `json:"local_build_mode"` // 本地构建模式
    WorkspaceDir   string `json:"workspace_dir"`    // 工作空间目录
    RegistryURL    string `json:"registry_url"`     // 镜像仓库地址
}

// 新增本地构建执行器创建函数
func NewLocalBuildExecutor(service Service, logger Logger, workspaceBase string) BuildExecutor
```

#### 服务初始化增强 (cmd/cicd-service/main.go)
```go
// 根据配置创建适当的构建执行器
func isLocalBuildMode() bool {
    // 检查配置文件名、环境变量等
    // 返回是否为本地构建模式
}
```

### 3. 启动脚本

#### scripts/start-local-dev.sh
- 完整的本地开发环境管理脚本
- 支持启动、停止、重启、清理等操作
- 自动检测和配置本地构建模式

#### scripts/test-local-build.sh
- 本地构建模式的测试脚本
- 验证构建功能是否正常工作

## 配置详解

### 本地构建模式检测

系统通过以下方式检测是否启用本地构建模式：

1. **配置文件名检测**: 包含 "local" 的配置文件
2. **配置项检测**: `docker.registry.mode: "local"`
3. **环境变量检测**: `BUILD_MODE=local` 或 `DOCKER_REGISTRY=local`

### CI/CD 流程调整

在本地构建模式下，CI/CD 流程的变化：

```yaml
# 构建阶段
- name: "Docker 构建"
  type: "docker_build"
  config:
    dockerfile: "Dockerfile"
    context: "."
    tags: ["app:latest"]

# 推送阶段（本地模式下跳过）
- name: "Docker 推送"
  type: "docker_push"
  config:
    tags: ["app:latest"]
    # 本地模式下此步骤会被跳过
```

## 使用指南

### 1. 快速启动

```bash
# 启动基础开发环境
./scripts/start-local-dev.sh start

# 启动完整环境（包括 Gitea）
./scripts/start-local-dev.sh start --full

# 后台启动
./scripts/start-local-dev.sh start --detach
```

### 2. 服务访问

| 服务 | 地址 | 说明 |
|------|------|------|
| API Gateway | http://localhost:8080 | 主要 API 入口 |
| CI/CD Service | http://localhost:8082 | 持续集成服务 |
| User Service | http://localhost:8083 | 用户认证服务 |
| PostgreSQL | localhost:5432 | 数据库服务 |
| Redis | localhost:6379 | 缓存服务 |

### 3. 开发工作流

```bash
# 修改代码后重新构建服务
docker-compose -f docker-compose.local.yml build api-gateway

# 重启服务
docker-compose -f docker-compose.local.yml restart api-gateway

# 查看日志
./scripts/start-local-dev.sh logs api-gateway

# 进入容器调试
./scripts/start-local-dev.sh shell api-gateway
```

### 4. 测试验证

```bash
# 运行本地构建测试
./scripts/test-local-build.sh

# 检查服务状态
./scripts/start-local-dev.sh status
```

## 技术实现细节

### 1. 构建流程优化

- **缓存利用**: 启用 Docker 构建缓存，加速重复构建
- **并行构建**: 支持多个服务并行构建
- **资源限制**: 合理配置构建资源限制

### 2. 网络配置

```yaml
networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16  # 避免与默认网络冲突
```

### 3. 数据持久化

```yaml
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  # 移除了 registry_data 卷
```

## 迁移指南

### 从镜像仓库模式迁移到本地模式

1. **备份数据**: 备份重要的数据库和配置
2. **停止服务**: 停止现有的服务
3. **切换配置**: 使用本地配置文件
4. **重新启动**: 使用本地模式启动服务

```bash
# 停止现有服务
docker-compose -f docker-compose.dev.yml down

# 启动本地模式
./scripts/start-local-dev.sh start
```

### 从本地模式迁移到生产环境

1. **配置镜像仓库**: 设置生产环境的镜像仓库
2. **修改配置**: 使用生产环境配置
3. **构建推送**: 构建并推送镜像到仓库
4. **部署服务**: 在生产环境部署

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8080
   ```

2. **构建失败**
   ```bash
   # 清理构建缓存
   docker builder prune
   
   # 重新构建
   ./scripts/start-local-dev.sh build
   ```

3. **网络问题**
   ```bash
   # 检查网络连接
   docker network ls
   docker network inspect paas-network
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   export LOG_LEVEL=debug
   ./scripts/start-local-dev.sh restart
   ```

2. **查看构建日志**
   ```bash
   docker-compose -f docker-compose.local.yml logs --follow cicd-service
   ```

## 总结

通过实施本地构建模式，开发者可以在单台 Docker 服务器上高效地进行 PaaS 平台开发，无需复杂的镜像仓库配置。这种方案特别适合：

- **快速原型开发**: 减少环境配置时间
- **功能测试验证**: 快速迭代和测试
- **学习和实验**: 降低学习门槛
- **小团队开发**: 简化协作环境

当项目需要扩展到多环境部署时，可以轻松切换回使用镜像仓库的标准模式。
