# CI/CD 服务使用指南

> 本文档整合了原 README-CICD.md 的内容，提供完整的 CI/CD 服务使用说明。

# CI/CD 服务模块实现总结

## 项目概述

本项目成功实现了一个完整的 CI/CD 服务模块，作为 PaaS 平台的核心组件之一。该服务提供了从代码提交到应用部署的完整自动化流程，支持多种构建类型、部署策略和集成方式。

## 🚀 核心功能

### 1. 流水线管理
- **灵活配置**: 支持 YAML 格式的流水线配置
- **多阶段支持**: 支持构建、测试、部署等多个阶段
- **条件触发**: 支持基于分支、标签等条件的自动触发
- **版本控制**: 流水线配置支持版本管理和回滚

### 2. 构建执行
- **多步骤类型**: 支持 Git 检出、Shell 命令、Docker 构建等多种步骤
- **并行执行**: 支持阶段内步骤的并行执行
- **资源管理**: 支持构建资源的限制和监控
- **缓存机制**: 支持依赖缓存，提高构建效率

### 3. 部署管理
- **多环境支持**: 支持开发、测试、生产等多环境部署
- **部署策略**: 支持滚动更新、蓝绿部署、金丝雀部署
- **回滚机制**: 支持一键回滚到上一个稳定版本
- **健康检查**: 部署过程中的自动健康检查

### 4. 实时监控
- **日志流**: 实时构建日志推送，支持 WebSocket 连接
- **状态跟踪**: 实时构建和部署状态更新
- **指标收集**: 集成 Prometheus 指标收集
- **告警通知**: 支持邮件、Slack、企业微信等通知方式

### 5. 集成能力
- **Git 集成**: 支持 Gitea、GitHub、GitLab 的 Webhook 集成
- **容器化**: 基于 Docker 的构建和部署
- **Kubernetes**: 原生支持 Kubernetes 集群部署
- **API 接口**: 完整的 RESTful API 和 WebSocket 接口

## 🏗️ 技术架构

### 核心技术栈
- **后端语言**: Go 1.21+
- **Web 框架**: Gin
- **数据库**: PostgreSQL (主要) / SQLite (开发)
- **缓存**: Redis
- **容器**: Docker
- **编排**: Kubernetes
- **监控**: Prometheus + Grafana

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI/CLI    │    │   API Gateway   │    │   Load Balancer │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  CI/CD Service  │    │  Build Nodes    │    │  Deploy Agents  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │   File Storage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
paas-platform/
├── cmd/cicd-service/           # 服务入口程序
│   └── main.go                 # 主程序入口
├── internal/cicd/              # 核心业务逻辑
│   ├── models.go               # 数据模型定义
│   ├── dto.go                  # 数据传输对象
│   ├── service.go              # 业务服务层
│   ├── handler.go              # HTTP 处理器
│   ├── executor.go             # 构建执行引擎
│   ├── scheduler.go            # 构建调度器
│   ├── docker.go               # Docker 客户端
│   ├── webhook.go              # Webhook 处理
│   ├── logger.go               # 日志管理
│   └── service_test.go         # 单元测试
├── configs/                    # 配置文件
│   └── cicd-service.yaml       # 服务配置
├── deployments/                # 部署配置
│   ├── docker/                 # Docker 部署
│   │   ├── Dockerfile          # 镜像构建文件
│   │   └── docker-compose.yml  # 容器编排
│   └── kubernetes/             # Kubernetes 部署
│       └── cicd-service.yaml   # K8s 资源定义
├── docs/                       # 文档
│   ├── cicd-service-api.md     # API 文档
│   └── cicd-service-guide.md   # 使用指南
└── scripts/                    # 脚本
    └── deploy.sh               # 部署脚本
```

## 🔧 主要组件

### 1. 服务层 (Service Layer)
- **CICDService**: 核心业务逻辑实现
- **流水线管理**: 创建、更新、删除、查询流水线
- **构建管理**: 触发、监控、取消构建任务
- **部署管理**: 执行、监控、回滚部署任务

### 2. 执行引擎 (Execution Engine)
- **BuildExecutor**: 构建任务执行器
- **多步骤支持**: Git 检出、Shell 命令、Docker 操作等
- **并发控制**: 支持并行和串行执行
- **错误处理**: 完善的错误处理和重试机制

### 3. 调度系统 (Scheduler)
- **BuildScheduler**: 构建任务调度器
- **队列管理**: 优先级队列和负载均衡
- **节点管理**: 构建节点注册和健康检查
- **资源分配**: 智能资源分配和调度

### 4. 日志系统 (Logging System)
- **BuildLogger**: 构建日志管理器
- **实时推送**: WebSocket 实时日志流
- **持久化**: Redis + 数据库双重存储
- **查询过滤**: 支持多维度日志查询

### 5. 集成组件 (Integration Components)
- **WebhookHandler**: Git 仓库 Webhook 处理
- **DockerClient**: Docker 操作客户端
- **通知系统**: 多渠道消息通知

## 📊 功能特性

### 流水线配置示例
```yaml
apiVersion: v1
kind: Pipeline
metadata:
  name: nodejs-app-pipeline
  description: Node.js 应用 CI/CD 流水线
spec:
  triggers:
    - type: push
      branches: [main, develop]
  stages:
    - name: 构建
      steps:
        - name: 检出代码
          type: git_checkout
        - name: 安装依赖
          type: shell
          config:
            commands: [npm ci]
        - name: 运行测试
          type: shell
          config:
            commands: [npm test]
        - name: 构建镜像
          type: docker_build
          config:
            dockerfile: Dockerfile
            tags: [app:${BUILD_NUMBER}]
    - name: 部署
      steps:
        - name: 推送镜像
          type: docker_push
        - name: 部署应用
          type: deploy
          config:
            environment: production
            strategy: rolling
```

### API 接口
- **流水线管理**: CRUD 操作
- **构建管理**: 触发、监控、日志查询
- **部署管理**: 执行、状态查询、回滚
- **实时通信**: WebSocket 日志流
- **统计信息**: 构建统计、节点状态

## 🧪 测试覆盖

### 单元测试
- **服务层测试**: 业务逻辑测试
- **数据模型测试**: 验证和序列化测试
- **API 测试**: HTTP 接口测试
- **集成测试**: 组件集成测试

### 测试结果
```
=== RUN   TestCICDService_CreatePipeline
--- PASS: TestCICDService_CreatePipeline (0.00s)
=== RUN   TestCICDService_CreateBuild
--- PASS: TestCICDService_CreateBuild (0.00s)
=== RUN   TestCICDService_UpdateBuildStatus
--- PASS: TestCICDService_UpdateBuildStatus (0.00s)
=== RUN   TestCICDService_GetBuild
--- PASS: TestCICDService_GetBuild (0.00s)
=== RUN   TestCICDService_ListBuilds
--- PASS: TestCICDService_ListBuilds (0.00s)
=== RUN   TestCICDService_CancelBuild
--- PASS: TestCICDService_CancelBuild (0.00s)
=== RUN   TestPipelineConfig_Validate
--- PASS: TestPipelineConfig_Validate (0.00s)
=== RUN   TestCreateBuildRequest_Validate
--- PASS: TestCreateBuildRequest_Validate (0.00s)
PASS
ok      paas-platform/internal/cicd    0.021s
```

## 🚀 部署方式

### 1. 本地开发
```bash
# 启动依赖服务
docker-compose -f deployments/docker/docker-compose.yml up -d postgres redis

# 运行服务
go run cmd/cicd-service/main.go
```

### 2. Docker 部署
```bash
# 使用部署脚本
./scripts/deploy.sh docker -e dev

# 或手动部署
cd deployments/docker
docker-compose up -d
```

### 3. Kubernetes 部署
```bash
# 使用部署脚本
./scripts/deploy.sh k8s -e prod -t v1.0.0

# 或手动部署
kubectl apply -f deployments/kubernetes/cicd-service.yaml
```

## 📈 性能指标

### 系统容量
- **并发构建**: 支持 10+ 并发构建任务
- **节点扩展**: 支持动态添加构建节点
- **数据存储**: 支持 TB 级构建数据存储
- **日志处理**: 支持实时日志流处理

### 性能优化
- **缓存机制**: 依赖缓存和构建缓存
- **并行执行**: 阶段内步骤并行执行
- **资源复用**: 构建环境复用
- **增量构建**: 支持增量构建优化

## 🔒 安全特性

### 认证授权
- **JWT 认证**: 基于 JWT 的用户认证
- **RBAC 权限**: 基于角色的访问控制
- **API 限流**: 防止 API 滥用
- **审计日志**: 完整的操作审计

### 数据安全
- **敏感信息**: 密钥和凭证加密存储
- **网络隔离**: 构建环境网络隔离
- **镜像安全**: 容器镜像安全扫描
- **传输加密**: HTTPS/TLS 传输加密

## 📚 文档资源

### 用户文档
- **API 文档**: 完整的 RESTful API 文档
- **使用指南**: 详细的功能使用说明
- **配置参考**: 配置文件参数说明
- **故障排除**: 常见问题解决方案

### 开发文档
- **架构设计**: 系统架构和设计思路
- **代码规范**: Go 代码规范和最佳实践
- **扩展开发**: 插件和扩展开发指南
- **贡献指南**: 开源贡献流程

## 🎯 未来规划

### 短期目标
- **插件系统**: 支持自定义构建步骤插件
- **多云支持**: 支持多云环境部署
- **可视化**: Web UI 界面开发
- **性能优化**: 进一步性能调优

### 长期目标
- **AI 集成**: 智能构建优化和故障诊断
- **GitOps**: 完整的 GitOps 工作流支持
- **多租户**: 企业级多租户支持
- **生态集成**: 与更多开发工具集成

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系：

- **Issue 提交**: 在 Git 仓库提交 Issue
- **文档反馈**: 对文档内容提出改进建议
- **功能请求**: 提出新功能需求
- **Bug 报告**: 报告发现的问题和 Bug

---

**版本**: v1.0.0  
**完成时间**: 2024-01-15  
**开发团队**: PaaS 平台开发团队  
**技术栈**: Go + PostgreSQL + Redis + Docker + Kubernetes
