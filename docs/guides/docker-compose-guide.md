# Docker Compose 配置文件使用指南

## 📋 配置文件概览

PaaS平台提供了多个Docker Compose配置文件，每个文件针对不同的使用场景进行了优化。本指南将帮助您选择和使用合适的配置文件。

## 🔧 主要配置文件

### 开发环境配置

#### `docker-compose.dev.yml` - 标准开发环境
**适用场景：**
- 日常开发工作
- 团队协作开发
- 需要完整开发工具链的场景

**特点：**
- 包含完整的开发服务栈
- 支持热重载和调试
- 包含开发工具和调试端口
- 优化了开发体验

**启动命令：**
```bash
docker-compose -f docker-compose.dev.yml up -d
```

#### `docker-compose.local.yml` - 本地开发环境
**适用场景：**
- 单台服务器开发
- 资源受限的开发环境
- 不需要镜像仓库的场景

**特点：**
- 专为本地开发优化
- 无需外部镜像仓库
- 容器名包含 `-local` 后缀，避免冲突
- 轻量化配置

**启动命令：**
```bash
docker-compose -f docker-compose.local.yml up -d
```

### 专用功能配置

#### `docker-compose.flexible.yml` - 灵活配置
**适用场景：**
- 需要自定义服务组合
- 测试特定功能模块
- 按需启动服务

**特点：**
- 支持 profiles 选择性启动
- 模块化服务配置
- 灵活的服务组合

**启动命令：**
```bash
# 启动基础服务
docker-compose -f docker-compose.flexible.yml up -d

# 启动完整环境
docker-compose -f docker-compose.flexible.yml --profile full up -d
```

#### `docker-compose.logging.yml` - 日志系统
**适用场景：**
- 需要集中日志管理
- 日志分析和监控
- ELK 栈部署

**特点：**
- 包含 Elasticsearch、Logstash、Kibana
- 日志聚合和分析
- 日志可视化界面

**启动命令：**
```bash
# 启动日志系统
docker-compose -f docker-compose.logging.yml up -d

# 与开发环境组合使用
docker-compose -f docker-compose.dev.yml -f docker-compose.logging.yml up -d
```

#### `docker-compose.monitoring.yml` - 监控系统
**适用场景：**
- 性能监控和告警
- 系统指标收集
- 监控仪表板

**特点：**
- 包含 Prometheus、Grafana、AlertManager
- 完整的监控告警栈
- 预配置的监控面板

**启动命令：**
```bash
# 启动监控系统
docker-compose -f docker-compose.monitoring.yml up -d

# 与开发环境组合使用
docker-compose -f docker-compose.dev.yml -f docker-compose.monitoring.yml up -d
```

### 前端配置

#### `web/docker-compose.yml` - 前端独立部署
**适用场景：**
- 前端独立开发
- 前端服务单独部署
- 前端性能测试

**特点：**
- 仅包含前端服务
- 独立的前端开发环境
- 支持前端热重载

**启动命令：**
```bash
cd web
docker-compose up -d
```

## 🚀 常用组合使用方法

### 完整开发环境
```bash
# 启动开发环境 + 监控 + 日志
docker-compose \
  -f docker-compose.dev.yml \
  -f docker-compose.monitoring.yml \
  -f docker-compose.logging.yml \
  up -d
```

### 轻量开发环境
```bash
# 仅启动核心服务
docker-compose -f docker-compose.local.yml up -d
```

### 测试环境
```bash
# 启动灵活配置的测试环境
docker-compose -f docker-compose.flexible.yml --profile test up -d
```

### 生产模拟环境
```bash
# 启动完整的生产模拟环境
docker-compose \
  -f docker-compose.flexible.yml \
  -f docker-compose.monitoring.yml \
  --profile full \
  up -d
```

## 📊 配置文件对比

| 配置文件 | 服务数量 | 资源占用 | 启动时间 | 适用场景 |
|----------|----------|----------|----------|----------|
| dev | 完整 | 高 | 中等 | 日常开发 |
| local | 精简 | 低 | 快速 | 本地开发 |
| flexible | 可选 | 可变 | 可变 | 灵活配置 |
| logging | 专用 | 中等 | 中等 | 日志分析 |
| monitoring | 专用 | 中等 | 中等 | 性能监控 |

## 🔧 环境变量配置

### 通用环境变量
```bash
# 数据库配置
POSTGRES_DB=paas
POSTGRES_USER=paas
POSTGRES_PASSWORD=paas123

# Redis配置
REDIS_PASSWORD=redis123

# 应用配置
APP_ENV=development
LOG_LEVEL=debug
```

### 开发环境特定变量
```bash
# 开发环境
DOCKER_REGISTRY=localhost:5000
DEBUG_MODE=true
HOT_RELOAD=true
```

### 本地环境特定变量
```bash
# 本地环境
DOCKER_REGISTRY=local
COMPOSE_SUFFIX=-local
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口冲突
**问题：** 启动时提示端口已被占用
**解决：** 
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 停止冲突的服务
docker-compose down

# 使用不同的配置文件
docker-compose -f docker-compose.local.yml up -d
```

#### 2. 容器名冲突
**问题：** 容器名已存在
**解决：**
```bash
# 停止现有容器
docker stop $(docker ps -q)

# 清理容器
docker-compose down --remove-orphans

# 使用本地配置避免冲突
docker-compose -f docker-compose.local.yml up -d
```

#### 3. 资源不足
**问题：** 内存或磁盘空间不足
**解决：**
```bash
# 使用轻量配置
docker-compose -f docker-compose.local.yml up -d

# 清理未使用的资源
docker system prune -f
```

## 📝 最佳实践

### 1. 选择合适的配置文件
- **日常开发**: 使用 `docker-compose.dev.yml`
- **资源受限**: 使用 `docker-compose.local.yml`
- **功能测试**: 使用 `docker-compose.flexible.yml`
- **性能测试**: 组合使用监控配置

### 2. 环境隔离
- 使用不同的容器名后缀
- 配置独立的数据卷
- 使用环境变量区分配置

### 3. 资源管理
- 定期清理未使用的容器和镜像
- 监控资源使用情况
- 根据需要调整服务配置

### 4. 安全考虑
- 不在生产环境使用开发配置
- 定期更新密码和密钥
- 限制网络访问权限

## 🔗 相关文档

- [开发环境认证配置指南](development-auth-guide.md)
- [CI/CD 服务使用指南](cicd-service-guide.md)
- [身份提供商集成指南](idp-integration-guide.md)

---

**文档版本**: 1.0  
**最后更新**: 2025年8月  
**维护团队**: PaaS平台开发团队
