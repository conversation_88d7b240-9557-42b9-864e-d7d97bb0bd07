# 身份提供商集成指南

> 本文档整合了原 README-IDP.md 的内容，提供身份提供商集成的详细说明。
# PaaS 平台 IDP 优先认证集成

## 🎯 项目概述

本项目为 PaaS 平台实现了完整的 Identity Provider (IDP) 优先认证解决方案，将 IDP 认证设为主要认证方式，普通用户仅支持 IDP 认证，系统管理员保留本地认证能力。

### ✨ 核心特性

- **🔐 IDP 优先认证**：普通用户仅支持 IDP 认证，提供企业级身份管理
- **👨‍💼 管理员本地认证**：系统管理员保留本地认证，确保系统可管理性
- **🌐 多协议支持**：支持 OIDC、OAuth2、SAML、LDAP 等主流认证协议
- **🔗 灵活账号关联**：支持本地账号与 IDP 账号的多种关联方式
- **👥 自动用户管理**：从 IDP 自动创建用户，无需手动注册
- **🎨 双重登录界面**：企业用户 IDP 登录 + 管理员专用登录
- **📊 完整审计日志**：记录所有认证和操作事件
- **🏢 多租户支持**：每个租户可独立配置 IDP 提供商
- **🔒 企业级安全**：令牌加密存储、会话管理、权限控制

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API 网关      │    │   用户服务      │
│                 │    │                 │    │                 │
│ • Vue.js        │◄──►│ • 混合认证中间件│◄──►│ • IDP 服务      │
│ • IDP 登录界面  │    │ • JWT 验证      │    │ • 账号关联      │
│ • 回调处理      │    │ • 路由转发      │    │ • 用户同步      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                        ▲
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   外部 IDP      │    │   数据存储      │    │   监控审计      │
│                 │    │                 │    │                 │
│ • Azure AD      │    │ • 用户数据库    │    │ • 审计日志      │
│ • Google        │    │ • IDP 配置      │    │ • 性能监控      │
│ • 企业 SAML     │    │ • 会话存储      │    │ • 告警通知      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd paas

# 安装依赖
go mod download

# 设置环境变量
export DB_HOST="localhost"
export DB_USER="paas_user"
export DB_PASSWORD="your_password"
export DB_NAME="paas_db"
```

### 2. 数据库初始化

```bash
# 备份现有数据库（重要！）
mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql

# 执行 IDP 数据库迁移
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < scripts/migrate-idp-tables.sql
```

### 3. 配置 IDP 提供商

```bash
# 复制配置模板
cp configs/idp-providers.example.yaml configs/idp-providers.yaml

# 编辑配置文件，添加您的 IDP 提供商信息
vim configs/idp-providers.yaml
```

### 4. 一键部署

```bash
# 执行自动化部署脚本
./scripts/deploy-idp.sh
```

### 5. 验证部署

```bash
# 检查服务状态
curl http://localhost:8083/health
curl http://localhost:8080/health

# 查看 IDP 提供商列表
curl http://localhost:8080/api/v1/admin/idp/providers
```

## 📋 配置指南

### IDP 提供商配置示例

#### Azure AD (OIDC)
```yaml
- name: "azure-ad"
  type: "oidc"
  enabled: true
  config:
    client_id: "your-azure-client-id"
    client_secret: "your-azure-client-secret"
    issuer: "https://login.microsoftonline.com/{tenant-id}/v2.0"
    scopes: ["openid", "profile", "email"]
```

#### Google OAuth2
```yaml
- name: "google"
  type: "oauth2"
  enabled: true
  config:
    client_id: "your-google-client-id"
    client_secret: "your-google-client-secret"
    auth_url: "https://accounts.google.com/o/oauth2/v2/auth"
    token_url: "https://oauth2.googleapis.com/token"
```

### 环境变量配置

```bash
# IDP 功能开关
export PAAS_IDP_ENABLED=true
export PAAS_IDP_ALLOW_LOCAL_AUTH=true

# 安全配置
export JWT_SECRET="your-jwt-secret-key"
export IDP_ENCRYPTION_KEY="your-idp-encryption-key"

# 数据库配置
export DB_HOST="your-db-host"
export DB_USER="your-db-user"
export DB_PASSWORD="your-db-password"
```

## 🔧 开发指南

### 项目结构

```
paas/
├── cmd/
│   ├── user-service/          # 用户服务入口
│   └── api-gateway/           # API 网关入口
├── internal/
│   └── auth/                  # 认证模块
│       ├── idp_service.go     # IDP 服务实现
│       ├── idp_handler.go     # IDP HTTP 处理器
│       ├── idp_middleware.go  # IDP 认证中间件
│       └── models.go          # 数据模型
├── configs/
│   ├── user-service.yaml      # 用户服务配置
│   ├── api-gateway.yaml       # API 网关配置
│   └── idp-providers.yaml     # IDP 提供商配置
├── scripts/
│   ├── deploy-idp.sh          # 部署脚本
│   ├── stop-services.sh       # 停止服务脚本
│   └── migrate-idp-tables.sql # 数据库迁移脚本
├── web/
│   └── src/
│       ├── views/auth/        # 认证相关页面
│       └── api/auth.ts        # 认证 API 接口
├── tests/
│   └── integration/           # 集成测试
└── docs/
    └── idp-integration-guide.md # 详细文档
```

### 本地开发

```bash
# 启动开发环境
go run cmd/user-service/main.go --config=configs/user-service.yaml &
go run cmd/api-gateway/main.go --config=configs/api-gateway.yaml &

# 启动前端开发服务器
cd web
npm run dev
```

### 运行测试

```bash
# 单元测试
go test ./internal/auth -v

# 集成测试
go test ./tests/integration -v

# 前端测试
cd web && npm test
```

## 🔌 API 接口

### 认证相关

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/idp/login/{provider}` | 发起 IDP 登录 |
| POST | `/api/v1/idp/callback/{provider}` | 处理 IDP 回调 |
| POST | `/api/v1/idp/link` | 关联 IDP 账号 |
| GET | `/api/v1/idp/accounts` | 获取用户 IDP 账号 |

### 管理相关

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/admin/idp/providers` | 创建 IDP 提供商 |
| GET | `/api/v1/admin/idp/providers` | 获取 IDP 提供商列表 |
| PUT | `/api/v1/admin/idp/providers/{id}` | 更新 IDP 提供商 |
| DELETE | `/api/v1/admin/idp/providers/{id}` | 删除 IDP 提供商 |

## 🚨 故障排除

### 常见问题

1. **IDP 认证失败**
   - 检查 IDP 配置是否正确
   - 验证客户端 ID 和密钥
   - 确认回调 URL 配置

2. **用户信息同步失败**
   - 检查属性映射配置
   - 验证 IDP 返回的数据格式
   - 查看同步日志

3. **令牌验证失败**
   - 检查令牌是否过期
   - 验证中间件配置
   - 确认缓存设置

### 日志查看

```bash
# 查看服务日志
tail -f logs/user-service.log
tail -f logs/api-gateway.log

# 查看 IDP 审计日志
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT * FROM idp_audit_logs ORDER BY timestamp DESC LIMIT 10;"
```

## 🔒 安全注意事项

1. **敏感信息保护**
   - 使用环境变量存储客户端密钥
   - 启用令牌加密存储
   - 定期轮换密钥

2. **网络安全**
   - 强制使用 HTTPS
   - 配置防火墙规则
   - 启用 CORS 保护

3. **访问控制**
   - 实施最小权限原则
   - 定期审查用户权限
   - 监控异常登录

## 📚 相关文档

- [详细集成指南](docs/idp-integration-guide.md)
- [API 接口文档](docs/api-reference.md)
- [部署运维指南](docs/deployment-guide.md)
- [安全最佳实践](docs/security-guide.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或需要支持，请：

1. 查看 [FAQ](docs/faq.md)
2. 搜索 [Issues](../../issues)
3. 创建新的 Issue
4. 联系开发团队

---

**注意**：在生产环境部署前，请仔细阅读安全指南并进行充分测试。
