# PaaS 平台配置指南

## 配置设计理念

### 为什么统一使用 YAML 配置？

我们采用统一的 YAML 配置文件设计，避免了 `.env` 和 `YAML` 混合使用的复杂性：

**优势：**
- **统一格式**: 所有配置都使用 YAML，便于管理和维护
- **结构化**: 支持嵌套配置，表达复杂的配置关系
- **可读性**: YAML 格式直观，便于理解和修改
- **环境变量覆盖**: 支持通过环境变量覆盖任何配置项
- **可选组件**: 通过 `enabled` 开关控制组件启用/禁用

## 配置模式

### 1. 最小化模式 (`app.minimal.yaml`)

适用于：单机开发、快速原型、资源受限环境

**特点：**
- 仅启用核心服务（API Gateway、User Service、App Manager）
- 使用 SQLite 数据库，无需 PostgreSQL
- 禁用 Redis，使用内存缓存
- 禁用 CI/CD、监控等扩展功能
- 最小资源占用

**启动方式：**
```bash
# 方式1：使用参数
./scripts/start-local-dev.sh start --minimal

# 方式2：直接指定配置文件
./scripts/start-local-dev.sh start -c configs/app.minimal.yaml
```

**配置示例：**
```yaml
# 数据库配置 - 使用 SQLite
database:
  enabled: true
  driver: "sqlite"
  dsn: "./data/paas.db"

# Redis 配置 - 禁用
redis:
  enabled: false

# 功能开关 - 仅核心功能
features:
  app_management: true
  user_management: true
  config_management: true
  cicd: false
  monitoring: false
```

### 2. 标准模式 (`app.standard.yaml`)

适用于：完整开发环境、团队协作、功能测试

**特点：**
- 启用 PostgreSQL 数据库
- 启用 Redis 缓存
- 包含 CI/CD 服务
- 启用监控和日志聚合
- 支持 Git 集成

**启动方式：**
```bash
# 方式1：使用参数
./scripts/start-local-dev.sh start --standard

# 方式2：直接指定配置文件
./scripts/start-local-dev.sh start -c configs/app.standard.yaml
```

**配置示例：**
```yaml
# 数据库配置 - 使用 PostgreSQL
database:
  enabled: true
  driver: "postgres"
  dsn: "host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable"

# Redis 配置 - 启用
redis:
  enabled: true
  addr: "redis:6379"

# 功能开关 - 包含扩展功能
features:
  app_management: true
  user_management: true
  config_management: true
  cicd: true
  monitoring: true
```

### 3. 完整模式 (`--full`)

适用于：完整功能验证、生产环境模拟

**特点：**
- 包含所有服务组件
- 启用 Gitea 代码仓库
- 启用 Docker Registry
- 完整的监控和日志系统

**启动方式：**
```bash
./scripts/start-local-dev.sh start --full
```

## 可选组件配置

### Redis 配置

**启用 Redis：**
```yaml
redis:
  enabled: true
  addr: "redis:6379"
  password: ""
  db: 0
```

**禁用 Redis（使用内存缓存）：**
```yaml
redis:
  enabled: false

# 内存缓存配置
resources:
  memory_cache:
    max_size: "100MB"
    ttl: "1h"
```

### 数据库配置

**使用 SQLite（最小化）：**
```yaml
database:
  enabled: true
  driver: "sqlite"
  dsn: "./data/paas.db"
  auto_migrate: true
```

**使用 PostgreSQL（标准）：**
```yaml
database:
  enabled: true
  driver: "postgres"
  dsn: "host=postgres user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable"
  max_open_conns: 25
  max_idle_conns: 10
```

### CI/CD 配置

**启用 CI/CD：**
```yaml
features:
  cicd: true

cicd:
  enabled: true
  build:
    workspace_dir: "/tmp/builds"
    max_concurrent_builds: 5
    default_timeout: 1800s
```

**禁用 CI/CD：**
```yaml
features:
  cicd: false

cicd:
  enabled: false
```

### 监控配置

**启用监控：**
```yaml
monitoring:
  enabled: true
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
  health_check:
    enabled: true
    interval: 30s
```

**禁用监控：**
```yaml
monitoring:
  enabled: false
```

## 环境变量覆盖

所有配置项都可以通过环境变量覆盖，格式为 `PAAS_<配置路径>`：

```bash
# 覆盖数据库配置
export PAAS_DATABASE_ENABLED=true
export PAAS_DATABASE_DRIVER=postgres
export PAAS_DATABASE_DSN="host=localhost user=paas password=paas123 dbname=paas_platform"

# 覆盖 Redis 配置
export PAAS_REDIS_ENABLED=true
export PAAS_REDIS_ADDR="localhost:6379"

# 覆盖日志级别
export PAAS_LOGGING_LEVEL=debug

# 启动服务
./scripts/start-local-dev.sh start
```

## 使用场景示例

### 场景1：快速开发测试

```bash
# 最小化环境，快速启动
./scripts/start-local-dev.sh start --minimal

# 访问服务
curl http://localhost:8080/health
curl http://localhost:8083/api/v1/users
```

### 场景2：完整功能开发

```bash
# 标准环境，包含数据库和缓存
./scripts/start-local-dev.sh start --standard

# 等待服务启动
sleep 30

# 访问各个服务
curl http://localhost:8080/health      # API Gateway
curl http://localhost:8082/health      # CI/CD Service
curl http://localhost:5432             # PostgreSQL
```

### 场景3：自定义配置

```bash
# 创建自定义配置文件
cp configs/app.minimal.yaml configs/app.custom.yaml

# 修改配置（启用 Redis 但禁用 CI/CD）
vim configs/app.custom.yaml

# 使用自定义配置启动
./scripts/start-local-dev.sh start -c configs/app.custom.yaml
```

### 场景4：环境变量动态配置

```bash
# 设置环境变量
export PAAS_REDIS_ENABLED=true
export PAAS_REDIS_ADDR="localhost:6379"
export PAAS_LOGGING_LEVEL=debug

# 启动最小化环境但启用 Redis
./scripts/start-local-dev.sh start --minimal
```

## 配置验证

系统会自动验证配置的有效性：

```bash
# 启动时会显示配置验证结果
./scripts/start-local-dev.sh start --standard

# 输出示例：
# [信息] 使用配置模式: standard
# [信息] 配置文件: configs/app.standard.yaml
# [信息] 启用的服务组: database,redis,cicd,config
# [成功] 配置验证通过
```

## 故障排除

### 常见配置问题

1. **端口冲突**
   ```yaml
   server:
     port: 8080  # 修改为其他端口
   ```

2. **数据库连接失败**
   ```yaml
   database:
     enabled: false  # 临时禁用数据库
   ```

3. **Redis 连接失败**
   ```yaml
   redis:
     enabled: false  # 禁用 Redis，使用内存缓存
   ```

### 配置调试

```bash
# 启用详细输出
./scripts/start-local-dev.sh start --minimal --verbose

# 查看配置文件使用情况
./scripts/start-local-dev.sh start --minimal 2>&1 | grep "配置文件"

# 检查环境变量
env | grep PAAS_
```

## 总结

通过统一的 YAML 配置和智能的服务组合，您可以：

1. **按需启动**: 根据开发需要选择最小化、标准或完整模式
2. **灵活配置**: 通过配置文件或环境变量灵活调整
3. **资源优化**: 避免启动不需要的服务，节省资源
4. **快速切换**: 在不同配置模式间快速切换

这种设计既简化了配置管理，又提供了足够的灵活性来满足不同的开发需求。
